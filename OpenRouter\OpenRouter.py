import os
import json
from typing import Dict, Any
from dotenv import load_dotenv
from prompt import PROMPT_EXAMPLE_GEMINI
import httpx
from asyncio import get_event_loop
from openai import OpenAI
load_dotenv()

OPEN_ROUTER_DEEP_SEEK_API_KEY = os.getenv('OPEN_ROUTER_DEEP_SEEK_API_KEY')


def clear_text(json_string)->Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        import json
        if not json_string: 
            return {}
        
        json_string = json_string.strip()
        try:

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            json_string = json.loads(json_string)
            return json_string
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")
    return {}


def extract_data_by_deepseek_r1(content, prompt=PROMPT_EXAMPLE_GEMINI)->Dict[str, Any]:
    client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=OPEN_ROUTER_DEEP_SEEK_API_KEY,
    )    
    completion = client.chat.completions.create(
        extra_body={},
        model="deepseek/deepseek-r1:free",
        messages=[
            {
            "role": "user",
            "content": f"{prompt}\n\nТекст для обработки:\n{content}"
            }
        ]
        )
    result = clear_text(completion.choices[0].message.content)
    return result
        

async def extract_data_by_deepseek_r1_async(content: str, prompt = PROMPT_EXAMPLE_GEMINI) -> Dict[str, Any]:
    from openai import AsyncOpenAI
    # Используем async with для корректного управления ресурсами клиента
    async with AsyncOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=OPEN_ROUTER_DEEP_SEEK_API_KEY,
    ) as client:
        content = f"{prompt}\n\nЗАДАЧА(OCR-текст): {content}"
        completion = await client.chat.completions.create(
            # extra_body={}, # Обычно не требуется для стандартных вызовов
            model="deepseek/deepseek-r1:free", # Убедитесь, что это правильный идентификатор модели для OpenRouter
                                          # Изначально было deepseek/deepseek-r1:free, но deepseek-chat более распространен
                                          # Если deepseek-r1:free - ваш рабочий вариант, верните его.
            messages=[
                {
                    "role": "user",
                    "content": content
                }
            ]
        )
    
    # Функция clear_text остается синхронной, если она не выполняет I/O операций.
    # Если clear_text выполняет длительные CPU-bound операции, ее можно запустить
    # в отдельном потоке с помощью asyncio.to_thread (Python 3.9+)
    # или loop.run_in_executor.
    # В данном случае, если это просто парсинг текста, то все в порядке.
    result_text = completion.choices[0].message.content
    if result_text is None:
        return {"error": "Model returned no content"}
        
    result = clear_text(result_text)
    return result


if __name__ == "__main__":
    tetx = """
        Короб 56
        EURO піддон 0
        Нестандартний 0
        Всього 56
    """

    loop = get_event_loop()
    result_async = loop.run_until_complete(extract_data_by_deepseek_r1_async(tetx))
    print(f"Асинхронный результат: {result_async}")
    
    result = extract_data_by_deepseek_r1(tetx)
    print(f"Синхронный результат: {result}")
