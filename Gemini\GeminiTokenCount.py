import google.generativeai as genai
import os
from datetime import datetime
from dotenv import load_dotenv
load_dotenv()

# Настройте ваш API ключ (лучше через переменные окружения)
# genai.configure(api_key="YOUR_API_KEY")
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
genai.configure(api_key=GEMINI_API_KEY)


def get_token_count(response):
    """
    Подсчитывает и выводит информацию о токенах в ответе Gemini API.
    
    Args:
        response: Ответ от Gemini API
    """
    try:
        # print("\n=== Информация об использовании токенов ===")
        # print(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        if hasattr(response, 'usage_metadata'):
            metadata = response.usage_metadata
            print(metadata)
            # print(f"Токены в промпте: {metadata.prompt_token_count}")
            # print(f"Токены в ответе: {metadata.candidates_token_count}")
            # print(f"Токены кэшированного контента: {metadata.cached_content_token_count}")
            # print(f"Токены рассуждения: {metadata.thoughts_token_count if 'thoughts_token_count' in metadata else 0}")
            # print(f"Всего токенов: {metadata.total_token_count}\n\n")

            
    except Exception as e:
        print(f"Ошибка при подсчете токенов: {str(e)}")
        if hasattr(response, 'error'):
            print(f"Ошибка API: {response.error}")


if __name__ == "__main__":
    # Выберите модель - закомментировано, т.к. старая версия API
    model = genai.GenerativeModel('gemini-2.0-flash') # Используем актуальную Flash модель

    # Ваш промпт
    prompt_text = "Напиши приложение на Python, которое будет дообучать llM модель."

    # Отправка запроса
    response = model.generate_content(prompt_text)
    get_token_count(response)
