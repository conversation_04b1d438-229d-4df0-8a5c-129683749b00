import aiohttp
import asyncio
import json
import os
import sys
import subprocess
from dotenv import load_dotenv


load_dotenv()

# Проверка и установка необходимых модулей
required_modules = ["dotenv", "aiohttp", "asyncio"]
for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        print(f"Модуль '{module}' не найден. Устанавливаю...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", module])

# Загрузка переменных окружения

API_KEY = os.getenv('DEEPSEEK_API_KEY') 
if not API_KEY:
    raise ValueError("DEEPSEEK_API_KEY не найден в файле .env")

MODEL = "deepseek-chat"  # Или другая поддерживаемая модель
BASE_URL = "https://api.deepseek.com/v1"  # Базовый URL для API


# Извлечение информации с помощью DeepSeek API
async def extract_doc_type(content: str, prompt: str):
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "system",
                "content": prompt,
            },
            {"role": "user", "content": content},
        ],
        # "response_format": {"type": "json_object"}
        "response_format": {
            "doc_type": "string",
            "doc_number": "string",
            "doc_date": "date",
        },
    }
    return await extract_info(payload)


async def extract_info_from_document(content: str, prompt: str):
    payload = {
        "model": MODEL,
        "messages": [
            {
                "role": "system",
                "content": (
                    f"{prompt} Ответ должен быть в формате JSON. Если данных нет, верни null."
                ),
            },
            {
                "role": "user",
                "content": f"Текст документа: {content}",
            },
        ],
        "temperature": 0.1,
        "top_p": 0.2,
        "max_tokens": 8000,
        "response_format": {
            "type": "json_object",
            "properties": {
                "documents": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "doc_type": {"type": "string"},
                            "doc_number": {"type": "string"},
                            "doc_date": {
                                "type": "string",
                                "format": "date",
                                "description": "Дата документа в формате dd.mm.yyyy",
                            },
                            "buyer_name": {"type": "string"},
                            "buyer_code": {
                                "type": "string",
                                "format": "number",
                                "minLength": 8,
                                "maxLength": 8,
                            },
                        },
                    },
                    "required": [
                        "doc_type",
                        "doc_number",
                        "doc_date",
                        "buyer_name",
                        "buyer_code",
                    ],
                }
            },
        },
    }
    return await extract_info(payload)


async def extract_info(payload: dict):
    headers = {"Authorization": f"Bearer {API_KEY}", "Content-Type": "application/json"}

    try:
        async with aiohttp.ClientSession() as session:
            print(f"Отправка запроса к API DeepSeek...")
            async with session.post(
                f"{BASE_URL}/chat/completions",
                json=payload,
                headers=headers,
                timeout=60,
            ) as response:
                if response.status == 200:
                    result = await response.json()

                    if "choices" in result and len(result["choices"]) > 0:
                        if "message" in result["choices"][0]:
                            try:
                                json_content = json.loads(
                                    result["choices"][0]["message"]["content"],
                                    strict=False,
                                )
                                return json_content
                            except json.JSONDecodeError:
                                print(
                                    f"Ошибка декодирования JSON: {result['choices'][0]['message']['content']}"
                                )
                                return {
                                    "error": "Не удалось декодировать JSON",
                                    "raw_text": result["choices"][0]["message"][
                                        "content"
                                    ],
                                }

                    print(f"Неожиданный формат ответа: {result}")
                    return {"error": "Неожиданный формат ответа"}
                else:
                    error_text = await response.text()
                    print(f"Ошибка API: {response.status} - {error_text}")
                    return {
                        "error": f"API Error: {response.status}",
                        "details": error_text,
                    }
    except aiohttp.ClientPayloadError as e:
        print(f"Ошибка загрузки ответа: {str(e)}")
        return {"error": f"Ошибка загрузки: {str(e)}"}
    except asyncio.TimeoutError:
        print("Превышено время ожидания ответа от API")
        return {"error": "Timeout"}
    except Exception as e:
        print(f"Общая ошибка при запросе: {str(e)}")
        return {"error": f"Общая ошибка: {str(e)}"}


async def main():
    text = """
    Видаткова накладна № 15523 від 15 травня 2023 р.
    Товарно-транспортна накладна 777 від 17 червня 2027 р.
    """
    prompt = """
    Данные извлекаются OCR.
    Поэтому могут быть грамматические ошибки. Тебе дается текст на украинском языке.
    Извлеки данные по ВСЕМ документам на странице: расходным(видатковым) накладным и товарно-транспортным накладным.
    Признаки (ключевые слова) товарно-траснортнoй накладной: "транспорт", додаток7, вантаж, розвантаж.
    Ты специалист по обработке текста на украинском языке. И понимаешь о каком документе идет речь.
    Ты правильно определяешь номер и дата к чему относится. К самому документу или документам, которые там перечислены.
    Дата документа идет после "від".
    Дату выводи в формате: dd.mm.yyyy.
    """

    result = await extract_info(
        {
            "model": MODEL,
            "messages": [
                {
                    "role": "user",
                    "content": f"{prompt}\n\nОтвет должен быть в формате JSON.\n\nТекст документа: {text}",
                }
            ],
            "temperature": 0.1,
            "top_p": 0.1,
            "max_tokens": 8000,
            "response_format": {"type": "json_object"},
        }
    )

    print("\nРезультат:")
    print(json.dumps(result, ensure_ascii=False, indent=2))

    return result


if __name__ == "__main__":
    asyncio.run(main())
