# подключение к 1С для извлечения данных

import aiohttp
import asyncio
import json
from dotenv import dotenv_values
import urllib


# Загрузка переменных окружения
config = dotenv_values(".env")
LOGIN = config.get("LOGIN_1C", "")
PASSWORD = config.get("PASSWORD_1C", "")
HOST = config.get("PG_HOST", "")

if not LOGIN or not PASSWORD:
    raise ValueError("LOGIN_1C или PASSWORD_1C не найдены в файле .env")


def get_sales_date_url(buyer_code, doc_number):
    if not buyer_code or not doc_number:
        raise ValueError("buyer_code и doc_number не могут быть пустыми")

    base_url = "http://*************/utp_prestige/odata/standard.odata/Document_РеализацияТоваровУслуг"
    params = {
        "$format": "json",
        "$filter": f"substringof('{doc_number}',Number) and Контрагент/КодПоЕДРПОУ eq '{buyer_code}'",
        "$expand": "*",
        "$select": "Date",
        "$top": "1",
        "$orderby": "Date desc",
    }

    # Кодируем параметры — только значения
    encoded_params = urllib.parse.urlencode(params, safe="'()/$ ", encoding="utf-8")

    # Собираем итоговый URL
    final_url = f"{base_url}?{encoded_params}"

    return final_url


async def get_data_from_1c(url):
    headers = {"Accept": "application/json"}
    async with aiohttp.ClientSession(
        auth=aiohttp.BasicAuth(LOGIN, PASSWORD)
    ) as session:
        async with session.get(url, headers=headers) as response:
            if response.status == 200:
                date = await response.json()
                if date.get("value"):
                    return date["value"][0]["Date"]
    return None


if __name__ == "__main__":
    url = get_sales_date_url("32490244", "10276")
    print(asyncio.run(get_data_from_1c(url)))
