import google.generativeai as genai
import os
import sys
import json
from pathlib import Path
from dotenv import load_dotenv

# Загрузка переменных окружения из .env файла
load_dotenv()

# 1. Config & Checks (API Key, File Path)
if not (api_key := os.getenv('GEMINI_API_KEY')):
    print("ERROR: Set GEMINI_API_KEY env var.", file=sys.stderr)
    sys.exit(1)

genai.configure(api_key=api_key)

if len(sys.argv) < 2 or not (fp := Path(sys.argv[1])).is_file():
    print(f"ERROR: Usage: python {sys.argv[0]} <valid_file_path>", file=sys.stderr)
    sys.exit(1)

# 2. Prompt, Model, Config (!!! ИЗМЕНИТЕ ПРОМПТ ПРИ НЕОБХОДИМОСТИ !!!)
PROMPT = """Проанализируй приложенный текстовый файл.
Верни ТОЛЬКО JSON объект с ключами 'summary' (краткое содержание) и
'entities' (список ключевых сущностей). Без пояснений до/после JSON."""

# Настройка модели Gemini
model = genai.GenerativeModel('gemini-1.5-flash-latest')

# Конфигурация генерации ответа
config = genai.types.GenerationConfig(
    response_mime_type="application/json",
    temperature=0.1
)

# Ссылка на загруженный файл
uf = None

try:
    # 3. Загрузка файла и генерация ответа
    uf = genai.upload_file(path=str(fp))
    resp = model.generate_content(
        [PROMPT, uf],
        generation_config=config,
        request_options={'timeout': 600}
    )

    # 4. Вывод информации о токенах (в stderr)
    if hasattr(resp, 'usage_metadata'):
        m = resp.usage_metadata
        print(f"Tokens(stderr)>> In:{m.prompt_token_count}, Out:{m.candidates_token_count}, Total:{m.total_token_count}", file=sys.stderr)
    elif hasattr(resp, 'prompt_feedback') and resp.prompt_feedback.block_reason:
        print(f"Tokens N/A (Blocked)", file=sys.stderr)

    # 5. Проверка ответа, парсинг и вывод JSON (в stdout)
    if not resp.parts:
        raise ValueError(
            f"API Error/Blocked: {resp.prompt_feedback.block_reason if hasattr(resp, 'prompt_feedback') else 'Empty Response'}"
        )

    # Вывод отформатированного JSON
    print(json.dumps(json.loads(resp.text), ensure_ascii=False, indent=2))

except Exception as e:
    print(f"ERROR: {type(e).__name__} - {e}", file=sys.stderr)
    sys.exit(1)

finally:
    # Очистка - удаление загруженного файла
    if uf:
        try:
            genai.delete_file(uf.name)
        except:
            pass  # Игнорируем ошибки при очистке