import os
import logging
import re
import fitz  # PyMuPDF
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract

if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")


def preprocess_image(img: Image.Image) -> Image.Image:
    """Улучшает качество изображения перед OCR."""
    try:
        # Конвертация в grayscale
        img = img.convert("L")

        # Повышение контраста
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(2.0)  # показывает лишний контраст

        # Бинаризация (пороговая обработка)
        img = img.point(lambda x: 0 if x < 180 else 255)

        # Уменьшение шумов
        img = img.filter(ImageFilter.MedianFilter(size=3))

        return img
    except Exception as e:
        logging.error(f"Ошибка предобработки изображения: {e}")
        return img


def extract_text_from_page(
    page: fitz.Page,
    dpi: int = 800,
    languages: str = "ukr+rus+eng"
) -> str:
    """
    Улучшенное извлечение текста со страницы PDF.

    :param page: Страница PDF
    :param dpi: Разрешение для рендеринга
    :param languages: Языки для OCR
    :return: Обработанный текст
    """
    try:
        pix = page.get_pixmap(dpi=dpi)
        img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)
        processed_img = preprocess_image(img)

        whitelist = "0123456789,-№ІіЇїҐґЄєабвгґдеєжзиіїйклмнопрстуфхцчшщьюяАБВГҐДЕЄЖЗИІЇЙКЛМНОПРСТУФХЦЧШЩЬЮЯ"

        # Проверяем, установлен ли путь к Tesseract
        if not pytesseract.pytesseract.tesseract_cmd or not os.path.exists(pytesseract.pytesseract.tesseract_cmd):
            pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

        # full_config = f"--psm 6 --dpi {dpi} -c tessedit_char_whitelist={whitelist}"
        full_config = f"--psm 6 --dpi {dpi}"

        text = pytesseract.image_to_string(
            processed_img, lang=languages, config=full_config
        )

        text = re.sub(r"(?<=\d) (?=\d)", "", text)
        text = re.sub(r"[^\S\n]+", " ", text)
        text = re.sub(r"\n{3,}", "\n\n", text)

        print(text)
        return text.strip()

    except pytesseract.TesseractNotFoundError:
        print("Ошибка: Tesseract не найден. Проверьте установку и путь к исполняемому файлу.")
        return ""
    except Exception as e:
        print(f"Ошибка при извлечении текста: {str(e)}")
        return ""


def process_pdf(
    input_path: str, output_path: str, start_page: int = 0, end_page: int = None
) -> None:
    """
    Обработка PDF-документа.

    :param input_path: Входной файл PDF
    :param output_path: Выходной текстовый файл
    :param start_page: Начальная страница (0-based)
    :param end_page: Конечная страница
    """
    try:
        doc = fitz.open(input_path)
        end_page = end_page or len(doc)

        with open(output_path, "w", encoding="utf-8") as f:
            for pg_num in range(start_page, end_page):
                page = doc.load_page(pg_num)
                text = extract_text_from_page(page)
                f.write(f"=== Страница {pg_num + 1} ===\n {text} \n\n")

    except Exception:
        pass


if __name__ == "__main__":
    # Настройка логов
    logging.basicConfig(
        level=logging.ERROR,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler("pdf_processing.log"), logging.StreamHandler()],
    )

    # Пример использования
    process_pdf(
        input_path=r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon.pdf",
        output_path="output.txt",
        start_page=0,
        end_page=2,
    )
