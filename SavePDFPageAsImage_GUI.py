# -*- coding: utf-8 -*-
"""
GUI для SavePDFPageAsImage.py - обработка PDF документов с OCR через Gemini AI.
"""
import os
import logging
from typing import Optional
import threading
import queue
import asyncio
from pathlib import Path

# --- Библиотеки для GUI ---
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox

# --- Импорт основной логики ---
from SavePDFPageAsImage import main as process_main

# --- Настройка ---
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# --- Класс для GUI ---
class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Обработчик PDF документов с AI OCR")
        self.root.geometry("800x700")

        self.style = ttk.Style(self.root)
        self.style.theme_use("clam")

        # Переменные для полей
        self.api_name = tk.StringVar(value="Gemini AI")
        self.model_name = tk.StringVar(value="gemini-2.5-flash")
        self.api_key = tk.StringVar()
        self.folder_path = tk.StringVar()

        self.log_queue = queue.Queue()
        self.create_widgets()
        self.setup_logging()
        self.root.after(100, self.process_log_queue)

    def add_context_menu(self, widget):
        """Добавляет контекстное меню с копированием/вставкой для виджета"""
        menu = tk.Menu(widget, tearoff=0)
        menu.add_command(label="Копировать", command=lambda: self.copy_text(widget))
        menu.add_command(label="Вставить", command=lambda: self.paste_text(widget))
        menu.add_separator()
        menu.add_command(label="Выделить все", command=lambda: self.select_all(widget))

        def show_menu(event):
            menu.post(event.x_root, event.y_root)

        widget.bind("<Button-3>", show_menu)  # Правый клик

        # Горячие клавиши
        widget.bind("<Control-c>", lambda e: self.copy_text(widget))
        widget.bind("<Control-v>", lambda e: self.paste_text(widget))
        widget.bind("<Control-a>", lambda e: self.select_all(widget))

    def copy_text(self, widget):
        """Копирует выделенный текст в буфер обмена"""
        try:
            selected_text = widget.selection_get()
            self.root.clipboard_clear()
            self.root.clipboard_append(selected_text)
        except tk.TclError:
            pass  # Нет выделенного текста

    def paste_text(self, widget):
        """Вставляет текст из буфера обмена"""
        try:
            text = self.root.clipboard_get()
            # Для Entry виджетов
            if hasattr(widget, 'delete') and hasattr(widget, 'insert'):
                # Очищаем текущее выделение или вставляем в позицию курсора
                try:
                    start = widget.index(tk.SEL_FIRST)
                    end = widget.index(tk.SEL_LAST)
                    widget.delete(start, end)
                    widget.insert(start, text)
                except tk.TclError:
                    # Нет выделения, вставляем в позицию курсора
                    widget.insert(tk.INSERT, text)
            # Для ScrolledText виджетов
            elif hasattr(widget, 'get') and hasattr(widget, 'delete') and hasattr(widget, 'insert'):
                try:
                    start = widget.index(tk.SEL_FIRST)
                    end = widget.index(tk.SEL_LAST)
                    widget.delete(start, end)
                    widget.insert(start, text)
                except tk.TclError:
                    widget.insert(tk.INSERT, text)
        except tk.TclError:
            pass  # Буфер обмена пуст

    def select_all(self, widget):
        """Выделяет весь текст в виджете"""
        widget.focus_set()
        if hasattr(widget, 'select_range'):
            widget.select_range(0, tk.END)
        elif hasattr(widget, 'tag_add'):
            widget.tag_add(tk.SEL, "1.0", tk.END)

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Параметры
        form_frame = ttk.LabelFrame(main_frame, text="Параметры обработки", padding="10")
        form_frame.pack(fill=tk.X, expand=False)
        form_frame.columnconfigure(1, weight=1)

        # Наименование API
        ttk.Label(form_frame, text="Наименование API:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        api_name_entry = ttk.Entry(form_frame, textvariable=self.api_name)
        api_name_entry.grid(row=0, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(api_name_entry)

        # Наименование модели
        ttk.Label(form_frame, text="Наименование модели:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        model_entry = ttk.Entry(form_frame, textvariable=self.model_name)
        model_entry.grid(row=1, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(model_entry)

        # API Key
        ttk.Label(form_frame, text="API Key:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(form_frame, textvariable=self.api_key, show="*")
        api_key_entry.grid(row=2, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(api_key_entry)

        # Папка для обработки
        ttk.Label(form_frame, text="Папка для обработки:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        folder_entry = ttk.Entry(form_frame, textvariable=self.folder_path)
        folder_entry.grid(row=3, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(folder_entry)
        ttk.Button(form_frame, text="Обзор...", command=self.browse_folder).grid(row=3, column=2, padx=5)

        # Кнопка запуска
        self.run_button = ttk.Button(main_frame, text="Запустить обработку", command=self.start_processing_thread, style="Accent.TButton")
        self.run_button.pack(pady=10)
        self.style.configure("Accent.TButton", foreground="white", background="green")

        # Лог
        log_frame = ttk.LabelFrame(main_frame, text="Лог выполнения", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled', bg='black', fg='white')
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.add_context_menu(self.log_text)

    def browse_folder(self):
        path = filedialog.askdirectory(title="Выберите папку с PDF файлами")
        if path:
            self.folder_path.set(path)

    def start_processing_thread(self):
        # Проверка заполнения полей
        if not self.model_name.get().strip():
            messagebox.showerror("Ошибка", "Введите наименование модели!")
            return
        if not self.api_key.get().strip():
            messagebox.showerror("Ошибка", "Введите API Key!")
            return
        if not self.folder_path.get().strip():
            messagebox.showerror("Ошибка", "Выберите папку для обработки!")
            return

        # Проверка существования папки
        if not os.path.exists(self.folder_path.get()):
            messagebox.showerror("Ошибка", "Выбранная папка не существует!")
            return

        # Отключение кнопки
        self.run_button.config(state='disabled', text="Обработка...")

        # Очистка лога
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

        # Запуск в отдельном потоке
        thread = threading.Thread(
            target=self.run_processing,
            args=(self.model_name.get(), self.api_key.get(), self.folder_path.get()),
            daemon=True
        )
        thread.start()

    def run_processing(self, model_name: str, api_key: str, folder_path: str):
        """Функция, выполняемая в отдельном потоке"""
        try:
            # Установка переменных окружения
            os.environ["GEMINI_API_KEY_AHMED"] = api_key
            # Note: Model switching is handled internally by get_current_model()

            logger.info(f"🚀 Запуск обработки с моделью: {model_name}")
            logger.info(f"📁 Папка: {folder_path}")

            # Запуск асинхронной обработки
            asyncio.run(process_main(folder_path))

            logger.info("✅ Обработка завершена успешно!")

        except Exception as e:
            logger.error(f"❌ Ошибка при обработке: {str(e)}", exc_info=True)

    def setup_logging(self):
        queue_handler = QueueHandler(self.log_queue)
        queue_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        logger.addHandler(queue_handler)

    def process_log_queue(self):
        try:
            while True:
                record = self.log_queue.get_nowait()
                self.log_text.config(state='normal')
                self.log_text.insert(tk.END, record + '\n')
                self.log_text.see(tk.END)
                self.log_text.config(state='disabled')
        except queue.Empty:
            pass

        # Проверка активности потоков обработки
        active_threads = [t for t in threading.enumerate() if isinstance(t, threading.Thread) and t.is_alive() and t.name != 'MainThread']
        processing_active = any(t.daemon for t in active_threads)

        if processing_active:
            self.run_button.config(state='disabled', text="Обработка...")
        else:
            self.run_button.config(state='normal', text="Запустить обработку")

        self.root.after(100, self.process_log_queue)

class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
