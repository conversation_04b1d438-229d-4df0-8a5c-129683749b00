import asyncio
import time
from collections import deque

class RateLimiter:
    def __init__(self, calls_limit: int, time_period: int = 60):
        self.calls_limit = calls_limit
        self.time_period = time_period
        self.calls_timestamps = deque()
        self.lock = asyncio.Lock()
    
    async def wait_if_needed(self):
        async with self.lock:
            current_time = time.time()
            
            # Удаляем устаревшие временные метки
            while self.calls_timestamps and current_time - self.calls_timestamps[0] > self.time_period:
                self.calls_timestamps.popleft()
            
            # Если достигнут лимит вызовов, нужно ждать
            if len(self.calls_timestamps) >= self.calls_limit:
                wait_time = self.calls_timestamps[0] + self.time_period - current_time
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                    current_time = time.time()
            
            # Добавляем новую временную метку
            self.calls_timestamps.append(current_time)