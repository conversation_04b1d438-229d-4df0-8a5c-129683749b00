# from docling.document_converter import DocumentConverter, PdfFormatOption
# from docling.datamodel.pipeline_options import PdfPipelineOptions
# from docling.datamodel.base_models import InputFormat
# from docling_core.types.doc.document import TextItem
# from docling.datamodel.pipeline_options import PdfPipelineOptions, EasyOcrOptions


# import warnings
# warnings.filterwarnings("ignore", message=".*pin_memory.*")

# # pipeline_options = PdfPipelineOptions()
# pipeline_options = PdfPipelineOptions(
#     do_ocr=True,
#     do_table_structure=True,
#     ocr_options=EasyOcrOptions(lang=["uk", "en"])
# )
# converter = DocumentConverter(
#     format_options={
#         InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
#     }
# )

# result = converter.convert(r"c:\Scan\All\AlreadyAddToDb\2025-08-22_151022.pdf")

# for page_no in range(1, len(result.document.pages) + 1):
#     md = result.document.export_to_markdown(page_no=page_no)
#     print(f"--- Page {page_no} ---\n{md}\n")
    

# ********************************

import warnings
warnings.filterwarnings("ignore", message=".*pin_memory.*")

from pathlib import Path
import logging
import time

from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, EasyOcrOptions

# Укажи путь к своему PDF-файлу
PDF_PATH = r"c:\Scan\All\AlreadyAddToDb\ВН 5893  0909 2022.pdf" 

def main():
    logging.basicConfig(level=logging.INFO)

    # Настройка опций: извлечение таблиц и OCR
    pipeline_options = PdfPipelineOptions(
        do_table_structure=True,
        do_ocr=True,
        ocr_options=EasyOcrOptions(lang=["uk"])
    )

    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
        }
    )

    start_time = time.time()
    result = converter.convert(PDF_PATH)
    elapsed = time.time() - start_time
    print(f"Документ обработан за {elapsed:.2f} сек.")

    # Сохраняем результат в текстовый файл с тем же именем, только .txt
    txt_path = Path(PDF_PATH).with_suffix('.txt')
    with open(txt_path, "w", encoding="utf-8") as fp:
        fp.write(result.document.export_to_text())
    print(f"Текст сохранён в: {txt_path}")

if __name__ == "__main__":
    main()
