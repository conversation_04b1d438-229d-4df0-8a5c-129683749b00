# Создам для вас улучшенную версию с автоматическим восстановлением после обрыва и поддержкой сетевых папок:

import asyncio
import aiofiles
import hashlib
import os
import json
import time
import signal
import sys
from pathlib import Path
from typing import Optional, Dict, List, Tuple, Set
from dataclasses import dataclass, asdict
import logging
from datetime import datetime
import argparse

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('file_copier.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FileChunk:
    """Информация о чанке файла"""
    offset: int
    size: int
    hash: str
    copied: bool = False

@dataclass
class FileTask:
    """Задача копирования файла"""
    source_path: str
    dest_path: str
    relative_path: str
    size: int
    chunks: List[FileChunk]
    last_modified: float
    checksum: str
    completed: bool = False
    last_progress_save: float = 0

@dataclass
class CopySession:
    """Сессия копирования"""
    source_dir: str
    dest_dir: str
    session_id: str
    started_at: str
    files: Dict[str, FileTask]
    completed_files: Set[str]
    failed_files: Set[str]
    
class AutoResumeCopier:
    def __init__(self, source_dir: str, dest_dir: str, chunk_size: int = 1024 * 1024):
        self.source_dir = Path(source_dir).resolve()
        self.dest_dir = Path(dest_dir).resolve()
        self.chunk_size = chunk_size
        
        # Создаем уникальный ID сессии
        self.session_id = f"{int(time.time())}_{abs(hash(str(self.source_dir) + str(self.dest_dir)))}"
        self.session_file = f".copy_session_{self.session_id}.json"
        
        # Семафоры для контроля нагрузки
        self.file_semaphore = asyncio.Semaphore(3)  # Максимум 3 файла одновременно
        self.chunk_semaphore = asyncio.Semaphore(10)  # Максимум 10 чанков одновременно
        
        # Флаг для graceful shutdown
        self.shutdown_requested = False
        self.active_tasks = set()
        
        # Статистика
        self.stats = {
            'total_files': 0,
            'completed_files': 0,
            'failed_files': 0,
            'bytes_copied': 0,
            'start_time': time.time()
        }
        
        # Настройка обработки сигналов
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Настройка обработчиков сигналов для graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Получен сигнал {signum}, начинаем graceful shutdown...")
            self.shutdown_requested = True
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def calculate_file_hash(self, file_path: str) -> str:
        """Асинхронное вычисление хеша файла"""
        hash_obj = hashlib.sha256()
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                while True:
                    if self.shutdown_requested:
                        break
                    chunk = await f.read(self.chunk_size)
                    if not chunk:
                        break
                    hash_obj.update(chunk)
        except Exception as e:
            logger.error(f"Ошибка вычисления хеша файла {file_path}: {e}")
            raise
        return hash_obj.hexdigest()
    
    async def calculate_chunk_hash(self, file_path: str, offset: int, size: int) -> str:
        """Асинхронное вычисление хеша чанка"""
        hash_obj = hashlib.sha256()
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                await f.seek(offset)
                remaining = size
                while remaining > 0 and not self.shutdown_requested:
                    read_size = min(remaining, 65536)  # 64KB за раз
                    chunk = await f.read(read_size)
                    if not chunk:
                        break
                    hash_obj.update(chunk)
                    remaining -= len(chunk)
        except Exception as e:
            logger.error(f"Ошибка вычисления хеша чанка {file_path}[{offset}:{offset+size}]: {e}")
            raise
        return hash_obj.hexdigest()
    
    async def scan_directory(self) -> List[Tuple[str, str, str]]:
        """Сканирование директории для получения списка файлов"""
        files_to_copy = []
        
        logger.info(f"Сканирование директории: {self.source_dir}")
        
        try:
            for root, dirs, files in os.walk(self.source_dir):
                if self.shutdown_requested:
                    break
                    
                for file in files:
                    source_path = Path(root) / file
                    relative_path = source_path.relative_to(self.source_dir)
                    dest_path = self.dest_dir / relative_path
                    
                    files_to_copy.append((str(source_path), str(dest_path), str(relative_path)))
        
        except Exception as e:
            logger.error(f"Ошибка сканирования директории: {e}")
            raise
        
        logger.info(f"Найдено {len(files_to_copy)} файлов для копирования")
        return files_to_copy
    
    async def prepare_file_task(self, source_path: str, dest_path: str, relative_path: str) -> FileTask:
        """Подготовка задачи копирования файла"""
        try:
            source_stat = os.stat(source_path)
            file_size = source_stat.st_size
            last_modified = source_stat.st_mtime
            
            # Разбиваем файл на чанки
            chunks = []
            offset = 0
            chunk_num = 0
            
            while offset < file_size and not self.shutdown_requested:
                current_chunk_size = min(self.chunk_size, file_size - offset)
                
                # Вычисляем хеш только для файлов больше 1MB
                if file_size > 1024 * 1024:
                    chunk_hash = await self.calculate_chunk_hash(source_path, offset, current_chunk_size)
                else:
                    chunk_hash = f"small_file_{chunk_num}"
                
                chunks.append(FileChunk(offset, current_chunk_size, chunk_hash))
                offset += current_chunk_size
                chunk_num += 1
            
            # Вычисляем общий хеш для файлов больше 10MB
            if file_size > 10 * 1024 * 1024:
                file_checksum = await self.calculate_file_hash(source_path)
            else:
                file_checksum = "skipped_for_small_file"
            
            return FileTask(
                source_path=source_path,
                dest_path=dest_path,
                relative_path=relative_path,
                size=file_size,
                chunks=chunks,
                last_modified=last_modified,
                checksum=file_checksum
            )
        
        except Exception as e:
            logger.error(f"Ошибка подготовки задачи для файла {source_path}: {e}")
            raise
    
    async def save_session(self, session: CopySession):
        """Сохранение состояния сессии"""
        try:
            session_data = {
                'source_dir': session.source_dir,
                'dest_dir': session.dest_dir,
                'session_id': session.session_id,
                'started_at': session.started_at,
                'files': {k: asdict(v) for k, v in session.files.items()},
                'completed_files': list(session.completed_files),
                'failed_files': list(session.failed_files),
                'stats': self.stats
            }
            
            async with aiofiles.open(self.session_file, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(session_data, indent=2, ensure_ascii=False))
                
        except Exception as e:
            logger.error(f"Ошибка сохранения сессии: {e}")
    
    async def load_session(self) -> Optional[CopySession]:
        """Загрузка состояния сессии"""
        # Ищем существующие файлы сессий
        session_files = [f for f in os.listdir('.') if f.startswith('.copy_session_') and f.endswith('.json')]
        
        for session_file in session_files:
            try:
                async with aiofiles.open(session_file, 'r', encoding='utf-8') as f:
                    data = json.loads(await f.read())
                
                # Проверяем, совпадают ли пути
                if (data['source_dir'] == str(self.source_dir) and 
                    data['dest_dir'] == str(self.dest_dir)):
                    
                    logger.info(f"Найдена существующая сессия: {session_file}")
                    
                    # Восстанавливаем задачи
                    files = {}
                    for path, file_data in data['files'].items():
                        chunks = [FileChunk(**chunk_data) for chunk_data in file_data['chunks']]
                        files[path] = FileTask(
                            source_path=file_data['source_path'],
                            dest_path=file_data['dest_path'],
                            relative_path=file_data['relative_path'],
                            size=file_data['size'],
                            chunks=chunks,
                            last_modified=file_data['last_modified'],
                            checksum=file_data['checksum'],
                            completed=file_data['completed']
                        )
                    
                    session = CopySession(
                        source_dir=data['source_dir'],
                        dest_dir=data['dest_dir'],
                        session_id=data['session_id'],
                        started_at=data['started_at'],
                        files=files,
                        completed_files=set(data['completed_files']),
                        failed_files=set(data['failed_files'])
                    )
                    
                    # Восстанавливаем статистику
                    if 'stats' in data:
                        self.stats.update(data['stats'])
                    
                    self.session_file = session_file
                    return session
                    
            except Exception as e:
                logger.warning(f"Не удалось загрузить сессию из {session_file}: {e}")
                continue
        
        return None
    
    async def verify_existing_chunks(self, file_task: FileTask):
        """Проверка существующих чанков в целевом файле"""
        if not os.path.exists(file_task.dest_path):
            return
        
        try:
            dest_stat = os.stat(file_task.dest_path)
            if dest_stat.st_size == 0:
                return
            
            verified_chunks = 0
            for chunk in file_task.chunks:
                if self.shutdown_requested:
                    break
                    
                if chunk.offset + chunk.size <= dest_stat.st_size:
                    if file_task.size <= 1024 * 1024:  # Для малых файлов не проверяем хеши
                        chunk.copied = True
                        verified_chunks += 1
                    else:
                        try:
                            existing_hash = await self.calculate_chunk_hash(
                                file_task.dest_path, chunk.offset, chunk.size
                            )
                            if existing_hash == chunk.hash:
                                chunk.copied = True
                                verified_chunks += 1
                        except Exception as e:
                            logger.warning(f"Ошибка проверки чанка {chunk.offset}: {e}")
            
            if verified_chunks > 0:
                logger.info(f"Файл {file_task.relative_path}: проверено {verified_chunks}/{len(file_task.chunks)} чанков")
                
        except Exception as e:
            logger.warning(f"Ошибка проверки существующих чанков для {file_task.relative_path}: {e}")
    
    async def copy_chunk(self, file_task: FileTask, chunk: FileChunk):
        """Асинхронное копирование чанка"""
        if chunk.copied or self.shutdown_requested:
            return True
        
        async with self.chunk_semaphore:
            try:
                # Создаем директорию если не существует
                os.makedirs(os.path.dirname(file_task.dest_path), exist_ok=True)
                
                # Читаем чанк из исходного файла
                async with aiofiles.open(file_task.source_path, 'rb') as src:
                    await src.seek(chunk.offset)
                    data = await src.read(chunk.size)
                
                if len(data) != chunk.size:
                    raise ValueError(f"Прочитано {len(data)} байт вместо {chunk.size}")
                
                # Проверяем хеш только для больших файлов
                if file_task.size > 1024 * 1024:
                    actual_hash = hashlib.sha256(data).hexdigest()
                    if actual_hash != chunk.hash:
                        raise ValueError(f"Хеш чанка не совпадает: ожидался {chunk.hash}, получен {actual_hash}")
                
                # Записываем чанк в целевой файл
                mode = 'r+b' if os.path.exists(file_task.dest_path) else 'wb'
                async with aiofiles.open(file_task.dest_path, mode) as dst:
                    await dst.seek(chunk.offset)
                    await dst.write(data)
                    await dst.flush()
                
                chunk.copied = True
                self.stats['bytes_copied'] += len(data)
                return True
                
            except Exception as e:
                logger.error(f"Ошибка копирования чанка {chunk.offset} файла {file_task.relative_path}: {e}")
                return False
    
    async def copy_file(self, session: CopySession, file_task: FileTask):
        """Копирование одного файла"""
        if file_task.completed or self.shutdown_requested:
            return True
        
        async with self.file_semaphore:
            try:
                logger.info(f"Копирование файла: {file_task.relative_path} ({file_task.size} байт)")
                
                # Проверяем, не изменился ли исходный файл
                try:
                    current_stat = os.stat(file_task.source_path)
                    if current_stat.st_mtime != file_task.last_modified:
                        logger.warning(f"Файл {file_task.relative_path} изменился, пересоздаем задачу")
                        new_task = await self.prepare_file_task(
                            file_task.source_path, file_task.dest_path, file_task.relative_path
                        )
                        session.files[file_task.source_path] = new_task
                        file_task = new_task
                except FileNotFoundError:
                    logger.warning(f"Исходный файл {file_task.relative_path} больше не существует")
                    return False
                
                # Проверяем существующие чанки
                await self.verify_existing_chunks(file_task)
                
                # Копируем недостающие чанки
                uncompleted_chunks = [chunk for chunk in file_task.chunks if not chunk.copied]
                
                if not uncompleted_chunks:
                    logger.info(f"Файл {file_task.relative_path} уже полностью скопирован")
                    file_task.completed = True
                    return True
                
                # Создаем задачи для копирования чанков
                chunk_tasks = []
                for chunk in uncompleted_chunks:
                    if self.shutdown_requested:
                        break
                    task = asyncio.create_task(self.copy_chunk(file_task, chunk))
                    self.active_tasks.add(task)
                    chunk_tasks.append(task)
                
                # Ждем завершения всех чанков
                completed_chunks = 0
                failed_chunks = 0
                
                for task in asyncio.as_completed(chunk_tasks):
                    if self.shutdown_requested:
                        break
                        
                    try:
                        success = await task
                        if success:
                            completed_chunks += 1
                        else:
                            failed_chunks += 1
                        
                        # Сохраняем прогресс каждые 5 чанков
                        if (completed_chunks + failed_chunks) % 5 == 0:
                            await self.save_session(session)
                            
                    except Exception as e:
                        logger.error(f"Исключение при копировании чанка: {e}")
                        failed_chunks += 1
                    finally:
                        self.active_tasks.discard(task)
                
                if failed_chunks == 0 and not self.shutdown_requested:
                    # Финальная проверка файла
                    if await self.verify_file_integrity(file_task):
                        file_task.completed = True
                        session.completed_files.add(file_task.source_path)
                        self.stats['completed_files'] += 1
                        logger.info(f"✓ Файл {file_task.relative_path} успешно скопирован")
                        return True
                    else:
                        logger.error(f"✗ Проверка целостности файла {file_task.relative_path} не пройдена")
                        return False
                else:
                    if failed_chunks > 0:
                        logger.error(f"✗ Ошибка копирования файла {file_task.relative_path}: {failed_chunks} неудачных чанков")
                    return False
                    
            except Exception as e:
                logger.error(f"Ошибка копирования файла {file_task.relative_path}: {e}")
                session.failed_files.add(file_task.source_path)
                self.stats['failed_files'] += 1
                return False
    
    async def verify_file_integrity(self, file_task: FileTask) -> bool:
        """Проверка целостности скопированного файла"""
        try:
            if not os.path.exists(file_task.dest_path):
                return False
            
            dest_stat = os.stat(file_task.dest_path)
            if dest_stat.st_size != file_task.size:
                logger.error(f"Размер файла не совпадает: ожидался {file_task.size}, получен {dest_stat.st_size}")
                return False
            
            # Проверяем контрольную сумму только для больших файлов
            if file_task.size > 10 * 1024 * 1024 and file_task.checksum != "skipped_for_small_file":
                dest_checksum = await self.calculate_file_hash(file_task.dest_path)
                if dest_checksum != file_task.checksum:
                    logger.error(f"Контрольная сумма не совпадает для файла {file_task.relative_path}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка проверки целостности файла {file_task.relative_path}: {e}")
            return False
    
    def print_progress(self, session: CopySession):
        """Вывод прогресса копирования"""
        total_files = len(session.files)
        completed = len(session.completed_files)
        failed = len(session.failed_files)
        in_progress = total_files - completed - failed
        
        elapsed = time.time() - self.stats['start_time']
        bytes_mb = self.stats['bytes_copied'] / (1024 * 1024)
        speed_mbps = bytes_mb / elapsed if elapsed > 0 else 0
        
        logger.info(f"Прогресс: {completed}/{total_files} файлов ({completed/total_files*100:.1f}%)")
        logger.info(f"Завершено: {completed}, В процессе: {in_progress}, Ошибок: {failed}")
        logger.info(f"Скопировано: {bytes_mb:.1f} MB, Скорость: {speed_mbps:.1f} MB/s")
    
    async def copy_directory(self):
        """Основная функция копирования директории"""
        try:
            # Пытаемся загрузить существующую сессию
            session = await self.load_session()
            
            if session is None:
                # Создаем новую сессию
                logger.info("Создание новой сессии копирования")
                files_to_copy = await self.scan_directory()
                
                if not files_to_copy:
                    logger.info("Нет файлов для копирования")
                    return True
                
                # Подготавливаем задачи
                files = {}
                self.stats['total_files'] = len(files_to_copy)
                
                logger.info("Подготовка задач копирования...")
                for i, (source_path, dest_path, relative_path) in enumerate(files_to_copy):
                    if self.shutdown_requested:
                        break
                        
                    try:
                        file_task = await self.prepare_file_task(source_path, dest_path, relative_path)
                        files[source_path] = file_task
                        
                        if (i + 1) % 100 == 0:
                            logger.info(f"Подготовлено {i + 1}/{len(files_to_copy)} задач")
                            
                    except Exception as e:
                        logger.error(f"Ошибка подготовки задачи для {relative_path}: {e}")
                        continue
                
                session = CopySession(
                    source_dir=str(self.source_dir),
                    dest_dir=str(self.dest_dir),
                    session_id=self.session_id,
                    started_at=datetime.now().isoformat(),
                    files=files,
                    completed_files=set(),
                    failed_files=set()
                )
                
            else:
                logger.info(f"Восстановление сессии от {session.started_at}")
                logger.info(f"Найдено {len(session.files)} файлов, завершено {len(session.completed_files)}")
            
            # Сохраняем начальное состояние
            await self.save_session(session)
            
            # Копируем файлы
            remaining_files = [
                task for path, task in session.files.items() 
                if path not in session.completed_files and path not in session.failed_files
            ]
            
            logger.info(f"Начинаем копирование {len(remaining_files)} файлов")
            
            # Создаем задачи копирования с ограничением
            file_tasks = []
            for file_task in remaining_files:
                if self.shutdown_requested:
                    break
                task = asyncio.create_task(self.copy_file(session, file_task))
                self.active_tasks.add(task)
                file_tasks.append(task)
            
            # Ждем завершения с периодическим выводом прогресса
            completed_count = 0
            last_progress_time = time.time()
            
            for task in asyncio.as_completed(file_tasks):
                if self.shutdown_requested:
                    logger.info("Получен сигнал остановки, завершаем текущие задачи...")
                    break
                
                try:
                    await task
                    completed_count += 1
                    
                    # Выводим прогресс каждые 30 секунд или каждые 10 файлов
                    current_time = time.time()
                    if (current_time - last_progress_time > 30 or 
                        completed_count % 10 == 0):
                        self.print_progress(session)
                        await self.save_session(session)
                        last_progress_time = current_time
                        
                except Exception as e:
                    logger.error(f"Исключение при копировании файла: {e}")
                finally:
                    self.active_tasks.discard(task)
            
            # Финальное сохранение и отчет
            await self.save_session(session)
            self.print_final_report(session)
            
            # Если все файлы скопированы успешно, удаляем файл сессии
            if len(session.failed_files) == 0 and not self.shutdown_requested:
                if os.path.exists(self.session_file):
                    os.remove(self.session_file)
                logger.info("Копирование завершено успешно!")
                return True
            else:
                logger.info(f"Копирование завершено с ошибками. Файл сессии сохранен: {self.session_file}")
                return False
                
        except Exception as e:
            logger.error(f"Критическая ошибка копирования: {e}")
            return False
    
    def print_final_report(self, session: CopySession):
        """Вывод финального отчета"""
        total_files = len(session.files)
        completed = len(session.completed_files)
        failed = len(session.failed_files)
        
        elapsed = time.time() - self.stats['start_time']
        bytes_gb = self.stats['bytes_copied'] / (1024 * 1024 * 1024)
        
        logger.info("=" * 50)
        logger.info("ОТЧЕТ О КОПИРОВАНИИ")
        logger.info("=" * 50)
        logger.info(f"Источник: {self.source_dir}")
        logger.info(f"Назначение: {self.dest_dir}")
        logger.info(f"Всего файлов: {total_files}")
        logger.info(f"Успешно скопировано: {completed}")
        logger.info(f"Ошибок: {failed}")
        logger.info(f"Данных скопировано: {bytes_gb:.2f} GB")
        logger.info(f"Время выполнения: {elapsed/3600:.1f} часов")
        logger.info(f"Средняя скорость: {bytes_gb*3600/elapsed:.1f} GB/час")
        
        if failed > 0:
            logger.info("\nФайлы с ошибками:")
            for failed_file in session.failed_files:
                if failed_file in session.files:
                    logger.info(f"  - {session.files[failed_file].relative_path}")
        
        logger.info("=" * 50)

def main():
    parser = argparse.ArgumentParser(description='Асинхронный копировщик с автовосстановлением')
    parser.add_argument('source', help='Исходная директория (может быть сетевой)')
    parser.add_argument('dest', help='Целевая директория')
    parser.add_argument('--chunk-size', type=int, default=1024*1024, 
                       help='Размер чанка в байтах (по умолчанию 1MB)')
    
    args = parser.parse_args()
    
    # Проверяем существование исходной директории
    if not os.path.exists(args.source):
        logger.error(f"Исходная директория не существует: {args.source}")
        return False
    
    if not os.path.isdir(args.source):
        logger.error(f"Указанный путь не является директорией: {args.source}")
        return False
    
    # Создаем целевую директорию если не существует
    try:
        os.makedirs(args.dest, exist_ok=True)
    except Exception as e:
        logger.error(f"Не удалось создать целевую директорию {args.dest}: {e}")
        return False
    
    logger.info(f"Начинаем копирование:")
    logger.info(f"  Источник: {args.source}")
    logger.info(f"  Назначение: {args.dest}")
    logger.info(f"  Размер чанка: {args.chunk_size} байт")
    
    copier = AutoResumeCopier(args.source, args.dest, args.chunk_size)
    
    try:
        success = asyncio.run(copier.copy_directory())
        return success
    except KeyboardInterrupt:
        logger.info("Копирование прервано пользователем")
        return False
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)


"""
## Создайте также простой скрипт запуска `copy.py`:
#!/usr/bin/env python3

# Простой запуск копировщика файлов

import sys
import os
import asyncio
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auto_resume_copier import AutoResumeCopier
import logging

def main():
    if len(sys.argv) != 3:
        print("Использование: python copy.py <источник> <назначение>")
        print("Примеры:")
        print("  python copy.py /home/<USER>/docs /backup/docs")
        print("  python copy.py //server/share/folder /local/backup")
        print("  python copy.py Z:\\Documents C:\\Backup\\Documents")
        return False
    
    source_dir = sys.argv[1]
    dest_dir = sys.argv[2]
    
"""