import base64
import os
from google import genai
from google.genai import types
from dotenv import load_dotenv

load_dotenv()

def test_gemini_simple():
    """Простой тест Gemini API"""
    try:
        client = genai.Client(
            api_key=os.environ.get("GEMINI_API_KEY"),
        )
        
        # Простой текстовый запрос
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text="Привет! Как дела?"),
                ],
            )
        ]
        
        model = "gemini-2.0-flash-exp"
        print("Отправляем запрос к Gemini...")
        
        response = client.models.generate_content(
            model=model, 
            contents=contents
        )
        
        print("Ответ получен:")
        print(response.text)
        
    except Exception as e:
        print(f"Ошибка: {e}")

if __name__ == "__main__":
    test_gemini_simple()
