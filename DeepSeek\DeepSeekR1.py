import requests
from dotenv import load_dotenv
import os


load_dotenv()
api_key = os.getenv("DEEPSEEK_API_KEY")


def extract_text_from_pdf(file_path: str):
    # URL API (уточните в документации DeepSeek R1)
    # url = "https://api.deepseek.com/v1/ocr"
    url = "https://api.deepseek.com/v1/ocr"
    
    # Заголовки с авторизацией
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    try:
        # Открываем файл и отправляем запрос
        with open(file_path, 'rb') as file:
            files = {'file': (file_path, file, 'application/pdf')}
            response = requests.post(url, headers=headers, files=files)
        
        # Проверяем статус ответа
        if response.status_code == 200:
            return response.json()  # Предполагаем, что текст возвращается в JSON
        else:
            return f"Ошибка: {response.status_code} - {response.text}"
            
    except Exception as e:
        return f"Ошибка при выполнении запроса: {str(e)}"


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # Пример использования
    pdf_file = r"d:\Scan\StranicaNaoborot.pdf"
    result = extract_text_from_pdf(pdf_file)
    print(result)