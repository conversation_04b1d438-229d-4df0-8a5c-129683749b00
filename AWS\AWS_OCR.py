# pip install boto3

import boto3
import time
import os
import sys
import io
from botocore.exceptions import ClientError
from pdf2image import convert_from_path
from PIL import Image

def check_textract_access():
    """Проверка доступа к AWS Textract"""
    try:
        # Пробуем выполнить простой запрос к Textract
        client = boto3.client('textract', region_name='us-east-1')
        # Проверяем только наличие доступа, не выполняя реальный запрос
        client.list_document_text_detection_jobs(MaxResults=1)
        return True
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code', '')
        if error_code == 'AccessDeniedException':
            print("У вас нет доступа к AWS Textract.")
            print("Обратитесь к администратору AWS для настройки прав доступа.")
            print("Инструкции находятся в файле AWS_Textract_Instructions.txt")
            return False
        else:
            # Другие ошибки могут быть связаны с неправильными параметрами запроса,
            # но не с отсутствием доступа
            return True
    except Exception as e:
        print(f"Ошибка при проверке доступа к AWS Textract: {str(e)}")
        return False

# Настройка клиента Textract
client = boto3.client(
    'textract',
    region_name='us-east-1'  # Используем настройки из профиля AWS
)

def extract_text_from_document(file_path):
    print(f"Открываем файл: {file_path}")

    # Проверяем расширение файла
    file_extension = os.path.splitext(file_path)[1].lower()

    if file_extension == '.pdf':
        print("Обнаружен PDF файл. Преобразуем в изображение...")
        try:
            # Преобразуем PDF в изображения
            images = convert_from_path(file_path, dpi=300, first_page=1, last_page=1)
            if not images:
                raise Exception("Не удалось преобразовать PDF в изображение")

            print(f"PDF успешно преобразован в {len(images)} изображений")

            # Берем первую страницу для анализа
            image = images[0]

            # Преобразуем изображение в байты
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte = img_byte_arr.getvalue()
            print(f"Изображение подготовлено, размер: {len(img_byte)} байт")

        except Exception as e:
            print(f"Ошибка при преобразовании PDF: {str(e)}")
            raise
    else:
        # Для изображений (PNG/JPEG)
        try:
            with open(file_path, 'rb') as file:
                img_byte = file.read()
                print(f"Файл успешно прочитан, размер: {len(img_byte)} байт")
        except Exception as e:
            print(f"Ошибка при чтении файла: {str(e)}")
            raise

    try:
        # Анализ документа
        print("Отправляем запрос в AWS Textract...")
        response = client.detect_document_text(
            Document={'Bytes': img_byte}
            # AWS Textract автоматически определяет язык
        )
        print("Ответ от AWS Textract получен")

        # Извлечение текста
        extracted_text = []
        for block in response['Blocks']:
            if block['BlockType'] == 'LINE':
                extracted_text.append(block['Text'])

        return '\n'.join(extracted_text)
    except Exception as e:
        print(f"Ошибка при анализе документа: {str(e)}")
        raise

# Для многостраничных PDF (асинхронная обработка)
def extract_text_from_pdf(s3_bucket, s3_file):
    response = client.start_document_text_detection(
        DocumentLocation={
            'S3Object': {
                'Bucket': s3_bucket,
                'Name': s3_file
            }
        }
        # AWS Textract автоматически определяет язык
    )

    job_id = response['JobId']
    while True:
        result = client.get_document_text_detection(
            JobId=job_id
            # AWS Textract автоматически определяет язык
        )
        if result['JobStatus'] in ['SUCCEEDED', 'FAILED']:
            break
        time.sleep(5)

    if result['JobStatus'] == 'SUCCEEDED':
        text = []
        for page in result['Blocks']:
            if page['BlockType'] == 'LINE':
                text.append(page['Text'])
        return '\n'.join(text)
    else:
        raise Exception("Ошибка распознавания")


# Пример использования
if __name__ == "__main__":
    # Для локального файла (изображение)
    text = extract_text_from_document(r'c:\Scan\All\TestVN2str.pdf')
    print("Извлеченный текст:\n", text)