import psycopg2
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import normalize
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from dotenv import load_dotenv
import os
import logging
from functools import lru_cache
import pickle
from pathlib import Path
import pandas as pd
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
import warnings
warnings.filterwarnings('ignore')

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
load_dotenv()

class SmartDocumentExtractor:
    """Интеллектуальный экстрактор метаданных из OCR текста"""
    
    def __init__(self):
        # Эталонные типы документов
        self.document_types = {
            'ТТН': ['товарно-транспортна накладна', 'ттн', 'товарно транспортна'],
            'Накладна на повернення': ['накладна на повернення', 'повернення товар', 'return invoice'],
            'Видаткова накладна': ['видаткова накладна', 'расходная накладная', 'видаткова'],
            'Податкова накладна': ['податкова накладна', 'налоговая накладная'],
            'Акт повернення': ['акт повернення', 'акт возврата', 'return act'],
            'Акт': ['акт приймання', 'акт виконаних', 'акт звірки'],
            'Рахунок-фактура': ['рахунок-фактура', 'счет-фактура', 'invoice'],
            'Накладна': ['накладна', 'накладная', 'waybill'],
        }
        
        # Известные клиенты
        self.known_clients = {
            'ЕПІЦЕНТР': ['епіцентр', 'epicenter', 'эпицентр', 'епіцентр к'],
            'МЕТРО': ['метро', 'metro', 'metro cash'],
            'АШАН': ['ашан', 'auchan', 'ошан'],
            'СІЛЬПО': ['сільпо', 'silpo', 'сильпо'],
            'АТБ': ['атб', 'atb'],
            'РОЗЕТКА': ['розетка', 'rozetka'],
            'НОВА ПОШТА': ['нова пошта', 'nova poshta', 'новая почта'],
        }
    
    def extract_document_type(self, text: str) -> Tuple[str, float]:
        """
        Извлечение типа документа с использованием нечеткого поиска
        Возвращает: (тип, уверенность 0-1)
        """
        text_lower = text.lower()[:500]  # Анализируем первые 500 символов
        
        best_type = 'Документ'
        best_score = 0.0
        
        for doc_type, patterns in self.document_types.items():
            for pattern in patterns:
                # Используем нечеткое сравнение
                score = fuzz.partial_ratio(pattern, text_lower) / 100.0
                
                # Дополнительный поиск по ключевым словам
                if all(word in text_lower for word in pattern.split()):
                    score = min(score + 0.3, 1.0)
                
                if score > best_score:
                    best_score = score
                    best_type = doc_type
        
        # Порог уверенности
        if best_score < 0.5:
            best_type = 'Документ'
        
        return best_type, best_score
    
    def extract_document_number(self, text: str, doc_type: str) -> Tuple[str, float]:
        """
        Извлечение номера документа с валидацией
        Возвращает: (номер, уверенность 0-1)
        """
        # Паттерны для поиска номера
        number_patterns = [
            r'№\s*([А-ЯA-Zа-яa-z0-9\-/]{2,30})',
            r'номер\s*[:]*\s*([А-ЯA-Z0-9\-/]{2,30})',
            r'№([А-Яа-я]{1,3}/[А-ЯA-Z0-9\-]+/[А-ЯA-Z0-9\-]+)',
            r'#\s*([А-ЯA-Z0-9\-/]{2,30})',
        ]
        
        # Ищем номер рядом с типом документа
        doc_type_pos = text.lower().find(doc_type.lower())
        search_area = text
        if doc_type_pos >= 0:
            # Ищем в области 200 символов после типа
            search_area = text[doc_type_pos:min(doc_type_pos + 200, len(text))]
        
        candidates = []
        for pattern in number_patterns:
            matches = re.findall(pattern, search_area, re.IGNORECASE)
            for match in matches:
                # Валидация номера
                if self._validate_document_number(match):
                    candidates.append(match)
        
        if candidates:
            # Выбираем наиболее вероятный номер
            best_number = max(candidates, key=lambda x: len(x))
            confidence = 0.8 if len(candidates) == 1 else 0.6
            return best_number, confidence
        
        return 'Не вказано', 0.0
    
    def _validate_document_number(self, number: str) -> bool:
        """Валидация номера документа"""
        if not number or len(number) < 2 or len(number) > 50:
            return False
        
        # Исключаем служебные слова
        exclude_words = ['від', 'from', 'дата', 'date', 'сторінка', 'page', 'резерву']
        if any(word in number.lower() for word in exclude_words):
            return False
        
        # Должны быть цифры или специальные символы
        if not any(c.isdigit() or c in '-/' for c in number):
            return False
        
        return True
    
    def extract_date(self, text: str, doc_type: str) -> Tuple[str, float]:
        """
        Извлечение даты документа с валидацией
        Возвращает: (дата, уверенность 0-1)
        """
        # Паттерны дат
        date_patterns = [
            (r'від\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{2,4})', 0.9),
            (r'дата[:]?\s*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{2,4})', 0.9),
            (r'(\d{1,2}[\.\/]\d{1,2}[\.\/]20\d{2})', 0.7),
            (r'(\d{1,2})\.(0[1-9]|1[0-2])\.(\d{4})', 0.8),
        ]
        
        candidates = []
        for pattern, confidence in date_patterns:
            matches = re.findall(pattern, text[:1000], re.IGNORECASE)
            for match in matches:
                date_str = match if isinstance(match, str) else '.'.join(match)
                if self._validate_date(date_str):
                    candidates.append((date_str, confidence))
        
        if candidates:
            # Сортируем по уверенности
            candidates.sort(key=lambda x: x[1], reverse=True)
            return candidates[0]
        
        return 'Не вказано', 0.0
    
    def _validate_date(self, date_str: str) -> bool:
        """Валидация даты"""
        try:
            # Проверяем формат даты
            parts = re.split(r'[\.\/\-]', date_str)
            if len(parts) != 3:
                return False
            
            day, month, year = parts
            day = int(day)
            month = int(month)
            year = int(year) if len(year) == 4 else 2000 + int(year)
            
            # Проверка диапазонов
            if not (1 <= day <= 31 and 1 <= month <= 12 and 2020 <= year <= 2030):
                return False
            
            return True
        except:
            return False
    
    def extract_client(self, text: str) -> Tuple[str, float]:
        """
        Извлечение клиента с использованием нечеткого поиска
        Возвращает: (клиент, уверенность 0-1)
        """
        text_lower = text.lower()
        
        # Сначала ищем известных клиентов
        for client_name, variants in self.known_clients.items():
            for variant in variants:
                if variant in text_lower:
                    return client_name, 0.95
        
        # Нечеткий поиск известных клиентов
        best_match = None
        best_score = 0
        for client_name, variants in self.known_clients.items():
            for variant in variants:
                score = fuzz.partial_ratio(variant, text_lower) / 100.0
                if score > best_score and score > 0.8:
                    best_score = score
                    best_match = client_name
        
        if best_match:
            return best_match, best_score
        
        # Поиск по паттернам юрлиц
        company_patterns = [
            r'(?:Відправник|Покупець|Платник):\s*(?:ТОВ|ООО|ПП)\s+[«"\']*([^»"\'\n]{3,50})',
            r'(?:ТОВ|ООО)\s+[«"\']*([^»"\'\n]{3,50})[»"\']*',
            r'(?:ПП|ЧП|ФОП)\s+[«"\']*([^»"\'\n]{3,50})[»"\']*',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if self._validate_client_name(match):
                    return match.strip(), 0.7
        
        return 'Не вказано', 0.0
    
    def _validate_client_name(self, name: str) -> bool:
        """Валидация имени клиента"""
        if not name or len(name) < 3 or len(name) > 100:
            return False
        
        # Исключаем ФИО
        if re.match(r'^[А-ЯЄІЇҐ][а-яєіїґ]+\s+[А-ЯЄІЇҐ][а-яєіїґ]+\s+[А-ЯЄІЇҐ][а-яєіїґ]+$', name):
            return False
        
        # Исключаем служебные слова
        exclude_words = ['автор', 'друку', 'причина', 'повернення', 'документ', 'сторінка']
        if any(word in name.lower() for word in exclude_words):
            return False
        
        return True


class AdvancedDocumentSearch:
    """Продвинутая система поиска документов"""
    
    def __init__(self, cache_dir: str = "./cache"):
        self.db_config = {
            'host': 'localhost',
            'database': os.getenv('PG_DBNAME'),
            'user': os.getenv('PG_USER'),
            'password': os.getenv('PG_PASSWORD'),
            'port': os.getenv('PG_PORT', 5432),
            'client_encoding': 'utf-8'
        }
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        self.conn = None
        self.connect_to_db()
        
        # Инициализация экстрактора
        self.extractor = SmartDocumentExtractor()
        
        # TF-IDF векторизатор
        self.vectorizer = TfidfVectorizer(
            max_features=50000,
            ngram_range=(1, 3),
            min_df=1,
            max_df=0.95,
            sublinear_tf=True,
            use_idf=True,
            smooth_idf=True,
            encoding='utf-8'
        )
        
        self.tfidf_matrix = None
        self.documents = []
        self.is_index_built = False
    
    def connect_to_db(self):
        """Подключение к БД"""
        try:
            if self.conn and not self.conn.closed:
                return
            
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.set_client_encoding('UTF8')
            logger.info("✅ Підключено до бази даних")
        except Exception as e:
            logger.error(f"❌ Помилка підключення до БД: {e}")
            raise
    
    def build_index(self, force_rebuild: bool = False):
        """Построение индекса документов"""
        cache_file = self.cache_dir / "document_index.pkl"
        
        if not force_rebuild and cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self.documents = cache_data['documents']
                    self.tfidf_matrix = cache_data['matrix']
                    self.vectorizer = cache_data['vectorizer']
                    self.is_index_built = True
                    logger.info(f"✅ Завантажено кеш: {len(self.documents)} документів")
                    return
            except Exception as e:
                logger.warning(f"⚠️ Помилка кешу: {e}")
        
        try:
            self.connect_to_db()
            
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        id, 
                        file_name, 
                        page_number, 
                        description, 
                        full_path,
                        created_at
                    FROM t_scan_documents_raw 
                    WHERE description IS NOT NULL 
                        AND LENGTH(description) > 50
                    ORDER BY file_name, page_number
                """)
                
                raw_docs = cursor.fetchall()
            
            logger.info(f"📚 Завантажено {len(raw_docs)} документів з БД")
            
            self.documents = []
            texts_for_vectorization = []
            
            for doc in raw_docs:
                doc_id, file_name, page_number, description, full_path, created_at = doc
                
                # Извлечение метаданных
                doc_type, type_conf = self.extractor.extract_document_type(description)
                doc_number, number_conf = self.extractor.extract_document_number(description, doc_type)
                doc_date, date_conf = self.extractor.extract_date(description, doc_type)
                client, client_conf = self.extractor.extract_client(description)
                
                # Общая уверенность
                total_confidence = (type_conf + number_conf + date_conf + client_conf) / 4
                
                # Подготовка текста для векторизации
                clean_text = self._clean_text(description)
                
                self.documents.append({
                    'id': doc_id,
                    'file_name': str(file_name),
                    'page_number': page_number,
                    'full_path': str(full_path),
                    'original_text': description,
                    'clean_text': clean_text,
                    'type': doc_type,
                    'number': doc_number,
                    'date': doc_date,
                    'client': client,
                    'confidence': total_confidence,
                    'created_at': created_at
                })
                
                texts_for_vectorization.append(clean_text)
            
            if texts_for_vectorization:
                # Построение TF-IDF матрицы
                self.tfidf_matrix = self.vectorizer.fit_transform(texts_for_vectorization)
                self.tfidf_matrix = normalize(self.tfidf_matrix, norm='l2', axis=1)
                
                logger.info(f"✅ Індекс побудовано: {self.tfidf_matrix.shape}")
                
                # Сохранение кеша
                try:
                    cache_data = {
                        'documents': self.documents,
                        'matrix': self.tfidf_matrix,
                        'vectorizer': self.vectorizer
                    }
                    with open(cache_file, 'wb') as f:
                        pickle.dump(cache_data, f)
                    logger.info("💾 Кеш збережено")
                except Exception as e:
                    logger.warning(f"⚠️ Не вдалося зберегти кеш: {e}")
                
                self.is_index_built = True
                
        except Exception as e:
            logger.error(f"❌ Помилка побудови індексу: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """Очистка текста для векторизации"""
        if not text:
            return ""
        
        text = str(text).lower()
        # Удаляем лишние символы
        text = re.sub(r'[^\w\s\-\./]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def search(self, 
              query: str = "",
              doc_type: str = None,
              client: str = None,
              period: str = None,
              min_confidence: float = 0.3,
              max_results: int = 100) -> List[Dict[str, Any]]:
        """
        Умный поиск документов
        """
        self.build_index()
        
        if not self.documents:
            logger.warning("⚠️ Індекс порожній")
            return []
        
        # Этап 1: Векторный поиск (если есть запрос)
        candidates = []
        
        if query and query.strip():
            clean_query = self._clean_text(query)
            query_vec = self.vectorizer.transform([clean_query])
            query_vec = normalize(query_vec, norm='l2', axis=1)
            
            similarities = cosine_similarity(query_vec, self.tfidf_matrix).flatten()
            
            for idx, similarity in enumerate(similarities):
                if similarity > 0.1:  # Низкий порог для начального отбора
                    doc = self.documents[idx].copy()
                    doc['vector_score'] = float(similarity)
                    candidates.append(doc)
        else:
            # Без запроса берем все документы
            candidates = [doc.copy() for doc in self.documents]
            for doc in candidates:
                doc['vector_score'] = 1.0
        
        # Этап 2: Фильтрация и оценка
        results = []
        
        for doc in candidates:
            # Пропускаем документы с низкой уверенностью извлечения
            if doc['confidence'] < min_confidence:
                continue
            
            # Фильтр по типу документа (нечеткий)
            if doc_type:
                type_score = fuzz.partial_ratio(doc_type.lower(), doc['type'].lower()) / 100.0
                if type_score < 0.5:
                    continue
            else:
                type_score = 1.0
            
            # Фильтр по клиенту (нечеткий)
            if client:
                client_score = fuzz.partial_ratio(client.lower(), doc['client'].lower()) / 100.0
                if client_score < 0.5:
                    continue
            else:
                client_score = 1.0
            
            # Фильтр по периоду
            if period:
                if not self._match_period(period, doc['date'], doc['original_text']):
                    continue
            
            # Итоговая оценка
            final_score = (
                doc['vector_score'] * 0.4 +
                doc['confidence'] * 0.3 +
                type_score * 0.15 +
                client_score * 0.15
            )
            
            doc['final_score'] = final_score
            results.append(doc)
        
        # Сортировка по оценке
        results.sort(key=lambda x: x['final_score'], reverse=True)
        
        return results[:max_results]
    
    def _match_period(self, period: str, doc_date: str, text: str) -> bool:
        """Сопоставление периода"""
        if 'не вказано' in doc_date.lower():
            return False
        
        period_lower = period.lower()
        
        # Словарь месяцев
        months = {
            'січень': '01', 'лютий': '02', 'березень': '03',
            'квітень': '04', 'травень': '05', 'червень': '06',
            'липень': '07', 'серпень': '08', 'вересень': '09',
            'жовтень': '10', 'листопад': '11', 'грудень': '12'
        }
        
        # Извлекаем месяц и год из периода
        period_month = None
        period_year = None
        
        for month_name, month_num in months.items():
            if month_name in period_lower:
                period_month = month_num
                break
        
        year_match = re.search(r'20\d{2}', period)
        if year_match:
            period_year = year_match.group()
        
        # Проверяем соответствие
        if period_month and period_year:
            pattern = f'{period_month}.*{period_year}|{period_year}.*{period_month}'
            if re.search(pattern, doc_date):
                return True
        elif period_year:
            if period_year in doc_date:
                return True
        
        return False
    
    def export_to_excel(self, results: List[Dict], filename: str = None) -> str:
        """Экспорт результатов в Excel"""
        if not results:
            logger.warning("⚠️ Немає результатів для експорту")
            return None
        
        # Подготовка данных для DataFrame
        data = []
        for i, doc in enumerate(results, 1):
            data.append({
                '№': i,
                'Тип документа': doc['type'],
                'Номер документа': doc['number'],
                'Дата документа': doc['date'],
                'Клієнт': doc['client'],
                'Файл': doc['file_name'],
                'Сторінка': doc['page_number'],
                'Впевненість': f"{doc['confidence']:.2f}",
                'Релевантність': f"{doc['final_score']:.2f}"
            })
        
        # Создание DataFrame
        df = pd.DataFrame(data)
        
        # Генерация имени файла
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'search_results_{timestamp}.xlsx'
        
        # Путь для сохранения (в папке скрипта)
        output_path = Path(__file__).parent / filename
        
        # Сохранение в Excel с форматированием
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Результати пошуку', index=False)
            
            # Форматирование
            worksheet = writer.sheets['Результати пошуку']
            
            # Автоподбор ширины колонок
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"✅ Результати збережено: {output_path}")
        return str(output_path)
    
    def format_as_markdown(self, results: List[Dict]) -> str:
        """Форматирование результатов в Markdown"""
        if not results:
            return "❌ **Документів не знайдено**"
        
        table = "| № | Тип документа | Номер | Дата | Клієнт | Файл | Стор. | Впевненість |\n"
        table += "|---|---------------|-------|------|--------|------|-------|-------------|\n"
        
        for i, doc in enumerate(results[:50], 1):  # Ограничиваем вывод
            table += (f"| {i} | {doc['type']} | {doc['number']} | {doc['date']} | "
                     f"{doc['client']} | {doc['file_name']} | {doc['page_number']} | "
                     f"{doc['confidence']:.2f} |\n")
        
        if len(results) > 50:
            table += f"\n_...та ще {len(results) - 50} документів_\n"
        
        return table
    
    def close(self):
        """Закрытие соединения"""
        if self.conn and not self.conn.closed:
            self.conn.close()
            logger.info("🔌 З'єднання закрито")


class DocumentSearchInterface:
    """Удобный интерфейс для работы с поиском"""
    
    def __init__(self):
        self.search_engine = AdvancedDocumentSearch()
    
    def search(self, **kwargs) -> List[Dict]:
        """Выполнение поиска"""
        return self.search_engine.search(**kwargs)
    
    def search_and_export(self, **kwargs) -> Tuple[List[Dict], str]:
        """Поиск с экспортом в Excel"""
        results = self.search_engine.search(**kwargs)
        
        if results:
            filepath = self.search_engine.export_to_excel(results)
            return results, filepath
        
        return results, None
    
    def rebuild_index(self):
        """Перестроение индекса"""
        self.search_engine.build_index(force_rebuild=True)
    
    def close(self):
        """Закрытие"""
        self.search_engine.close()


def interactive_search_with_export():
    """Интерактивный поиск с экспортом в Excel"""
    search = DocumentSearchInterface()
    
    try:
        print("=" * 80)
        print("🔍 ПОШУК ДОКУМЕНТІВ З ЕКСПОРТОМ В EXCEL")
        print("=" * 80)
        print("Введіть параметри пошуку для експорту:")
        
        query = input("📝 Текстовий запит (Enter - пропустити): ").strip()
        doc_type = input("📄 Тип документа (Enter - пропустити): ").strip()
        client = input("👤 Клієнт (Enter - пропустити): ").strip()
        period = input("📅 Період (Enter - пропустити): ").strip()
        
        custom_path = input("💾 Шлях для збереження (Enter - автоматично): ").strip()
        
        # Подготовка параметров
        search_params = {}
        if query:
            search_params['query'] = query
        if doc_type:
            search_params['doc_type'] = doc_type
        if client:
            search_params['client'] = client
        if period:
            search_params['period'] = period
        
        if not search_params:
            print("⚠️ Не вказано параметрів пошуку!")
            return
        
        print("\n🔄 Виконується пошук...")
        
        # Поиск и экспорт
        results, filepath = search.search_and_export(**search_params)
        
        if results:
            print(f"\n✅ Знайдено {len(results)} документів")
            print(f"💾 Збережено в: {filepath}")
            
            # Показываем первые результаты
            print("\n📊 Перші 10 результатів:")
            print("-" * 80)
            
            for i, doc in enumerate(results[:10], 1):
                print(f"{i}. {doc['type']} №{doc['number']} від {doc['date']}")
                print(f"   Клієнт: {doc['client']}")
                print(f"   Файл: {doc['file_name']}, стор. {doc['page_number']}")
                print(f"   Впевненість: {doc['confidence']:.2%}")
                print()
        else:
            print("❌ Документів не знайдено")
        
    except Exception as e:
        print(f"❌ Помилка: {e}")
        import traceback
        traceback.print_exc()
    finally:
        search.close()


def test_extraction():
    """Тестирование извлечения метаданных"""
    extractor = SmartDocumentExtractor()
    
    # Тестовый текст (как из вашего примера)
    test_text = """
    Постачальник-одержувач: Товариство з обмеженою відповідальністю "ПРЕСТИЖ ПРОДУКТ.К" ЄДРПОУ 41098985
    Накладна на повернення №Пп/К7-0002999/К7-0064785-24 від 12.10.2024 ☑
    Відправник: ТОВ "Епіцентр К" 04128, м. Київ, вул. Берковецька, 6-К (044) 206-26-00
    """
    
    doc_type, type_conf = extractor.extract_document_type(test_text)
    number, number_conf = extractor.extract_document_number(test_text, doc_type)
    date, date_conf = extractor.extract_date(test_text, doc_type)
    client, client_conf = extractor.extract_client(test_text)
    
    print("=" * 60)
    print("ТЕСТ ИЗВЛЕЧЕНИЯ МЕТАДАННЫХ")
    print("-" * 60)
    print(f"Тип: {doc_type} (впевненість: {type_conf:.2%})")
    print(f"Номер: {number} (впевненість: {number_conf:.2%})")
    print(f"Дата: {date} (впевненість: {date_conf:.2%})")
    print(f"Клієнт: {client} (впевненість: {client_conf:.2%})")
    print("=" * 60)


if __name__ == "__main__":
    print("🚀 СИСТЕМА ПОШУКУ ДОКУМЕНТІВ v3.0")
    print("=" * 50)
    print("1. Інтерактивний пошук з експортом в Excel")
    print("2. Тест екстракції метаданих")
    print("3. Перебудувати індекс")
    print("=" * 50)
    
    choice = input("Оберіть опцію (1-3): ").strip()
    
    if choice == "1":
        interactive_search_with_export()
    elif choice == "2":
        test_extraction()
    elif choice == "3":
        search = DocumentSearchInterface()
        print("🔄 Перебудова індексу...")
        search.rebuild_index()
        print("✅ Індекс перебудовано!")
        search.close()
    else:
        print("❌ Невірний вибір")