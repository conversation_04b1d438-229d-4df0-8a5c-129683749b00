
# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from google import genai
from google.genai import types
from dotenv import load_dotenv
load_dotenv()

def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None



def generate(pdf_path):
    pdf_decoded = encode_pdf(pdf_path)
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-flash-preview-04-17"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
            ],
        ),
        types.Content(
            role="model",
            parts=[
                types.Part.from_text(text="""{}"""),
            ],
        ),
    ]
    generate_content_config = types.GenerateContentConfig(
        thinking_config = types.ThinkingConfig(
            thinking_budget=0,
        ),
        response_mime_type="application/json",
        response_schema=genai.types.Schema(
            type = genai.types.Type.OBJECT,
            required = ["documents"],
            properties = {
                "documents": genai.types.Schema(
                    type = genai.types.Type.ARRAY,
                    items = genai.types.Schema(
                        type = genai.types.Type.OBJECT,
                        required = ["doc_date", "doc_number", "buyer_name_short", "buyer_code", "reference_doc_number", "total_amount", "doc_page_type", "doc_type", "page_number"],
                        properties = {
                            "doc_date": genai.types.Schema( # type: ignore
                                type = genai.types.Type.STRING,
                                description="Дата в формате dd.mm.yyyy"
                            ),
                            "doc_number": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                                description="только число"
                            ),
                            "buyer_name_short": genai.types.Schema(
                                type = genai.types.Type.STRING,
                                description="Без кавычек и юридического статуса"
                            ),
                            "buyer_code": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                            ),
                            "reference_doc_number": genai.types.Schema(
                                type = genai.types.Type.ARRAY,
                                items = genai.types.Schema(
                                    type = genai.types.Type.INTEGER,
                                ),
                            ),
                            "total_amount": genai.types.Schema(
                                type = genai.types.Type.NUMBER,
                            ),
                            "doc_page_type": genai.types.Schema(
                                type = genai.types.Type.STRING,
                                enum = ["front_first", "middle", "last"],
                            ),
                            "doc_type": genai.types.Schema(
                                type = genai.types.Type.STRING,
                                enum = ["ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА",
                                        "ВИДАТКОВА НАКЛАДНА",
                                        "АКТ",
                                        "ПРИБУТКОВА НАКЛАДНА",
                                        "ДОВЕРЕННОСТЬ",
                                        "ПОВЕРНЕННАЯ ПОСТАВЩИКУ"],

                            ),
                            "page_number_in_file": genai.types.Schema(
                                type = genai.types.Type.INTEGER,
                                description = "Номер сторінки в файлі. Без повторов.",
                            ),
                        },
                    ),
                ),
            },
        ),
    )
    print(client.models.count_tokens(model=model, contents=contents))
    response = client.models.generate_content(model=model, contents=contents, config=generate_content_config)
    print(response.usage_metadata.total_token_count)
    return response.text


if __name__ == "__main__":
    pdf_path = r"c:\Scan\All\ForParse\2025-04-30_174333.pdf"
    result = generate(pdf_path)
    print(result)