import logging
import os
from os.path import exists
from pathlib import Path
import time
from collections import deque
from typing import List, Dict, Any, Optional
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# Синхронные библиотеки для I/O
import psycopg2
import psycopg2.extras

# CPU-bound библиотеки
import fitz  # PyMuPDF
from PIL import Image

from ExtractEmptyFiles import divide_documents, separate_pages_by_content_sync
# Импорты AI-функций (синхронные версии)
from Gemini.GeminiAI import extract_entity_from_page_by_gemini as extract_entity_from_file_sync
from prompt import PROMPT_OCR

# --- Константы и Настройки ---
MAX_WORKERS = 20
MAX_JPEG_SIZE = 90000
GEMINI_MAX_REQUESTS_PER_MINUTE = 50
MAX_RETRIES = 3
RETRY_BASE_DELAY = 2

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(threadName)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Глобальный потокобезопасный Rate Limiter
class RateLimiter:
    def __init__(self, max_requests_per_minute: int):
        self.max_requests = max_requests_per_minute
        self.request_times = deque()
        self.lock = threading.Lock()

    def acquire(self):
        with self.lock:
            while True:
                now = time.time()
                while self.request_times and now - self.request_times[0] > 60:
                    self.request_times.popleft()
                if len(self.request_times) < self.max_requests:
                    self.request_times.append(now)
                    return
                sleep_time = (self.request_times[0] + 60) - now + 0.1
                logger.info(f"Rate limit: ожидание {sleep_time:.1f} сек")
                self.lock.release()
                time.sleep(sleep_time)
                self.lock.acquire()

    def release_on_error(self):
        with self.lock:
            if self.request_times:
                self.request_times.pop()


gemini_rate_limiter = RateLimiter(GEMINI_MAX_REQUESTS_PER_MINUTE)

# --- Параметры подключения к БД ---
DB_PARAMS = {
    'host': os.getenv("PG_HOST_LOCAL", "localhost"),
    'database': os.getenv("PG_DBNAME", "postgres"),
    'user': os.getenv("PG_USER", "postgres"),
    'password': os.getenv("PG_PASSWORD", ""),
    'port': int(os.getenv("PG_PORT", "5432"))
}


def reprocess_and_update_row(row_data: Dict[str, Any]) -> bool:
    """
    Обрабатывает ОДНУ запись из БД и немедленно обновляет ее в случае успеха.
    Возвращает True при успехе, False при неудаче.
    """
    file_name = row_data['file_name']
    page_number = row_data['page_number']
    # Предполагаем, что файлы для переобработки лежат в определенной папке
    file_path = os.path.join(r"c:\Scan\All\AlreadyAddToDb\r", file_name)

    temp_file_name = None
    try:
        if not os.path.exists(file_path):
            logger.error(f"[Reprocess] Файл не найден: {file_path}")
            return False

        # Шаг 1: Подготовка изображения (как в process_page)
        ext = get_file_extension(file_path)
        if ext == 'pdf':
            temp_file_name = save_pdf_page_as_image(file_path, page_number)
        elif ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
            temp_file_name = file_path
        else:
            logger.warning(f"[Reprocess] Неподдерживаемый формат файла: {ext}")
            return False

        if not temp_file_name or not exists(temp_file_name):
            logger.error(f"[Reprocess] Не удалось создать временный файл для {file_name} стр.{page_number}")
            return False

        # Шаг 2: OCR
        ocr_text = safe_gemini_request(temp_file_name, PROMPT_OCR)

        # Шаг 3: Проверка результата и немедленное обновление в БД
        if ocr_text and 100 < len(ocr_text.strip()) < 10000:
            conn = None
            try:
                conn = psycopg2.connect(**DB_PARAMS)
                with conn.cursor() as cur:
                    sql = """
                        UPDATE t_scan_documents_raw
                        SET description = %s, created_at = now()
                        WHERE file_name = %s AND page_number = %s;
                    """
                    cur.execute(sql, (ocr_text.strip(), file_name, page_number))
                    conn.commit()
                logger.info(f"✅ Успешно обновлена запись: {file_name} стр.{page_number}")
                return True
            except psycopg2.Error as e:
                logger.error(f"[Reprocess] Ошибка БД при обновлении {file_name} стр.{page_number}: {e}")
                if conn: conn.rollback()
                return False
            finally:
                if conn: conn.close()
        else:
            logger.warning(f"[Reprocess] Не получен валидный текст для {file_name} стр.{page_number}")
            return False

    except Exception as e:
        logger.error(f"[Reprocess] Критическая ошибка при обработке {file_name} стр.{page_number}: {e}", exc_info=True)
        return False
    finally:
        if temp_file_name and get_file_extension(file_path) == 'pdf' and exists(temp_file_name):
            try:
                os.remove(temp_file_name)
            except OSError as e:
                logger.warning(f"Не удалось удалить временный файл {temp_file_name}: {e}")


def reprocess_empty_descriptions():
    """
    Находит записи с пустым description и запускает их параллельную переобработку.
    """
    rows_to_process = []
    conn = None
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            query = """
                SELECT file_name, page_number
                FROM t_scan_documents_raw
                WHERE coalesce(description,'') = '' AND file_name ILIKE '%vozvrat%'
                ORDER BY file_name, page_number;
            """
            cur.execute(query)
            rows_to_process = cur.fetchall()
    except psycopg2.Error as e:
        logger.error(f"Ошибка при получении данных из БД для переобработки: {e}")
        return
    finally:
        if conn:
            conn.close()

    if not rows_to_process:
        logger.info("Нет записей для повторной обработки.")
        return

    logger.info(f"Найдено {len(rows_to_process)} записей для повторной обработки.")

    successful = 0
    failed = 0
    with ThreadPoolExecutor(max_workers=MAX_WORKERS, thread_name_prefix='Reprocessor') as executor:
        # Создаем задачи для каждой строки
        future_to_row = {executor.submit(reprocess_and_update_row, row): row for row in rows_to_process}

        for future in as_completed(future_to_row):
            row_info = future_to_row[future]
            try:
                # future.result() вернет True или False из reprocess_and_update_row
                if future.result():
                    successful += 1
                else:
                    failed += 1
            except Exception as e:
                failed += 1
                logger.error(f"Исключение в потоке для {row_info['file_name']} стр.{row_info['page_number']}: {e}")

    logger.info(f"Повторная обработка завершена. Успешно обновлено: {successful}, с ошибками: {failed}")


def safe_gemini_request(file_path: str, prompt: str, max_retries: int = MAX_RETRIES) -> str:
    for attempt in range(max_retries):
        acquired = False
        try:
            gemini_rate_limiter.acquire()
            acquired = True
            logger.info(f"📡 Выполняется запрос к Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
            result = extract_entity_from_file_sync(file_path, prompt)
            if result and len(result.strip()) > 0:
                logger.info(f"✅ Успешный запрос к Gemini API для {Path(file_path).name}")
                return result
            else:
                logger.warning(f"⚠️ Пустой ответ от Gemini API для {Path(file_path).name}, попытка {attempt + 1}")
                gemini_rate_limiter.release_on_error()
                acquired = False
        except Exception as e:
            error_msg = str(e)
            if acquired:
                gemini_rate_limiter.release_on_error()
            if "429" in error_msg or "quota" in error_msg.lower():
                retry_delay = RETRY_BASE_DELAY * (2 ** attempt)
                logger.warning(f"🚫 Rate limit для Gemini API. Повтор через {retry_delay} сек.")
                time.sleep(retry_delay)
            else:
                logger.error(f"❌ Ошибка Gemini API для {Path(file_path).name}: {error_msg}")
                break
    logger.error(
        f"💥 Не удалось получить результат от Gemini API для {Path(file_path).name} после {max_retries} попыток")
    return ""


def get_file_extension(file_path: str) -> str:
    return Path(file_path).suffix.lower().lstrip('.')


def save_pdf_page_as_image(input_path: str, page_number: int, dpi: int = 600, jpeg_quality: int = 100,
                           max_size: int = MAX_JPEG_SIZE) -> Optional[str]:
    if not os.path.exists(input_path):
        logger.error(f"Входной файл не существует: {input_path}")
        return None
    output_dir = "temp_image"
    os.makedirs(output_dir, exist_ok=True)
    try:
        doc = fitz.open(input_path)
        try:
            if not (1 <= page_number <= len(doc)):
                raise ValueError(f"Страница {page_number} отсутствует в документе (всего {len(doc)} стр.).")
            page = doc.load_page(page_number - 1)
            pix = page.get_pixmap(dpi=dpi)
            img = Image.frombytes("RGB", (pix.width, pix.height), pix.samples)
            if pix.width > max_size or pix.height > max_size:
                scale = min(max_size / pix.width, max_size / pix.height)
                new_size = (int(pix.width * scale), int(pix.height * scale))
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            base_name = f"{Path(input_path).stem}_page_{page_number}.jpg"
            img_path = os.path.join(output_dir, base_name)
            img.save(img_path, format="JPEG", quality=jpeg_quality)
            return img_path
        finally:
            doc.close()
    except Exception as e:
        logger.error(f"Ошибка при сохранении страницы {page_number} из файла {input_path}: {e}", exc_info=True)
        return None


def add_to_db(data: List[Dict[str, Any]]):
    valid_data = [doc for doc in data if doc and doc.get('description')]
    if not valid_data:
        return 0
    sql = """
        INSERT INTO t_scan_documents_raw (full_path, page_number, description, file_name, created_at)
        VALUES (%s, %s, %s, %s, now())
        ON CONFLICT (file_name, page_number) DO UPDATE
        SET description = EXCLUDED.description,
            full_path = EXCLUDED.full_path,
            created_at = now();
    """
    values = [(doc.get('full_path'), doc.get('page_number'), doc.get('description'), doc.get('file_name')) for doc in
              valid_data]
    conn = None
    try:
        conn = psycopg2.connect(**DB_PARAMS)
        with conn.cursor() as cur:
            psycopg2.extras.execute_batch(cur, sql, values)
            conn.commit()
        logger.info(f"Данные OCR: успешно сохранено/обновлено {len(values)} записей.")
        return len(values)
    except psycopg2.Error as e:
        logger.error(f"Критическая ошибка при работе с БД: {e}", exc_info=True)
        if conn: conn.rollback()
        return 0
    finally:
        if conn: conn.close()


def process_page(pdf_or_image_path: str, page_number: int, repeat: int = 3) -> Dict[str, Any]:
    data = {'full_path': pdf_or_image_path, 'page_number': page_number, 'description': None,
            'file_name': Path(pdf_or_image_path).name}
    temp_file_name = None
    try:
        if not os.path.exists(pdf_or_image_path):
            logger.warning(f"Файл для обработки не найден: {pdf_or_image_path}")
            return {}
        ext = get_file_extension(pdf_or_image_path)
        if ext in ['png', 'jpg', 'jpeg', 'bmp', 'tiff']:
            temp_file_name = pdf_or_image_path
        elif ext == 'pdf':
            temp_file_name = save_pdf_page_as_image(pdf_or_image_path, page_number)
        else:
            return {}
        if not temp_file_name or not exists(temp_file_name):
            logger.error("Не удалось создать временный файл изображения.")
            return {}
        ocr_text = safe_gemini_request(temp_file_name, PROMPT_OCR)
        if ocr_text and 100 < len(ocr_text.strip()) < 10000:
            data['description'] = ocr_text.strip()
            return data
        elif repeat > 0 and (not ocr_text or len(ocr_text.strip()) <= 100):
            logger.info(
                f"🔄 Повторная обработка для {Path(pdf_or_image_path).name} стр.{page_number} (осталось {repeat - 1} попыток)")
            return process_page(pdf_or_image_path, page_number, repeat - 1)
        else:
            return {}
    except Exception as e:
        logger.error(f"Критическая ошибка при обработке {pdf_or_image_path} стр.{page_number}: {e}", exc_info=True)
        return {}
    finally:
        if temp_file_name and get_file_extension(pdf_or_image_path) == 'pdf' and exists(temp_file_name):
            try:
                os.remove(temp_file_name)
            except OSError:
                pass


def extract_pages_from_pdf(pdf_or_image_path: str, page_numbers: Optional[List[int]] = None):
    if not os.path.exists(pdf_or_image_path):
        logger.error(f"Файл не найден: {pdf_or_image_path}")
        return
    if not page_numbers:
        try:
            doc = fitz.open(pdf_or_image_path)
            total_pages = len(doc)
            doc.close()
            page_numbers = list(range(1, total_pages + 1))
        except Exception as e:
            logger.error(f"Не удалось прочитать PDF файл {pdf_or_image_path}: {e}")
            return
    results_to_db = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS, thread_name_prefix='PageProcessor') as executor:
        future_to_page = {executor.submit(process_page, pdf_or_image_path, page_num): page_num for page_num in
                          page_numbers}
        for future in as_completed(future_to_page):
            try:
                result = future.result()
                if result: results_to_db.append(result)
            except Exception as e:
                logger.error(f"❌ Страница {future_to_page[future]} - ошибка в потоке: {e}")
    if results_to_db:
        add_to_db(results_to_db)


def extract_pdf_files_from_folder(folder_path: str):
    pdf_files = []
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                abs_path = os.path.abspath(os.path.join(root, file))
                if 'data.pdf' not in file.lower() and 'empty.pdf' not in file.lower():
                    abs_path, _ = separate_pages_by_content_sync(abs_path)
                    if not abs_path:
                        continue
                pdf_files.append(abs_path)
    logger.info(f"Найдено {len(pdf_files)} PDF файлов для обработки")
    for i, pdf_file in enumerate(pdf_files):
        logger.info(f"--- Обрабатываем файл {i + 1}/{len(pdf_files)}: {Path(pdf_file).name} ---")
        try:
            extract_pages_from_pdf(pdf_file)
        except Exception as e:
            logger.error(f"❌ Не удалось обработать файл {Path(pdf_file).name}: {e}")


def main():
    """Главная синхронная функция."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    logger.info("=== Запуск системы обработки документов (Многопоточный режим) ===")
    logger.info(f"Максимальное количество потоков: {MAX_WORKERS}")
    start_time = time.time()

    try:
        # --- Выберите один из вариантов для запуска ---

        # Вариант 1. Обработка всех pdf в папке с учетом вложенных.
        logger.info("Запуск варианта 1: Обработка всех PDF в папке...")
        folder_path = r"c:\Scan\All\AlreadyAddToDb\r"
        extract_pdf_files_from_folder(folder_path)

        # Вариант 2. Обработка одного pdf с указанием списка страниц.
        # logger.info("Запуск варианта 2: Обработка одного PDF с указанием страниц...")
        # empty_pages = [
        #      {'pdf_file_path': r'c:\Scan\All\AlreadyAddToDb\2024.10 Vozvrati 3.pdf', 'page_numbers': [ 6, 10, 14, 15, 18 ]},
        # ]
        # for item in empty_pages:
        #      pdf_file_path_raw = item['pdf_file_path']
        #      page_numbers = item['page_numbers']
        #      logger.info(f"Обработка файла: {Path(pdf_file_path_raw).name} для страниц: {page_numbers}")
        #      pdf_file_path, _ = divide_documents(pdf_file_path_raw)
        #      if pdf_file_path:
        #          extract_pages_from_pdf(pdf_file_path, page_numbers)

        # Вариант 3. Обработка одного pdf БЕЗ указания списка страниц.
        # logger.info("Запуск варианта 3: Обработка всех страниц одного PDF...")
        # pdf_to_process = r"c:\Scan\All\AlreadyAddToDb\2024.10 Vozvrati 1.pdf"
        # extract_pages_from_pdf(pdf_to_process)

        # Вариант 4. Повторная обработка записей из БД.
        # logger.info("Запуск варианта 4: Повторная обработка данных из БД...")
        # reprocess_empty_descriptions()

    except KeyboardInterrupt:
        logger.info("Получен сигнал прерывания. Завершение работы...")
    except Exception as e:
        logger.error(f"Критическая ошибка в main: {e}", exc_info=True)
    finally:
        elapsed_time = time.time() - start_time
        logger.info(f"Все операции завершены. Время выполнения: {elapsed_time:.2f} секунд")


if __name__ == "__main__":
    main()