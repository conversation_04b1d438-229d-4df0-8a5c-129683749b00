# -*- coding: utf-8 -*-
"""
Скрипт для обработки PDF файлов с помощью Google Document AI.
Поддерживает обработку больших файлов (>40МБ) путем разделения на страницы.
"""

import os
import json
import asyncio
import sys
import logging
from typing import Dict, Optional, Any, List
from datetime import datetime

# --- НАСТРОЙКА ---
# Укажите путь к папке с PDF-файлами, которые нужно обработать
PDF_SOURCE_DIRECTORY = r"C:\Scan\All\AlreadyAddToDb\t"

# Настройки обработки
MAX_CONCURRENT_FILES = 1  # Количество одновременно обрабатываемых файлов
MAX_CONCURRENT_PAGES = 10  # Количество одновременно обрабатываемых страниц в одном файле
OPTIMIZE_LARGE_PAGES = True  # Оптимизировать ли слишком большие страницы PDF
CREATE_ERROR_LOG = True  # Создавать ли лог-файл с ошибками

# --- КОНЕЦ НАСТРОЙКИ ---

# Настройка логирования
log_format = '%(asctime)s - %(levelname)s - %(message)s'
logging.basicConfig(level=logging.WARNING, format=log_format)
logger = logging.getLogger(__name__)

# Добавляем файловый обработчик для логов
error_log_path = None
if CREATE_ERROR_LOG:
    error_log_path = os.path.join(PDF_SOURCE_DIRECTORY, f"processing_errors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    try:
        file_handler = logging.FileHandler(error_log_path, encoding='utf-8')
        file_handler.setLevel(logging.WARNING)
        file_handler.setFormatter(logging.Formatter(log_format))
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Не удалось создать лог-файл: {e}")
        CREATE_ERROR_LOG = False

# Добавляем корневую папку проекта в путь
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(current_dir)
if project_root not in sys.path:
    sys.path.append(project_root)

try:
    from Gemini.GoogleDocumentAIAsync import process_pdf_pages_async, check_file_size
    import aiofiles
    import fitz  # PyMuPDF
except ImportError as e:
    logger.error(f"Ошибка импорта: {e}")
    sys.exit(1)


def format_file_size(size_bytes: int) -> str:
    """Форматирует размер файла в человекочитаемый вид."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} TB"


async def analyze_pdf_file(pdf_path: str) -> Dict[str, Any]:
    """
    Анализирует PDF файл и возвращает информацию о нем.
    """
    try:
        file_size = os.path.getsize(pdf_path)
        doc = await asyncio.to_thread(fitz.open, pdf_path)
        page_count = len(doc)
        doc.close()
        
        return {
            "file_name": os.path.basename(pdf_path),
            "file_size": file_size,
            "file_size_formatted": format_file_size(file_size),
            "page_count": page_count,
            "requires_splitting": file_size > 41943040,  # 40 MB
        }
    except Exception as e:
        logger.error(f"Ошибка при анализе файла {pdf_path}: {e}")
        return None


async def process_single_pdf(pdf_path: str, semaphore: asyncio.Semaphore) -> Optional[str]:
    """
    Обрабатывает один PDF-файл с учетом его размера.
    """
    async with semaphore:
        if not os.path.exists(pdf_path):
            logger.error(f"Файл не найден: {pdf_path}")
            return None
        
        file_name = os.path.basename(pdf_path)
        
        # Анализируем файл
        file_info = await analyze_pdf_file(pdf_path)
        if file_info and file_info['requires_splitting']:
            logger.warning(f"Файл {file_name} превышает лимит 40МБ, будет обработан постранично")
        
        try:
            # Используем универсальную функцию, которая автоматически определит стратегию
            page_texts = await process_pdf_pages_async(
                pdf_path=pdf_path,
                max_concurrent=MAX_CONCURRENT_PAGES
            )
            
            if not page_texts:
                logger.error(f"Не удалось извлечь текст из: {file_name}")
                return None
            
            # Подсчитываем статистику
            successful_pages = [p for p, text in page_texts.items() if text and not text.startswith("[")]
            failed_pages = [p for p, text in page_texts.items() if not text or text.startswith("[")]
            
            if failed_pages:
                logger.warning(f"Не удалось обработать страницы в файле {file_name}: {failed_pages}")
            
            # Формируем данные для JSON
            output_data = []
            total_chars = 0
            
            for page_number, description in sorted(page_texts.items()):
                text = description.strip() if description else ""
                total_chars += len(text)
                
                record = {
                    "file_name": file_name,
                    "page_number": page_number,
                    "description": text,
                }
                output_data.append(record)
            
            # Определяем имя выходного файла
            output_path = pdf_path.replace(".pdf", ".json")
            
            # Сохраняем результат
            async with asyncio.Lock():
                async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(output_data, ensure_ascii=False, indent=2))
            
            return output_path
            
        except Exception as e:
            logger.error(f"Критическая ошибка при обработке {file_name}: {e}", exc_info=True)
            return None


async def process_batch(pdf_files: List[str], batch_num: int, total_batches: int) -> List[Optional[str]]:
    """
    Обрабатывает пакет PDF файлов.
    """
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_FILES)
    tasks = [process_single_pdf(pdf_path, semaphore) for pdf_path in pdf_files]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Обрабатываем результаты
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Необработанная ошибка для файла {os.path.basename(pdf_files[i])}: {result}")
            processed_results.append(None)
        else:
            processed_results.append(result)
    
    return processed_results


async def main():
    """
    Главная функция: находит все PDF в директории и запускает их обработку.
    """
    print("\n" + "="*70)
    print(" PDF DOCUMENT AI PROCESSOR")
    print("="*70)
    
    # Проверяем директорию
    if not os.path.isdir(PDF_SOURCE_DIRECTORY):
        logger.error(f"Директория не существует: {PDF_SOURCE_DIRECTORY}")
        return
    
    # Находим PDF файлы
    pdf_files = []
    for file in os.listdir(PDF_SOURCE_DIRECTORY):
        if file.lower().endswith('.pdf'):
            pdf_path = os.path.join(PDF_SOURCE_DIRECTORY, file)
            pdf_files.append(pdf_path)
    
    if not pdf_files:
        logger.warning(f"В папке {PDF_SOURCE_DIRECTORY} не найдено PDF-файлов")
        return
    
    print(f"Найдено PDF файлов: {len(pdf_files)}")

    start_time = datetime.now()
    
    # Обрабатываем файлы пакетами
    batch_size = MAX_CONCURRENT_FILES * 2
    total_batches = (len(pdf_files) + batch_size - 1) // batch_size
    
    all_results = []
    for i in range(0, len(pdf_files), batch_size):
        batch = pdf_files[i:i + batch_size]
        batch_results = await process_batch(batch, (i // batch_size) + 1, total_batches)
        all_results.extend(batch_results)
    
    # Подводим итоги
    end_time = datetime.now()
    duration = end_time - start_time
    
    successful = [r for r in all_results if r is not None]
    failed = [pdf_files[i] for i, r in enumerate(all_results) if r is None]
    
    print("\n" + "="*70)
    print(" ИТОГИ ОБРАБОТКИ")
    print("="*70)
    print(f"Успешно обработано: {len(successful)}/{len(pdf_files)} файлов")
    if failed:
        print(f"Не удалось обработать: {len(failed)} файлов")
        for pdf_path in failed[:10]:
            print(f"   - {os.path.basename(pdf_path)}")
        if len(failed) > 10:
            print(f"   ... и еще {len(failed) - 10} файлов")
    
    print(f"\nВремя обработки: {duration.total_seconds():.1f} сек")
    
    if CREATE_ERROR_LOG and error_log_path and os.path.exists(error_log_path):
        print(f"Лог ошибок сохранен: {error_log_path}")
    
    print("\nОбработка завершена!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nОбработка прервана пользователем")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Критическая ошибка: {e}", exc_info=True)
        sys.exit(1)