# -*- coding: utf-8 -*-
"""
Диагностический скрипт для анализа данных из базы данных
"""

import os
import pandas as pd
import psycopg2
from dotenv import load_dotenv

load_dotenv()

# Конфигурация из .env
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_HOST = os.getenv("PG_HOST_LOCAL")
PG_PORT = os.getenv("PG_PORT")
PG_DBNAME = os.getenv("PG_DBNAME")

def analyze_data():
    conn_string = f"dbname='{PG_DBNAME}' user='{PG_USER}' host='{PG_HOST}' password='{PG_PASSWORD}' port='{PG_PORT}'"
    
    try:
        with psycopg2.connect(conn_string) as conn:
            print("Успешное подключение к базе данных.")
            
            # Тот же SQL запрос, что и в основном коде
            sql = """
                SELECT 
                    doc_type,
                    doc_date,
                    doc_number,
                    buyer_name,
                    buyer_code,
                    page_number,
                    page_type,
                    invoices_numbers,
                    file_name
                FROM 
                    t_scan_documents
                WHERE doc_date > '01.01.2025' AND buyer_code = '32294905'
                ORDER BY
                    doc_type, doc_date, doc_number, buyer_name, buyer_code, invoices_numbers,
                    page_type ASC, page_number ASC     
                ;
            """
            
            df = pd.read_sql_query(sql, conn)
            print(f"Загружено {len(df)} записей из t_scan_documents.")
            
            # Анализируем конкретные документы
            df['doc_date'] = pd.to_datetime(df['doc_date'])
            
            # Группировка как в основном коде
            grouping_keys = ['doc_type', 'doc_date', 'doc_number', 'buyer_name', 'buyer_code']
            grouped = df.groupby(grouping_keys, sort=False)
            
            print(f"\nНайдено {len(grouped)} уникальных документов.")
            
            # Анализируем только проблемные документы
            problem_docs = []
            target_docs = ["ТТН 221", "ТТН 144", "ТТН 106"]  # Примеры для анализа

            for name, group in grouped:
                doc_type, doc_date, doc_number, buyer_name, buyer_code = name
                formatted_date = doc_date.strftime('%d %m %Y')
                doc_key = f"{doc_type} №{doc_number} от {formatted_date}"

                # Проверяем файлы
                unique_files = group['file_name'].unique()
                unique_page_types = group['page_type'].unique()

                # Анализируем только целевые документы или проблемные
                is_target = any(target in doc_key for target in target_docs)
                has_multiple_files = len(unique_files) > 1
                has_multiple_pages = len(group) > 1

                if is_target or has_multiple_files or has_multiple_pages:
                    print(f"\n=== {doc_key} ===")
                    print(f"Всего записей: {len(group)}")
                    print(f"Уникальные файлы: {len(unique_files)} - {list(unique_files)}")
                    print(f"Типы страниц: {sorted(unique_page_types)}")

                    # Показываем детали каждой записи
                    for idx, row in group.iterrows():
                        print(f"  - file: {row['file_name']}, page: {row['page_number']}, type: {row['page_type']}")

                    # Проверяем проблемные случаи
                    if len(unique_files) > 1:
                        problem_docs.append((doc_key, "Множественные файлы", unique_files))
                        print(f"  ⚠️  ПРОБЛЕМА: Страницы из разных файлов!")

                    # Особое внимание к ТТН 221
                    if "ТТН" in doc_key and "221" in doc_key:
                        print(f"  🔍 ОСОБОЕ ВНИМАНИЕ: ТТН 221")
                        print(f"     Ожидаемые page_type: 1, 3, 999")
                        print(f"     Фактические page_type: {sorted(unique_page_types)}")

            # Подсчитываем статистику
            multi_file_count = 0
            multi_page_count = 0

            for name, group in grouped:
                unique_files = group['file_name'].unique()
                if len(unique_files) > 1:
                    multi_file_count += 1
                if len(group) > 1:
                    multi_page_count += 1
            
            print(f"\n=== СВОДКА СТАТИСТИКИ ===")
            print(f"Всего документов: {len(grouped)}")
            print(f"Документы с множественными файлами: {multi_file_count}")
            print(f"Документы с множественными страницами: {multi_page_count}")

            print(f"\n=== СВОДКА ПРОБЛЕМ ===")
            if problem_docs:
                for doc_key, problem, details in problem_docs:
                    print(f"❌ {doc_key}: {problem} - {details}")
            else:
                print("✅ Проблем с множественными файлами не найдено")
                
    except Exception as e:
        print(f"Ошибка: {e}")

if __name__ == "__main__":
    analyze_data()
