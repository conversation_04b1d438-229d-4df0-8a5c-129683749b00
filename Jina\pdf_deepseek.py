# -*- coding: utf-8 -*-
# pip install tensorflow-hub scikit-learn pandas openpyxl psycopg2-binary python-dotenv openai

import psycopg2
import numpy as np
import pandas as pd
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import normalize
import re
from datetime import datetime
from dotenv import load_dotenv
import os
from typing import List, Dict, Any, Optional, Tuple
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from openpyxl.utils import get_column_letter
import tempfile
import json
from openai import OpenAI
import asyncio
import aiohttp

# Загрузка переменных окружения
load_dotenv()

class DocumentExtractionValidator:
    """Класс для валидации и сравнения извлеченных данных"""
    
    def __init__(self, deepseek_api_key: Optional[str] = None):
        self.deepseek_client = None
        if deepseek_api_key:
            self.deepseek_client = OpenAI(
                api_key=deepseek_api_key,
                base_url="https://api.deepseek.com"
            )
    
    def enhanced_document_type_detection(self, text: str) -> str:
        """Улучшенное определение типа документа с более точными паттернами"""
        text_lower = text.lower().strip()
        
        # Специфичные паттерны для точного определения (порядок важен!)
        type_patterns = [
            # Возврат товаров - самый приоритетный
            (r'накладн[ая][я]?\s+на\s+поверненн[яя]|поверненн[я]\s+товарів|поверненн[я]\s+постачальнику', 'Накладна на повернення'),
            (r'заявк[ая]\s+на\s+поверненн[яя]', 'Заявка на повернення'),
            
            # ТТН с возвратом
            (r'товарно[-\s]транспортн[ая][я]?\s+накладн[ая][я]?.*поверненн[яя]', 'ТТН на повернення'),
            
            # Конкретные акты (НЕ общий "акт")
            (r'акт\s+прийом[ау][-\s]передач[іи]', 'Акт прийому-передачі'),
            (r'акт\s+приймання', 'Акт приймання'),
            (r'акт\s+поверненн[яя]', 'Акт повернення'),
            (r'акт\s+звір[кя]и', 'Акт звірки'),
            (r'акт\s+виконаних\s+робіт', 'Акт виконаних робіт'),
            (r'акт\s+№\s*\d+', 'Акт'),  # Только если есть номер
            
            # ТТН и связанные документы
            (r'товарно[-\s]транспортн[ая][я]?\s+накладн[ая][я]?', 'ТТН'),
            (r'^ттн\s|\sттн\s|\sттн$', 'ТТН'),
            (r'відомост[і]\s+про\s+вантаж', 'ТТН'),
            (r'транспортн[ая][я]?\s+накладн[ая][я]?', 'ТТН'),
            
            # Рахунки
            (r'рахун[ок][ок]?[-\s]фактура?', 'Рахунок-фактура'),
            (r'рахун[ок][ок]?\s+№', 'Рахунок'),
            
            # Конкретные накладные
            (r'видаткова\s+накладн[ая][я]?|расходная\s+накладн[ая][я]?', 'Видаткова накладна'),
            (r'прибуткова\s+накладн[ая][я]?|приходная\s+накладн[ая][я]?', 'Прибуткова накладна'),
            (r'накладн[ая][я]?\s+№', 'Накладна'),
            
            # Другие конкретные документы
            (r'догов[іо]р\s+№|договір\s+про', 'Договір'),
            (r'протокол\s+№|протокол\s+від', 'Протокол'),
            (r'звіт\s+про|звіт\s+№', 'Звіт'),
            (r'довіренність|доверенность', 'Довіренність'),
        ]
        
        for pattern, doc_type in type_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return doc_type
                
        return 'Документ'
    
    def enhanced_extract_document_number(self, text: str) -> str:
        """Улучшенное извлечение номера документа"""
        # Паттерны для номеров с приоритетом
        number_patterns = [
            r'№\s*([A-ZА-Я0-9\-/]{2,20})',  # Стандартный номер после №
            r'номер[:\s]*([A-ZА-Я0-9\-/]{2,20})',  # После слова "номер"
            r'документ[а]?\s+№\s*([A-ZА-Я0-9\-/]{2,20})',  # После "документ №"
            r'([A-Z]{2,}/[A-Z0-9\-]+/[A-Z0-9\-]+)',  # Составные номера типа УК/12345/ВН
            r'\b([0-9]{4,10})\b',  # Простые числовые номера 4-10 цифр
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        number = match[0]
                    else:
                        number = match
                    
                    # Проверяем, что это не дата и не служебная информация
                    if (len(number.strip()) >= 2 and 
                        not any(word in number.lower() for word in ['від', 'дата', 'стор', 'page', 'сторінка']) and
                        not re.match(r'^\d{1,2}[./]\d{1,2}[./]\d{2,4}$', number)):
                        return number.strip()
        
        return 'Не вказано'
    
    def enhanced_extract_document_date(self, text: str) -> str:
        """Улучшенное извлечение даты документа"""
        # Словарь украинских месяцев
        months_ua = {
            'січня': '01', 'лютого': '02', 'березня': '03', 'квітня': '04',
            'травня': '05', 'червня': '06', 'липня': '07', 'серпня': '08',
            'вересня': '09', 'жовтня': '10', 'листопада': '11', 'грудня': '12',
            # Альтернативные написания с латинской 'i'
            'сiчня': '01', 'квiтня': '04', 'лiпня': '07', 'лiстопада': '11',
            # Русские названия месяцев
            'января': '01', 'февраля': '02', 'марта': '03', 'апреля': '04',
            'мая': '05', 'июня': '06', 'июля': '07', 'августа': '08',
            'сентября': '09', 'октября': '10', 'ноября': '11', 'декабря': '12'
        }
        
        # Паттерны для дат (от самых специфичных к общим)
        date_patterns = [
            # Дата после "від" с разными форматами
            (r'від\s+(\d{1,2})\s+([а-яі]+)\s+(\d{4})\s*р?\.?', 'dmy_month_word'),
            (r'від\s+(\d{1,2})[./](\d{1,2})[./](\d{4})', 'dmy_numeric'),
            (r'від\s+(\d{1,2})[./](\d{1,2})[./](\d{2})', 'dmy_numeric_short'),
            
            # Дата после "дата"
            (r'дата[:\s]+(\d{1,2})\s+([а-яі]+)\s+(\d{4})', 'dmy_month_word'),
            (r'дата[:\s]+(\d{1,2})[./](\d{1,2})[./](\d{4})', 'dmy_numeric'),
            
            # Дата в начале строки или после номера
            (r'№\s*\d+\s+від\s+(\d{1,2})\s+([а-яі]+)\s+(\d{4})', 'dmy_month_word'),
            (r'№\s*\d+\s+від\s+(\d{1,2})[./](\d{1,2})[./](\d{4})', 'dmy_numeric'),
            
            # Общие паттерны дат в тексте
            (r'\b(\d{1,2})\s+([а-яі]+)\s+(\d{4})\s*р?\.?', 'dmy_month_word'),
            (r'\b(\d{1,2})[./](\d{1,2})[./](\d{4})\b', 'dmy_numeric'),
            
            # Дата в формате YYYY-MM-DD или DD-MM-YYYY
            (r'\b(\d{4})[-./](\d{1,2})[-./](\d{1,2})\b', 'ymd_numeric'),
            (r'\b(\d{1,2})[-./](\d{1,2})[-./](\d{2})\b', 'dmy_numeric_short'),
        ]
        
        for pattern, format_type in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                try:
                    if format_type == 'dmy_numeric':
                        day, month, year = match.groups()
                        if 1 <= int(day) <= 31 and 1 <= int(month) <= 12:
                            return f"{day.zfill(2)}.{month.zfill(2)}.{year}"
                    elif format_type == 'dmy_numeric_short':
                        day, month, year = match.groups()
                        full_year = f"20{year}" if len(year) == 2 else year
                        if 1 <= int(day) <= 31 and 1 <= int(month) <= 12:
                            return f"{day.zfill(2)}.{month.zfill(2)}.{full_year}"
                    elif format_type == 'dmy_month_word':
                        day, month_word, year = match.groups()
                        month_num = months_ua.get(month_word.lower(), '00')
                        if month_num != '00' and 1 <= int(day) <= 31:
                            return f"{day.zfill(2)}.{month_num}.{year}"
                    elif format_type == 'ymd_numeric':
                        year, month, day = match.groups()
                        if 1 <= int(day) <= 31 and 1 <= int(month) <= 12:
                            return f"{day.zfill(2)}.{month.zfill(2)}.{year}"
                except (ValueError, IndexError):
                    continue
        
        # Попытка найти хотя бы год в тексте
        year_match = re.search(r'\b(20\d{2})\b', text)
        if year_match:
            year = year_match.group(1)
            # Пытаемся найти месяц рядом с годом
            context = text[max(0, year_match.start()-50):year_match.end()+50]
            for month_word, month_num in months_ua.items():
                if month_word in context.lower():
                    return f"01.{month_num}.{year}"  # Примерная дата
        
        return 'Не вказано'
    
    def enhanced_extract_client_name(self, text: str) -> str:
        """Улучшенное извлечение имени клиента"""
        text_lower = text.lower()
        
        # Известные крупные клиенты (точное совпадение)
        known_clients = {
            r'епіцентр|эпицентр|epicenter|епицентр': 'ЕПІЦЕНТР',
            r'метро|metro': 'МЕТРО', 
            r'ашан|auchan|аучан': 'АШАН',
            r'атб|atb': 'АТБ',
            r'сільпо|sіlpo|silpo|сильпо': 'СІЛЬПО',
            r'нов[ая][\s]+пошта|nova[\s]+poshta|нова[\s]+пошта': 'НОВА ПОШТА',
            r'розетка|rozetka': 'РОЗЕТКА',
            r'фоззі|fozzy|фоззи': 'ФОЗЗІ'
        }
        
        for pattern, client_name in known_clients.items():
            if re.search(pattern, text_lower, re.IGNORECASE):
                return client_name
        
        # Паттерны для извлечения названий организаций
        client_patterns = [
            # Основные поля для клиентов
            r'отримувач[:\s]*([^\n,;]{5,60})',
            r'получатель[:\s]*([^\n,;]{5,60})',
            r'відправник[:\s]*([^\n,;]{5,60})',
            r'отправитель[:\s]*([^\n,;]{5,60})',
            r'контрагент[:\s]*([^\n,;]{5,60})',
            r'клієнт[:\s]*([^\n,;]{5,60})',
            r'клиент[:\s]*([^\n,;]{5,60})',
            r'постачальник[:\s]*([^\n,;]{5,60})',
            
            # Организационные формы
            r'товариство[:\s\"]*([^\"\n,;]{5,60})',
            r'тов[.\s]*[\"\'\s]*([^\"\'н,;]{5,60})[\"\'\s]*',
            r'ооо[\s]*[\"\'\s]*([^\"\'н,;]{5,60})[\"\'\s]*',
            r'ТОВ[\s]*[\"\'\s]*([^\"\'н,;]{5,60})[\"\'\s]*',
            r'ООО[\s]*[\"\'\s]*([^\"\'н,;]{5,60})[\"\'\s]*',
            
            # Общие паттерны для организаций
            r'«([^\n,;]{3,50})»',  # Кавычки «»
            r'\"([^\"\n,;]{3,50})\"',  # Обычные кавычки
            r"\'([^\'\n,;]{3,50})\'"   # Одинарные кавычки
        ]
        
        for pattern in client_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                client_name = match.group(1).strip().strip('\"\'').strip()
                # Проверяем, что это не служебная информация
                if (len(client_name) > 4 and 
                    not any(word in client_name.lower() for word in 
                           ['накладна', 'рахунок', 'акт', 'документ', 'ттн', 'дата', 'номер',
                            'від', 'обмеженою', 'відповідальністю', 'код']) and
                    not re.match(r'^[\d\s.,/-]+$', client_name)):
                    # Очищаем от лишних слов
                    client_name = re.sub(r'\s*код\s*\d+.*$', '', client_name, flags=re.IGNORECASE)
                    client_name = re.sub(r'\s*інн\s*\d+.*$', '', client_name, flags=re.IGNORECASE)
                    client_name = re.sub(r'\s*єдрпоу\s*\d+.*$', '', client_name, flags=re.IGNORECASE)
                    return client_name.strip()
        
        # Попытка найти короткие названия известных компаний
        short_names = {
            r'\bКМ\b': 'КМ (Кавказ Мол)',
            r'ПРЕСТИЖ': 'ПРЕСТИЖ ПРОДУКТ',
            r'ЭПИЦЕНТР': 'ЕПІЦЕНТР'
        }
        
        for pattern, full_name in short_names.items():
            if re.search(pattern, text, re.IGNORECASE):
                return full_name
        
        return 'Невідомий клієнт'
    
    async def validate_with_deepseek(self, text: str) -> Dict[str, Any]:
        """Валидация данных с помощью DeepSeek AI с таймаутом"""
        if not self.deepseek_client:
            print("⚠️ DeepSeek клиент не инициализирован")
            return {}
            
        prompt = f"""
        Проанализируй текст документа и извлеки следующую информацию в формате JSON:
        
        {{
            "document_type": "тип документа (ТТН, Накладна, Акт, Рахунок и т.д.)",
            "document_number": "номер документа",
            "document_date": "дата в формате DD.MM.YYYY",
            "client_name": "название клиента/контрагента"
        }}
        
        Текст документа:
        {text[:2000]}  # Ограничиваем длину текста
        
        Отвечай только в формате JSON, без дополнительных комментариев.
        """
        
        try:
            print("🔄 Отправляю запрос к DeepSeek API...")
            import concurrent.futures
            loop = asyncio.get_event_loop()
            
            # Добавляем таймаут 30 секунд
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    lambda: self.deepseek_client.chat.completions.create(
                        model="deepseek-chat",
                        messages=[
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.1,
                        timeout=30  # Таймаут 30 секунд
                    )
                )
                
                # Ждем с таймаутом
                try:
                    response = await asyncio.wait_for(
                        loop.run_in_executor(None, future.result),
                        timeout=35  # Общий таймаут 35 секунд
                    )
                except asyncio.TimeoutError:
                    print("⏰ Таймаут запроса к DeepSeek API (35 сек)")
                    future.cancel()
                    return {}
            
            response_content = response.choices[0].message.content
            if response_content and response_content.strip():
                print("✅ Получен ответ от DeepSeek API")
                # Пытаемся очистить ответ от возможных префиксов
                clean_content = response_content.strip()
                if clean_content.startswith('```json'):
                    clean_content = clean_content[7:]
                if clean_content.endswith('```'):
                    clean_content = clean_content[:-3]
                clean_content = clean_content.strip()
                
                ai_result = json.loads(clean_content)
                return ai_result
            else:
                print("⚠️ DeepSeek вернул пустой ответ")
                return {}
            
        except asyncio.TimeoutError:
            print("⏰ Превышен таймаут запроса к DeepSeek API")
            return {}
        except json.JSONDecodeError as e:
            print(f"❌ Ошибка парсинга JSON от DeepSeek: {e}")
            return {}
        except Exception as e:
            print(f"❌ Общая ошибка при валидации с DeepSeek: {e}")
            return {}
    
    def compare_and_validate(self, script_data: Dict[str, str], ai_data: Dict[str, str], 
                           original_text: str) -> Dict[str, Any]:
        """Сравнение и валидация данных из скрипта и AI"""
        validation_result = {
            'script_extraction': script_data,
            'ai_extraction': ai_data,
            'final_result': {},
            'confidence_scores': {},
            'conflicts': [],
            'validation_notes': []
        }
        
        # Сравниваем каждое поле
        fields = ['type', 'number', 'date', 'client']
        ai_fields = ['document_type', 'document_number', 'document_date', 'client_name']
        
        for script_field, ai_field in zip(fields, ai_fields):
            script_value = script_data.get(script_field, 'Не вказано')
            ai_value = ai_data.get(ai_field, 'Не вказано')
            
            # Нормализуем значения для сравнения
            script_norm = self.normalize_value(script_value)
            ai_norm = self.normalize_value(ai_value)
            
            if script_norm == ai_norm:
                # Полное совпадение
                validation_result['final_result'][script_field] = script_value
                validation_result['confidence_scores'][script_field] = 1.0
            elif script_norm == 'не вказано' and ai_norm != 'не вказано':
                # AI нашел значение, скрипт - нет
                validation_result['final_result'][script_field] = ai_value
                validation_result['confidence_scores'][script_field] = 0.8
                validation_result['conflicts'].append(f"{script_field}: скрипт не нашел, AI нашел '{ai_value}'")
            elif ai_norm == 'не вказано' and script_norm != 'не вказано':
                # Скрипт нашел, AI - нет
                validation_result['final_result'][script_field] = script_value
                validation_result['confidence_scores'][script_field] = 0.7
                validation_result['conflicts'].append(f"{script_field}: AI не нашел, скрипт нашел '{script_value}'")
            else:
                # Особая логика для клиентов
                if script_field == 'client':
                    is_compatible, confidence, final_client = self.smart_client_comparison(script_value, ai_value)
                    validation_result['final_result'][script_field] = final_client
                    validation_result['confidence_scores'][script_field] = confidence
                    
                    if not is_compatible:
                        validation_result['conflicts'].append(
                            f"client: разные клиенты - скрипт: '{script_value}', AI: '{ai_value}'"
                        )
                else:
                    similarity = self.calculate_similarity(script_value, ai_value)
                    if similarity > 0.7:
                        # Похожие значения - выбираем более детальное
                        if len(ai_value) > len(script_value):
                            validation_result['final_result'][script_field] = ai_value
                            validation_result['confidence_scores'][script_field] = 0.8
                        else:
                            validation_result['final_result'][script_field] = script_value
                            validation_result['confidence_scores'][script_field] = 0.8
                    else:
                        # Существенно разные значения - требует ручной проверки
                        validation_result['final_result'][script_field] = f"КОНФЛИКТ: скрипт='{script_value}' | AI='{ai_value}'"
                        validation_result['confidence_scores'][script_field] = 0.3
                        validation_result['conflicts'].append(
                            f"{script_field}: конфликт значений - скрипт: '{script_value}', AI: '{ai_value}'"
                        )
        
        # Добавляем оценку общей точности
        avg_confidence = sum(validation_result['confidence_scores'].values()) / len(validation_result['confidence_scores'])
        validation_result['overall_confidence'] = avg_confidence
        
        if avg_confidence >= 0.9:
            validation_result['validation_notes'].append("Высокая точность извлечения")
        elif avg_confidence >= 0.7:
            validation_result['validation_notes'].append("Средняя точность, возможны незначительные ошибки")
        else:
            validation_result['validation_notes'].append("Низкая точность, требуется ручная проверка")
        
        return validation_result
    
    def smart_client_comparison(self, script_client: str, ai_client: str) -> Tuple[bool, float, str]:
        """Умное сравнение клиентов - учитывает краткие и полные названия"""
        script_norm = script_client.upper().strip()
        ai_norm = ai_client.upper().strip()
        
        # Маппинг кратких названий к полным
        client_mappings = {
            'ЕПІЦЕНТР': ['ЕПІЦЕНТР К', 'ТОВ "ЕПІЦЕНТР К"', 'ЕПИЦЕНТР'],
            'ПРЕСТИЖ ПРОДУКТ': ['ПРЕСТИЖ ПРОДУКТ.К', 'ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ "ПРЕСТИЖ ПРОДУКТ.К"']
        }
        
        # Проверяем через маппинг
        for short_name, full_names in client_mappings.items():
            if script_norm == short_name:
                for full_name in full_names:
                    if full_name in ai_norm or ai_norm in full_name:
                        return True, 0.9, ai_client  # Возвращаем полное название
        
        # Проверяем частичное совпадение
        similarity = self.calculate_similarity(script_client, ai_client)
        if similarity > 0.6:
            final_client = ai_client if len(ai_client) > len(script_client) else script_client
            return True, 0.8, final_client
        
        return False, 0.3, f"КОНФЛИКТ: скрипт='{script_client}' | AI='{ai_client}'"
    
    def normalize_value(self, value: str) -> str:
        """Нормализация значения для сравнения"""
        if not value or value.lower() in ['не вказано', 'невідомий клієнт', 'документ']:
            return 'не вказано'
        return value.lower().strip()
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Вычисление схожести между двумя строками"""
        from difflib import SequenceMatcher
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()


class SemanticDocumentSearch:
    """Улучшенная система семантического поиска с валидацией данных"""
    
    def __init__(self):
        self.db_config = {
            'host': os.getenv('PG_HOST_LOCAL'),
            'database': os.getenv('PG_DBNAME'),
            'user': os.getenv('PG_USER'),
            'password': os.getenv('PG_PASSWORD'),
            'port': os.getenv('PG_PORT', 5432),
            'client_encoding': 'utf-8'
        }
        
        self.document_vectors = None
        self.documents_metadata = []
        self.conn = None
        self.connect_to_db()
        
        # Инициализация валидатора с поддержкой DeepSeek
        deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
        self.validator = DocumentExtractionValidator(deepseek_api_key)
        
        # Простая замена эмбеддингов - TF-IDF like подход
        self.vocabulary = {}
        self.inverse_vocabulary = {}
        
        # Кэширование для ускорения
        self.cache_dir = "vector_cache"
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
    
    def connect_to_db(self):
        """Подключение к базе данных"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.set_client_encoding('UTF8')
            print("✅ Підключено до бази даних")
        except Exception as e:
            print(f"❌ Помилка підключення до БД: {e}")
            raise
    
    def safe_decode(self, text: Any) -> str:
        """Безопасное декодирование текста"""
        if text is None:
            return ""
        if isinstance(text, str):
            return text
        if isinstance(text, bytes):
            try:
                return text.decode('utf-8', errors='replace')
            except:
                return str(text)
        return str(text)
    
    def build_vocabulary(self, texts: List[str]):
        """Построение словаря терминов"""
        vocabulary = {}
        for text in texts:
            words = self.tokenize_text(text)
            for word in words:
                if word not in vocabulary:
                    vocabulary[word] = len(vocabulary)
        
        self.vocabulary = vocabulary
        self.inverse_vocabulary = {v: k for k, v in vocabulary.items()}
        return vocabulary
    
    def tokenize_text(self, text: str) -> List[str]:
        """Токенизация текста"""
        if not text:
            return []
        
        # Удаление специальных символов, сохранение букв и цифр
        text = re.sub(r'[^\w\sа-яєіїґА-ЯЄІЇҐ0-9]', ' ', text.lower())
        words = re.findall(r'[а-яєіїґa-z0-9]{3,}', text)  # Слова от 3 символов
        
        # Удаление стоп-слов
        stop_words = self.get_stop_words()
        return [word for word in words if word not in stop_words]
    
    def get_stop_words(self):
        """Стоп-слова для украинского и русского"""
        return {
            'і', 'та', 'у', 'в', 'з', 'зі', 'на', 'для', 'до', 'за', 'про', 'від', 'по',
            'не', 'що', 'як', 'але', 'то', 'це', 'ж', 'би', 'б', 'й', 'ти', 'ми', 'ви',
            'вони', 'їх', 'її', 'його', 'їй', 'нам', 'вам', 'їм', 'собі', 'мені', 'тобі',
            'себе', 'тебе', 'мене', 'воно', 'вона', 'він', 'я', 'мій', 'твій', 'наш', 'ваш',
            'інд', 'цей', 'той', 'такий', 'який', 'кожний', 'всі', 'весь', 'деякий',
            'жодний', 'інший', 'котрий', 'чий', 'скільки', 'стільки', 'хто', 'де', 'куди',
            'звідки', 'коли', 'чому', 'навіщо', 'чи', 'або', 'тому', 'бо', 'щоб', 'якщо',
            'ніж', 'поки', 'доки', 'після', 'перед', 'під', 'над', 'між', 'через', 'біля'
        }
    
    def text_to_vector(self, text: str) -> np.ndarray:
        """Преобразование текста в вектор"""
        if not self.vocabulary:
            return np.array([])
        
        words = self.tokenize_text(text)
        vector = np.zeros(len(self.vocabulary))
        
        for word in words:
            if word in self.vocabulary:
                vector[self.vocabulary[word]] += 1
        
        # Нормализация
        if np.sum(vector) > 0:
            vector = vector / np.sum(vector)
        
        return vector
    def save_vectors_cache(self):
        """Сохранение векторов в кэш"""
        try:
            import pickle
            cache_data = {
                'document_vectors': self.document_vectors,
                'documents_metadata': self.documents_metadata,
                'vocabulary': self.vocabulary,
                'inverse_vocabulary': self.inverse_vocabulary
            }
            
            cache_file = os.path.join(self.cache_dir, "vectors_cache.pkl")
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            print(f"💾 Вектори збережено у кеш: {cache_file}")
            
        except Exception as e:
            print(f"⚠️ Помилка збереження кешу: {e}")
    
    def load_vectors_cache(self) -> bool:
        """Загрузка векторов из кэша"""
        try:
            import pickle
            cache_file = os.path.join(self.cache_dir, "vectors_cache.pkl")
            
            if not os.path.exists(cache_file):
                return False
            
            # Проверяем актуальность кэша (максимум 1 час)
            import time
            cache_age = time.time() - os.path.getmtime(cache_file)
            if cache_age > 3600:  # 1 час
                print("🔄 Кеш устарел, перестворюємо вектори...")
                return False
            
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            self.document_vectors = cache_data['document_vectors']
            self.documents_metadata = cache_data['documents_metadata']
            self.vocabulary = cache_data['vocabulary']
            self.inverse_vocabulary = cache_data['inverse_vocabulary']
            
            print(f"⚡ Завантажено з кешу: {len(self.documents_metadata):,} документів")
            return True
            
        except Exception as e:
            print(f"⚠️ Помилка завантаження кешу: {e}")
            return False

    def get_db_hash(self) -> str:
        """Получение хеша базы данных для проверки актуальности"""
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    SELECT COUNT(*), MAX(created_at)
                    FROM t_scan_documents_raw 
                    WHERE description IS NOT NULL AND description != ''
                """)
                count, last_updated = cursor.fetchone()
                return f"{count}_{last_updated}"
        except:
            return "unknown"
    
    def load_documents(self, force_rebuild: bool = False):
        """Загрузка документов и создание векторных представлений с кэшированием"""
        
        # Пробуем загрузить из кэша
        if not force_rebuild and self.load_vectors_cache():
            return
        
        print("🔄 Побудова нового векторного індексу...")
        
        try:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, file_name, page_number, description, full_path 
                    FROM t_scan_documents_raw 
                    WHERE description IS NOT NULL AND description != ''
                    ORDER BY file_name, page_number
                """)
                
                documents = cursor.fetchall()
                texts = []
                self.documents_metadata = []
                
                print(f"📚 Завантажено {len(documents):,} документів")
                
                # Показываем прогресс каждые 1000 документов
                for i, doc in enumerate(documents):
                    if i % 1000 == 0 and i > 0:
                        print(f"⏳ Оброблено {i:,} / {len(documents):,} документів")
                    
                    doc_id, file_name, page_number, description, full_path = doc
                    
                    description_str = self.safe_decode(description)
                    file_name_str = self.safe_decode(file_name)
                    full_path_str = self.safe_decode(full_path)
                    
                    texts.append(description_str)
                    
                    self.documents_metadata.append({
                        'id': doc_id,
                        'file_name': file_name_str,
                        'page_number': page_number,
                        'full_path': full_path_str,
                        'original_text': description_str
                    })
                
                # Построение словаря и векторов
                if texts:
                    print("🔨 Створення словника...")
                    self.build_vocabulary(texts)
                    
                    print(f"🔨 Створення векторів для {len(texts):,} документів...")
                    
                    # Создание векторов для всех документов порциями
                    vectors = []
                    batch_size = 500
                    
                    for i in range(0, len(texts), batch_size):
                        batch_texts = texts[i:i+batch_size]
                        batch_vectors = [self.text_to_vector(text) for text in batch_texts]
                        vectors.extend(batch_vectors)
                        print(f"⏳ Векторизація: {min(i+batch_size, len(texts)):,} / {len(texts):,}")
                    
                    self.document_vectors = np.array(vectors)
                    
                    print("🔄 Нормалізація векторів...")
                    self.document_vectors = normalize(self.document_vectors, norm='l2', axis=1)
                    
                    print("✅ Векторні представлення успішно створені")
                    
                    # Сохраняем в кэш
                    self.save_vectors_cache()
                
        except Exception as e:
            print(f"❌ Помилка завантаження документів: {e}")
            raise
    
    def semantic_search(self, query: str, min_similarity: float = 0.1, top_k: int = 50) -> List[Dict[str, Any]]:
        """Семантический поиск документов"""
        if self.document_vectors is None:
            self.load_documents()
        
        if self.document_vectors.size == 0:
            return []
        
        # Создание вектора для запроса
        query_vector = self.text_to_vector(query)
        if query_vector.size == 0:
            return []
        
        query_vector = normalize(query_vector.reshape(1, -1), norm='l2', axis=1)
        
        # Вычисление косинусной схожести
        similarities = cosine_similarity(query_vector, self.document_vectors).flatten()
        
        results = []
        for idx, similarity in enumerate(similarities):
            if similarity >= min_similarity:
                metadata = self.documents_metadata[idx].copy()
                metadata['similarity'] = float(similarity)
                
                # Извлечение структурированной информации
                doc_info = self.extract_metadata(metadata['original_text'])
                metadata.update(doc_info)
                
                results.append(metadata)
        
        # Сортировка по схожести
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]
    
    async def semantic_search_with_validation(self, query: str, min_similarity: float = 0.1, 
                                            top_k: int = 50, validate_with_ai: bool = True) -> List[Dict[str, Any]]:
        """Улучшенный семантический поиск с валидацией AI"""
        if self.document_vectors is None:
            self.load_documents()
        
        if self.document_vectors.size == 0:
            return []
        
        # Создание вектора для запроса
        query_vector = self.text_to_vector(query)
        if query_vector.size == 0:
            return []
        
        query_vector = normalize(query_vector.reshape(1, -1), norm='l2', axis=1)
        
        # Вычисление косинусной схожести
        similarities = cosine_similarity(query_vector, self.document_vectors).flatten()
        
        results = []
        for idx, similarity in enumerate(similarities):
            if similarity >= min_similarity:
                metadata = self.documents_metadata[idx].copy()
                metadata['similarity'] = float(similarity)
                
                # Извлечение с полной валидацией
                if validate_with_ai:
                    validation_info = await self.extract_metadata_with_validation(metadata['original_text'])
                    metadata.update(validation_info['final_result'])
                    metadata['validation_data'] = validation_info
                else:
                    doc_info = self.extract_metadata(metadata['original_text'])
                    metadata.update(doc_info)
                
                results.append(metadata)
        
        # Сортировка по схожести
        results.sort(key=lambda x: x['similarity'], reverse=True)
        return results[:top_k]
    
    def extract_metadata(self, text: str) -> Dict[str, str]:
        """Улучшенное извлечение метаданных с валидацией"""
        # Используем улучшенные функции извлечения
        script_result = {
            'type': self.validator.enhanced_document_type_detection(text),
            'number': self.validator.enhanced_extract_document_number(text),
            'date': self.validator.enhanced_extract_document_date(text),
            'client': self.validator.enhanced_extract_client_name(text)
        }
        
        return script_result
    
    async def extract_metadata_with_validation(self, text: str) -> Dict[str, Any]:
        """Извлечение метаданных с полной валидацией AI"""
        # Извлекаем данные скриптом
        script_data = self.extract_metadata(text)
        
        # Валидируем через AI (если доступен)
        ai_data = await self.validator.validate_with_deepseek(text)
        
        # Сравниваем результаты
        validation_result = self.validator.compare_and_validate(script_data, ai_data, text)
        
        return validation_result
        
    def detect_document_type(self, text: str) -> str:
        """Определение типа документа"""
        # Сначала проверяем на возвраты
        if re.search(r'накладн[ая][я]?\s+на\s+поверненн[яя]', text, re.IGNORECASE):
            # Проверяем, является ли это ТТН
            if re.search(r'товарно[-\s]транспортн', text, re.IGNORECASE):
                return 'ТТН на повернення'
            else:
                return 'Накладна на повернення'
        
        # Затем специфичные акты
        if re.search(r'акт\s+прийом[ау][-\s]передач[іи]', text, re.IGNORECASE):
            return 'Акт прийому-передачі'
        
        if re.search(r'акт\s+приймання', text, re.IGNORECASE):
            return 'Акт приймання'
        
        if re.search(r'акт\s+поверненн[яя]', text, re.IGNORECASE):
            return 'Акт повернення'
        
        if re.search(r'акт\s+звір[кя]и', text, re.IGNORECASE):
            return 'Акт звірки'
        
        # Затем общие типы
        if re.search(r'товарно[-\s]транспортн[ая][я]?\s+накладн[ая][я]?|ттн', text, re.IGNORECASE):
            return 'ТТН'
        
        if re.search(r'відомост[і] про вантаж', text, re.IGNORECASE):
            return 'ТТН'
        
        if re.search(r'розвантаж', text, re.IGNORECASE):
            return 'ТТН'
        
        if re.search(r'рахун[ок][ок]?[-\s]фактура?', text, re.IGNORECASE):
            return 'Рахунок-фактура'
        
        if re.search(r'заявк[ая]\s+на\s+поверненн[яя]', text, re.IGNORECASE):
            return 'Заявка на повернення'
        
        if re.search(r'накладн[ая]\s+на\s+поверненн[я]', text, re.IGNORECASE):
            return 'Накладна на повернення'
        
        if re.search(r'поверненн[я] товарів', text, re.IGNORECASE):
            return 'Накладна на повернення'
        
        if re.search(r'поверненн[я] постачальнику', text, re.IGNORECASE):
            return 'Накладна на повернення'
        
        if re.search(r'видаткова накладн[ая]\s+', text, re.IGNORECASE):
            return 'Видаткова накладна'
        
        if re.search(r'расходная накладн[ая]\s+', text, re.IGNORECASE):
            return 'Видаткова накладна'

        if re.search(r'накладн[ая][я]?', text, re.IGNORECASE):
            return 'Накладна'
        
        if re.search(r'рахун[ок][ок]?', text, re.IGNORECASE):
            return 'Рахунок'
        
        if re.search(r'догов[іо]р', text, re.IGNORECASE):
            return 'Договір'
        
        if re.search(r'протокол', text, re.IGNORECASE):
            return 'Протокол'
        
        if re.search(r'звіт', text, re.IGNORECASE):
            return 'Звіт'
        
        return 'Документ'

    def extract_document_number(self, text: str) -> str:
        """Извлечение номера документа"""
        number_patterns = [
            r'№\s*([A-ZА-Я0-9\-/]+[A-ZА-Я0-9])',
            r'номер[:\s]*([A-ZА-Я0-9\-/]+[A-ZА-Я0-9])',
            r'№([A-ZА-Я0-9\-/]+[A-ZА-Я0-9])',
            r'([A-Z]{2,}/[A-Z0-9\-]+/[A-Z0-9\-]+)'
        ]
        
        for pattern in number_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for match in matches:
                    if isinstance(match, tuple):
                        number = match[0]
                    else:
                        number = match
                    
                    if (len(number) >= 3 and 
                        not any(word in number.lower() for word in ['від', 'дата', 'стор', 'page'])):
                        return number
        
        return 'Не вказано'
    
    def extract_document_date(self, text: str) -> str:
        """Извлечение даты документа"""
        date_patterns = [
            r'від\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})',
            r'дата[:\s]*(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})',
            r'(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{4})',
            r'(\d{1,2}\s+[а-я]+\s+\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                date_str = match.group(1)
                return date_str
        
        return 'Не вказано'
    
    def extract_client_name(self, text: str) -> str:
        """Извлечение имени клиента"""
        client_patterns = [
            (r'відправник[:\s]*([^\n,]{3,50})', 1),
            (r'контрагент[:\s]*([^\n,]{3,50})', 1),
            (r'товариство[:\s]*([^\n,]{3,50})', 1),
            (r'тов[:\s]*([^\n,]{3,50})', 1),
            (r'епіцентр|epicenter', 'ЕПІЦЕНТР'),
            (r'метро|metro', 'МЕТРО'),
            (r'ашан|auchan', 'АШАН'),
            (r'атб|atb', 'АТБ'),
            (r'сільпо|silpo', 'СІЛЬПО'),
            (r'нов[ая][\s]+пошта|nova[\s]+poshta', 'НОВА ПОШТА')
        ]
        
        for pattern, client_info in client_patterns:
            if isinstance(client_info, int):
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    client_name = match.group(client_info).strip()
                    if (len(client_name) > 2 and 
                        not any(word in client_name.lower() for word in ['накладна', 'рахунок', 'акт', 'документ', 'ТТН'])):
                        return client_name
            else:
                if re.search(pattern, text, re.IGNORECASE):
                    return client_info
        
        return 'Невідомий клієнт'
    
    def export_to_excel(self, results: List[Dict[str, Any]], output_path: Optional[str] = None) -> str:
        """Экспорт результатов в Excel с данными валидации"""
        if not results:
            raise ValueError("Немає результатів для експорту")
        
        # Определяем путь для сохранения
        if not output_path:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            filename = f"Звіт_документів_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            output_path = os.path.join(script_dir, filename)
        else:
            if not output_path.lower().endswith('.xlsx'):
                output_path += '.xlsx'
        
        # Создаем Excel файл
        wb = Workbook()
        ws = wb.active
        ws.title = "Документи"
        
        # Проверяем, есть ли данные валидации
        has_validation_data = any('validation_data' in result for result in results)
        
        if has_validation_data:
            # Расширенные заголовки с данными валидации
            headers = ['Тип документа', 'Клієнт', 'Період', 'Номер документа', 
                       'Файл', 'Сторінка', 'Схожість', 'Точність',
                       'Конфлікти', 'Примітки', 'Фрагмент текста']
        else:
            # Стандартные заголовки
            headers = ['Тип документа', 'Клієнт', 'Період', 'Номер документа', 
                       'Файл', 'Сторінка', 'Схожість', 'Фрагмент текста']
        
        for col_idx, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_idx, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Данные
        for row_idx, result in enumerate(results, 2):
            ws.cell(row=row_idx, column=1, value=result.get('type', 'Не вказано'))
            ws.cell(row=row_idx, column=2, value=result.get('client', 'Невідомий клієнт'))
            ws.cell(row=row_idx, column=3, value=result.get('date', 'Не вказано'))
            ws.cell(row=row_idx, column=4, value=result.get('number', 'Не вказано'))
            ws.cell(row=row_idx, column=5, value=result.get('file_name', ''))
            ws.cell(row=row_idx, column=6, value=result.get('page_number', ''))
            ws.cell(row=row_idx, column=7, value=f"{result.get('similarity', 0):.3f}")
            
            if has_validation_data and 'validation_data' in result:
                vd = result['validation_data']
                ws.cell(row=row_idx, column=8, value=f"{vd.get('overall_confidence', 0):.2f}")
                ws.cell(row=row_idx, column=9, value='; '.join(vd.get('conflicts', [])))
                ws.cell(row=row_idx, column=10, value='; '.join(vd.get('validation_notes', [])))
                text_col = 11
            else:
                text_col = 8
            
            # Добавляем фрагмент текста (первые 300 символов)
            text_fragment = result.get('original_text', '')[:300]
            if len(result.get('original_text', '')) > 300:
                text_fragment += "..."
            ws.cell(row=row_idx, column=text_col, value=text_fragment)
        
        # Настройка ширины колонок
        if has_validation_data:
            column_widths = [25, 25, 15, 20, 35, 10, 10, 12, 40, 30, 50]
        else:
            column_widths = [25, 25, 15, 20, 35, 10, 10, 50]
        
        for col_idx, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(col_idx)].width = width
        
        # Границы
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in ws.iter_rows(min_row=1, max_row=len(results) + 1, max_col=len(headers)):
            for cell in row:
                cell.border = thin_border
                cell.alignment = Alignment(vertical='center', wrap_text=True)
        
        # Цветовое кодирование по точности (если есть данные валидации)
        if has_validation_data:
            for row_idx, result in enumerate(results, 2):
                if 'validation_data' in result:
                    confidence = result['validation_data'].get('overall_confidence', 0)
                    if confidence >= 0.9:
                        # Высокая точность - зеленый
                        fill = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")
                    elif confidence >= 0.7:
                        # Средняя точность - желтый
                        fill = PatternFill(start_color="FFEB9C", end_color="FFEB9C", fill_type="solid")
                    else:
                        # Низкая точность - красный
                        fill = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")
                    
                    for col in range(1, len(headers) + 1):
                        ws.cell(row=row_idx, column=col).fill = fill
        
        # Сохраняем
        wb.save(output_path)
        
        return output_path
    
    def close(self):
        """Закрытие соединения"""
        if self.conn:
            self.conn.close()


# Основная функция для использования
def main():
    """Главная функция поиска с кэшированием"""
    print("🔍 СИСТЕМА СЕМАНТИЧНОГО ПОШУКУ ДОКУМЕНТІВ")
    print("=" * 50)
    
    search_system = SemanticDocumentSearch()
    
    try:
        # Проверка кэша
        print("Опції векторизації:")
        print("1. Використати кеш (швидко)")
        print("2. Пересторити вектори (повільно, для 5,753 документів)")
        
        cache_choice = input("Оберіть варіант (1/2) [1]: ").strip() or "1"
        force_rebuild = cache_choice == "2"
        
        if force_rebuild:
            print("⚠️  Обрано перестворення векторів. Це може зайняти кілька хвилин...")
        
        # Попередня загрузка векторов
        print("⏳ Підготовка системи пошуку...")
        search_system.load_documents(force_rebuild=force_rebuild)
        
        # Ввод параметров поиска
        print("\nВведіть пошуковий запит:")
        print("Приклад: 'повернення товарів Епіцентр'")
        print("-" * 40)
        
        query = input("➡️  Ваш запит: ").strip()
        
        if not query:
            print("❌ Будь ласка, введіть запит!")
            return
        
        # Выполняем поиск
        print(f"\n🔍 Шукає: '{query}'")
        print("⏳ Обробка запиту...")
        
        results = search_system.semantic_search(
            query=query,
            min_similarity=0.15,  # Более низкий порог для лучших результатов
            top_k=100
        )
        
        if not results:
            print("❌ Документів не знайдено")
            print("💡 Спробуйте:")
            print("   - Зменшити специфічність запиту")
            print("   - Використати інші ключові слова")
            return
        
        print(f"✅ Знайдено {len(results)} документів")
        
        # Экспорт в Excel
        excel_path = search_system.export_to_excel(results)
        
        print(f"\n📊 Файл Excel створено:")
        print(f"📁 {excel_path}")
        
        # Показать топ-результаты
        print(f"\n🏆 Топ-5 результатів:")
        for i, result in enumerate(results[:5], 1):
            print(f"{i}. {result['type']} №{result['number']} - {result['client']} ({result['similarity']:.3f})")
        
        print(f"\n💡 Файл збережено в папці скрипта")
        print("   Відкрийте його в Excel для подальшого використання")
        
    except Exception as e:
        print(f"❌ Помилка: {e}")
    finally:
        search_system.close()


async def main_with_validation():
    """Главная функция поиска с валидацией"""
    print("🔍 УЛУЧШЕННАЯ СИСТЕМА СЕМАНТИЧНОГО ПОИСКА ДОКУМЕНТОВ")
    print("🆕 С ИНТЕЛЛЕКТУАЛЬНОЙ ВАЛИДАЦИЕЙ ДАННЫХ")
    print("=" * 60)
    
    search_system = SemanticDocumentSearch()
    
    try:
        # Ввод параметров поиска
        print("\nВведіть пошуковий запит:")
        print("Приклад: 'повернення товарів Епіцентр'")
        print("-" * 50)
        
        query = input("➡️  Ваш запит: ").strip()
        
        if not query:
            print("❌ Будь ласка, введіть запит!")
            return
        
        # Параметры валидации
        print("\nОпції валідації:")
        print("1. Стандартний пошук (швидко)")
        print("2. Пошук з AI-валідацією (точно, але повільно)")
        
        validation_choice = input("Оберіть варіант (1/2) [1]: ").strip() or "1"
        use_ai_validation = validation_choice == "2"
        
        if use_ai_validation:
            print("⚠️  Обрано AI-валідацію. Перевірте, що DEEPSEEK_API_KEY налаштовано.")
        
        # Выполняем поиск
        print(f"\n🔍 Шукає: '{query}'")
        print("⏳ Обробка запиту...")
        
        if use_ai_validation:
            results = await search_system.semantic_search_with_validation(
                query=query,
                min_similarity=0.15,
                top_k=100,
                validate_with_ai=True
            )
        else:
            results = search_system.semantic_search(
                query=query,
                min_similarity=0.15,
                top_k=100
            )
        
        if not results:
            print("❌ Документів не знайдено")
            print("💡 Спробуйте:")
            print("   - Зменшити специфічність запиту")
            print("   - Використовувати інші ключові слова")
            return
        
        print(f"✅ Знайдено {len(results)} документів")
        
        # Анализ точности (если использовалась AI-валидация)
        if use_ai_validation and any('validation_data' in r for r in results):
            accuracy_stats = analyze_accuracy(results)
            print(f"\n📊 Аналіз точності извлечення:")
            print(f"🎯 Висока точність (>90%): {accuracy_stats['high_accuracy']} документів")
            print(f"🟡 Середня точність (70-90%): {accuracy_stats['medium_accuracy']} документів")
            print(f"🔴 Низька точність (<70%): {accuracy_stats['low_accuracy']} документів")
            print(f"📋 Конфліктів всього: {accuracy_stats['conflicts_count']}")
        
        # Экспорт в Excel
        excel_path = search_system.export_to_excel(results)
        
        print(f"\n📊 Файл Excel створено:")
        print(f"📁 {excel_path}")
        
        # Показать топ-результаты
        print(f"\n🏆 Топ-5 результатів:")
        for i, result in enumerate(results[:5], 1):
            confidence_info = ""
            if use_ai_validation and 'validation_data' in result:
                confidence = result['validation_data'].get('overall_confidence', 0)
                confidence_info = f" (точність: {confidence:.1%})"
            
            print(f"{i}. {result.get('type', 'Невідомо')} №{result.get('number', 'N/A')} - "
                  f"{result.get('client', 'Невідомий клієнт')} "
                  f"({result.get('similarity', 0):.3f}){confidence_info}")
        
        print(f"\n💡 Файл збережено в папці скрипта")
        print("   Відкрийте його в Excel для подальшого використання")
        
    except Exception as e:
        print(f"❌ Помилка: {e}")
    finally:
        search_system.close()


def analyze_accuracy(results: List[Dict[str, Any]]) -> Dict[str, int]:
    """Анализ точности результатов"""
    stats = {
        'high_accuracy': 0,
        'medium_accuracy': 0,
        'low_accuracy': 0,
        'conflicts_count': 0
    }
    
    for result in results:
        if 'validation_data' in result:
            vd = result['validation_data']
            confidence = vd.get('overall_confidence', 0)
            conflicts = vd.get('conflicts', [])
            
            if confidence >= 0.9:
                stats['high_accuracy'] += 1
            elif confidence >= 0.7:
                stats['medium_accuracy'] += 1
            else:
                stats['low_accuracy'] += 1
                
            stats['conflicts_count'] += len(conflicts)
    
    return stats


# Обновляем основную функцию для поддержки асинхронности
if __name__ == "__main__":
    asyncio.run(main_with_validation())
