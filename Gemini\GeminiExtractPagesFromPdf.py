# -*- coding: utf-8 -*-
# извлекает из PDF страницы, которые вы хотите, и сохраняет их в новый PDF файл.
# Опционально, уменьшает размер итогового файла, если это возможно.

import fitz  # PyMuPDF
import os
import datetime
from PIL import Image
import io
import tempfile

# --- Константы (остаются такими же) ---
# Если True, страницы будут растеризованы при извлечении. Для первоначального извлечения. До сжатия.
RASTERIZE_PAGES_DURING_EXTRACTION = True

# Алгоритм сжатия Deflate без потерь при первоначальном извлечении. До сжатия.
DEFLATE_COMPRESSION = True

# Если True, применяем к файлу с извлеченными страницами
COMPRESS_FINAL_PDF = True

# для начальной растеризации страниц во время их извлечения из исходного PDF-файла.
INITIAL_RASTER_DPI = 150  # DPI для растеризации страниц при извлечении
GARBAGE_COLLECTION = 3  # Сбор мусора при сохранении
FINAL_COMPRESSION_DPI = 200  # DPI для финального сжатия
JPEG_COMPRESSION_QUALITY = 20  # Качество JPEG для сжатия
DEFAULT_LOG_SUBDIR = "logs"  # Подкаталог для логов


def _get_script_directory():
    """Возвращает директорию, в которой находится текущий скрипт."""
    return os.path.dirname(os.path.abspath(__file__))


def setup_error_logger(log_file_path=None, pdf_filename_for_log_name=None):
    """
    Настраивает и возвращает путь к лог-файлу.
    Если log_file_path не указан, создает лог в поддиректории DEFAULT_LOG_SUBDIR
    относительно директории скрипта, используя pdf_filename_for_log_name для имени.
    """
    if log_file_path:
        # Если путь к логу указан, пытаемся создать директорию, если ее нет
        log_dir = os.path.dirname(log_file_path)
        if log_dir and not os.path.exists(log_dir):
            try:
                os.makedirs(log_dir, exist_ok=True)
            except OSError as e:
                print(
                    f"WARNING: Не удалось создать директорию для лога '{log_dir}': {e}. Лог будет в директории скрипта."
                )
                # Возврат к директории скрипта, если не удалось создать указанную
                base_name = os.path.splitext(os.path.basename(log_file_path))[0]
                log_file_path = os.path.join(
                    _get_script_directory(), f"{base_name}.log"
                )
        return log_file_path

    # Если log_file_path не указан, используем имя PDF (если есть) или стандартное имя
    script_dir = _get_script_directory()
    logs_dir_path = os.path.join(script_dir, DEFAULT_LOG_SUBDIR)

    if not os.path.exists(logs_dir_path):
        try:
            os.makedirs(logs_dir_path, exist_ok=True)
        except OSError as e:
            print(
                f"WARNING: Не удалось создать директорию логов '{logs_dir_path}': {e}. Лог будет в директории скрипта."
            )
            logs_dir_path = script_dir  # Возврат к директории скрипта

    log_base_name = "pdf_processing_error"
    if pdf_filename_for_log_name:
        log_base_name = os.path.splitext(os.path.basename(pdf_filename_for_log_name))[0]

    final_log_path = os.path.join(logs_dir_path, f"{log_base_name}.log")
    return final_log_path


def log_message(log_file, message, level="INFO"):
    timestamp = datetime.datetime.now().strftime("%d.%m.%Y %H:%M:%S")
    log_line_for_file = f"{timestamp} | {level.upper()} | {message}\n"
    try:
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(log_line_for_file)
    except Exception as e_log:
        print(
            f"{timestamp} | CRITICAL | Не удалось записать в лог-файл {log_file}: {e_log}"
        )

    if (
        level.upper() == "ERROR"
        or level.upper() == "WARNING"
        or level.upper() == "CRITICAL"
    ):
        print(f"{timestamp} | {level.upper()} | {message}")


# --- Функции process_page_for_compression, compress_pdf_internal, extract_and_save_pages_to_single_pdf ---
# (Остаются такими же, как в предыдущем ответе, но теперь log_message используется последовательно)


def process_page_for_compression(
    page, dpi, compression_quality, error_log_file, page_num_info=""
):
    # log_message(error_log_file, f"Начало process_page_for_compression для {page_num_info}", "INFO")
    pix = page.get_pixmap(dpi=dpi)
    # Используем PNG для промежуточного шага с Pillow, чтобы контролировать качество JPEG на выходе
    # Если напрямую из pix.tobytes("jpeg"), качество контролируется менее гибко
    img_pil = Image.open(io.BytesIO(pix.tobytes("png")))

    tmp_filename = ""
    try:
        # delete=False важно, чтобы файл не удалялся сразу после закрытия дескриптора tmp
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as tmp:
            tmp_filename = tmp.name

        img_pil.save(tmp_filename, "JPEG", quality=compression_quality, optimize=True)
        return tmp_filename
    except Exception as e_proc_page:
        if tmp_filename and os.path.exists(
            tmp_filename
        ):  # Пытаемся удалить, если создан
            try:
                os.unlink(tmp_filename)
            except:
                pass  # Игнорируем ошибки при удалении временного файла здесь
        raise e_proc_page


def compress_pdf_internal(input_path, output_path, dpi, quality, error_log_file):
    log_message(
        error_log_file,
        f"Начало сжатия файла: {os.path.basename(input_path)} -> {os.path.basename(output_path)} (DPI: {dpi}, Качество JPEG: {quality})",
        "INFO",
    )
    original_doc = None
    compressed_doc = None
    success_flag = False
    compression_info = {}
    temp_files_to_delete = []

    try:
        if not os.path.exists(input_path):
            log_message(
                error_log_file, f"Файл для сжатия не найден: {input_path}", "ERROR"
            )
            return False, {}

        original_doc = fitz.open(input_path)
        compressed_doc = fitz.open()

        for page_num in range(len(original_doc)):
            page_num_one_based = page_num + 1
            page = original_doc[page_num]
            tmp_img_filename = None
            try:
                # log_message(error_log_file, f"Сжатие страницы {page_num_one_based}/{len(original_doc)}...", "INFO")
                tmp_img_filename = process_page_for_compression(
                    page, dpi, quality, error_log_file, f"стр. {page_num_one_based}"
                )
                temp_files_to_delete.append(tmp_img_filename)

                # Вставка изображения в PDF
                img_for_pdf_doc = fitz.open(
                    tmp_img_filename
                )  # Открываем временный JPEG как PDF
                rect_img = img_for_pdf_doc.load_page(
                    0
                ).rect  # Получаем размеры изображения
                new_compressed_page = compressed_doc.new_page(
                    width=rect_img.width, height=rect_img.height
                )
                new_compressed_page.insert_image(rect_img, filename=tmp_img_filename)
                img_for_pdf_doc.close()

            except Exception as e_page_compress:
                log_message(
                    error_log_file,
                    f"Ошибка при сжатии страницы {page_num_one_based} файла {os.path.basename(input_path)}: {e_page_compress}",
                    "ERROR",
                )
                continue

        if compressed_doc.page_count > 0:
            compressed_doc.save(output_path, garbage=4, deflate=True)
            success_flag = True
            try:
                input_size_kb = os.path.getsize(input_path) / 1024
                output_size_kb = os.path.getsize(output_path) / 1024
                reduction_percent = (
                    (1 - output_size_kb / input_size_kb) * 100
                    if input_size_kb > 0
                    else 0
                )
                compression_info = {
                    "input_size_kb": round(input_size_kb, 2),
                    "output_size_kb": round(output_size_kb, 2),
                    "reduction_percent": round(reduction_percent, 2),
                }
                log_message(
                    error_log_file,
                    f"Файл {os.path.basename(output_path)} успешно сжат. Инфо: {compression_info}",
                    "INFO",
                )
            except Exception as e_size:
                log_message(
                    error_log_file,
                    f"Не удалось получить информацию о размерах файлов после сжатия: {e_size}",
                    "WARNING",
                )
        else:
            log_message(
                error_log_file,
                f"Не удалось сжать файл {os.path.basename(input_path)}, нет страниц в итоговом документе.",
                "WARNING",
            )

    except Exception as e:
        log_message(
            error_log_file,
            f"Критическая ошибка при сжатии PDF {os.path.basename(input_path)}: {e}",
            "ERROR",
        )
        success_flag = False
        compression_info = {"error": str(e)}
    finally:
        for tmp_file in temp_files_to_delete:  # Очистка всех временных файлов
            if os.path.exists(tmp_file):
                try:
                    os.unlink(tmp_file)
                except Exception as e_del_tmp:
                    log_message(
                        error_log_file,
                        f"Не удалось удалить временный файл {tmp_file}: {e_del_tmp}",
                        "WARNING",
                    )
        if original_doc:
            try:
                original_doc.close()
            except:
                pass
        if (
            compressed_doc
        ):  # compressed_doc закрывается методом save, если он был вызван
            try:
                compressed_doc.close()
            except ValueError as ve:
                if "document closed" not in str(ve).lower():
                    log_message(
                        error_log_file,
                        f"Предупреждение (ValueError) при закрытии compressed_doc: {ve}",
                        "WARNING",
                    )
            except Exception:
                pass  # Игнорируем другие ошибки закрытия здесь
    return success_flag, compression_info


def extract_and_save_pages_to_single_pdf(
    pdf_path, page_numbers_to_extract, output_pdf_path, error_log_file
):
    original_pdf_basename = os.path.basename(pdf_path)
    new_doc_obj = None
    source_doc_obj = None
    operation_successful = False

    try:
        # Валидация входного файла уже должна быть сделана до вызова этой функции
        source_doc_obj = fitz.open(pdf_path)
        total_pages_in_source = source_doc_obj.page_count
        new_doc_obj = fitz.open()

        log_message(
            error_log_file,
            f"Извлечение страниц из: {original_pdf_basename} (Всего: {total_pages_in_source})",
            "INFO",
        )

        pages_added_to_new_doc = 0
        for page_num_one_based in page_numbers_to_extract:
            page_num_zero_based = page_num_one_based - 1

            if not (0 <= page_num_zero_based < total_pages_in_source):
                msg = f"Страница {page_num_one_based} не существует в {original_pdf_basename} (всего {total_pages_in_source} стр.). Пропуск."
                log_message(error_log_file, msg, "WARNING")
                continue

            try:
                # log_message(error_log_file, f"Обработка страницы {page_num_one_based}...", "INFO")
                page_to_process = source_doc_obj.load_page(page_num_zero_based)

                if RASTERIZE_PAGES_DURING_EXTRACTION:
                    # log_message(error_log_file, f"  Растеризация с DPI={INITIAL_RASTER_DPI}...", "INFO")
                    pix = page_to_process.get_pixmap(dpi=INITIAL_RASTER_DPI)
                    new_page_for_image = new_doc_obj.new_page(
                        width=pix.width, height=pix.height
                    )
                    new_page_for_image.insert_image(new_page_for_image.rect, pixmap=pix)
                    pix = None
                else:
                    # log_message(error_log_file, f"  Копирование (векторное)...", "INFO")
                    new_doc_obj.insert_pdf(
                        source_doc_obj,
                        from_page=page_num_zero_based,
                        to_page=page_num_zero_based,
                    )

                pages_added_to_new_doc += 1

            except Exception as e_page:
                msg = f"Ошибка при извлечении/обработке стр. {page_num_one_based} из {original_pdf_basename}: {e_page}"
                log_message(error_log_file, msg, "ERROR")

        if pages_added_to_new_doc > 0:
            final_page_count_before_save = new_doc_obj.page_count
            log_message(
                error_log_file,
                f"Сохранение извлеченных страниц ({final_page_count_before_save} стр.) в {output_pdf_path}...",
                "INFO",
            )
            new_doc_obj.save(
                output_pdf_path, garbage=GARBAGE_COLLECTION, deflate=DEFLATE_COMPRESSION
            )

            log_message(
                error_log_file,
                f"Успешно сохранено {final_page_count_before_save} стр. в {output_pdf_path}",
                "INFO",
            )
            operation_successful = True
        else:
            msg = f"Ни одна страница для {original_pdf_basename} не была добавлена в '{os.path.basename(output_pdf_path)}'."
            log_message(error_log_file, msg, "WARNING")
            operation_successful = False  # Файл не будет создан, если нет страниц
        return operation_successful

    except (RuntimeError, fitz.FileDataError) as e_fitz_runtime:
        msg = (
            f"Критическая ошибка PyMuPDF (извлечение) с '{pdf_path}': {e_fitz_runtime}."
        )
        log_message(error_log_file, msg, "ERROR")
        return False
    except Exception as e:
        msg = f"Непредвиденная ошибка (извлечение) с '{pdf_path}': {e}"
        log_message(error_log_file, msg, "ERROR")
        return False
    finally:
        if source_doc_obj:
            try:
                source_doc_obj.close()
            except:
                pass
        if new_doc_obj:
            try:
                new_doc_obj.close()
            except ValueError as ve:
                if "document closed" not in str(ve).lower():
                    log_message(
                        error_log_file,
                        f"Предупреждение (ValueError) при закрытии new_doc_obj (извлечение): {ve}",
                        "WARNING",
                    )
            except Exception as e_fin:
                log_message(
                    error_log_file,
                    f"Неожиданное предупреждение при закрытии new_doc_obj (извлечение): {e_fin}",
                    "WARNING",
                )


# --- Основная функция для запуска процесса ---
def run_extraction_process(
    input_pdf_abs_path, output_pdf_abs_path, pages_to_extract, error_log_abs_path=None
):
    """
    Выполняет весь процесс: настройка лога, извлечение, опциональное сжатие.

    Args:
        input_pdf_abs_path (str): Абсолютный путь к исходному PDF.
        output_pdf_abs_path (str): Абсолютный путь для сохранения итогового (возможно, сжатого) PDF.
                                    Имя файла уже должно быть сформировано вызывающей стороной.
        pages_to_extract (list): Список номеров страниц (1-индексированных).
        error_log_abs_path (str, optional): Абсолютный путь к лог-файлу.
                                            Если None, будет создан по умолчанию.
    """
    # 1. Настройка лог-файла
    # Если error_log_abs_path не предоставлен, он будет создан на основе имени input_pdf_abs_path
    # или стандартного имени в подпапке logs.
    current_error_log_path = (
        error_log_abs_path
        if error_log_abs_path
        else setup_error_logger(pdf_filename_for_log_name=input_pdf_abs_path)
    )

    # 2. Валидация входного файла
    if not (os.path.exists(input_pdf_abs_path) and os.path.isfile(input_pdf_abs_path)):
        msg = f"Исходный PDF-файл '{input_pdf_abs_path}' не найден или не является файлом."
        log_message(current_error_log_path, msg, "ERROR")
        return False  # Возвращаем статус неудачи

    if not pages_to_extract:
        log_message(
            current_error_log_path, "Список страниц для извлечения пуст.", "ERROR"
        )
        return False

    # 3. Логирование начальных параметров (теперь внутри этой функции)
    log_message(
        current_error_log_path,
        f"--- Начало операции для {os.path.basename(input_pdf_abs_path)} ---",
        "INFO",
    )
    log_message(current_error_log_path, f"Исходный PDF: {input_pdf_abs_path}", "INFO")
    log_message(
        current_error_log_path, f"Страницы для извлечения: {pages_to_extract}", "INFO"
    )
    log_message(
        current_error_log_path,
        f"Итоговый PDF будет сохранен как: {output_pdf_abs_path}",
        "INFO",
    )
    log_message(current_error_log_path, f"Лог файл: {current_error_log_path}", "INFO")
    if RASTERIZE_PAGES_DURING_EXTRACTION:
        log_message(
            current_error_log_path,
            f"Начальная растеризация: Да, DPI={INITIAL_RASTER_DPI}",
            "INFO",
        )
    log_message(
        current_error_log_path,
        f"Финальное сжатие: {'Да' if COMPRESS_FINAL_PDF else 'Нет'}",
        "INFO",
    )
    if COMPRESS_FINAL_PDF:
        log_message(
            current_error_log_path,
            f"Параметры сжатия: DPI={FINAL_COMPRESSION_DPI}, JPEG Качество={JPEG_COMPRESSION_QUALITY}",
            "INFO",
        )
    log_message(current_error_log_path, "--------------------------", "INFO")

    # Путь для промежуточного файла (содержащего только извлеченные страницы до финального сжатия)
    # Он будет иметь то же имя, что и output_pdf_abs_path, если сжатие отключено.
    # Если сжатие включено, output_pdf_abs_path будет именем для *окончательно сжатого* файла.
    path_for_extracted_pages = ""
    if COMPRESS_FINAL_PDF:
        # Создаем временное имя для файла только с извлеченными страницами
        temp_dir = os.path.dirname(output_pdf_abs_path)
        temp_base = (
            os.path.splitext(os.path.basename(output_pdf_abs_path))[0]
            + "_temp_extracted.pdf"
        )
        path_for_extracted_pages = os.path.join(temp_dir, temp_base)
    else:
        path_for_extracted_pages = (
            output_pdf_abs_path  # Если нет сжатия, это и есть итоговый файл
        )

    # 4. Извлечение и сборка страниц
    extraction_success = extract_and_save_pages_to_single_pdf(
        input_pdf_abs_path,
        pages_to_extract,
        path_for_extracted_pages,
        current_error_log_path,
    )

    if not extraction_success:
        log_message(
            current_error_log_path,
            "Ошибка на этапе извлечения страниц. Операция прервана.",
            "ERROR",
        )
        if COMPRESS_FINAL_PDF and os.path.exists(
            path_for_extracted_pages
        ):  # Удаляем временный, если он создался до ошибки
            try:
                os.remove(path_for_extracted_pages)
            except:
                pass
        return False

    # 5. Финальное сжатие (если включено)
    if COMPRESS_FINAL_PDF:
        log_message(
            current_error_log_path,
            f"Начало финального сжатия '{os.path.basename(path_for_extracted_pages)}' -> '{os.path.basename(output_pdf_abs_path)}'...",
            "INFO",
        )
        compress_success, c_info = compress_pdf_internal(
            path_for_extracted_pages,  # Сжимаем промежуточный файл
            output_pdf_abs_path,  # Сохраняем окончательно сжатый результат
            dpi=FINAL_COMPRESSION_DPI,
            quality=JPEG_COMPRESSION_QUALITY,
            error_log_file=current_error_log_path,
        )
        # Удаляем временный файл с извлеченными страницами
        if os.path.exists(path_for_extracted_pages):
            try:
                os.remove(path_for_extracted_pages)
                log_message(
                    current_error_log_path,
                    f"Временный файл извлеченных страниц '{os.path.basename(path_for_extracted_pages)}' удален.",
                    "INFO",
                )
            except Exception as e_del_temp:
                log_message(
                    current_error_log_path,
                    f"Не удалось удалить временный файл '{os.path.basename(path_for_extracted_pages)}': {e_del_temp}",
                    "WARNING",
                )

        if not compress_success:
            log_message(
                current_error_log_path,
                f"Ошибка на этапе финального сжатия. Файл '{os.path.basename(path_for_extracted_pages)}' мог остаться не сжатым.",
                "ERROR",
            )
            return False  # Возвращаем статус неудачи
    else:
        # Если сжатие отключено, path_for_extracted_pages и есть output_pdf_abs_path
        log_message(current_error_log_path, "Финальное сжатие пропущено.", "INFO")

    log_message(
        current_error_log_path,
        f"Операция успешно завершена. Итоговый файл: {output_pdf_abs_path}",
        "INFO",
    )
    return True  # Возвращаем статус успеха


# --- Точка входа, если скрипт запускается напрямую ---
if __name__ == "__main__":
    # Этот блок теперь служит только для примера вызова или для интерактивного режима,
    # если вы решите его добавить обратно.
    # Для прямого вызова с заданными параметрами:

    # 2.1) имя входного файла pdf.
    input_file = (
        r"c:\Scan\All\2025-05-31_1.pdf"  # Пример
    )
    # 2.2) имя выходного файла pdf.
    # Имя выходного файла формируется на основе входного
    output_dir_main = (
        os.path.dirname(input_file) if os.path.dirname(input_file) else os.getcwd()
    )
    output_base_name_main = os.path.splitext(os.path.basename(input_file))[0]
    # Если COMPRESS_FINAL_PDF=True, то к output_base_name_main будет добавлен _compressed в run_extraction_process
    # Если COMPRESS_FINAL_PDF=False, то будет _extracted_pages
    # Поэтому здесь мы можем задать только основу для имени _extracted_pages
    # Окончательное имя (с _compressed или без) формируется внутри run_extraction_process
    # на основе output_file_for_run

    # Правильнее будет задать конечное имя файла, которое мы ожидаем получить
    if COMPRESS_FINAL_PDF:
        output_file_for_run = os.path.join(
            output_dir_main, f"{output_base_name_main}_compressed.pdf"
        )
    else:
        output_file_for_run = os.path.join(
            output_dir_main, f"{output_base_name_main}_extracted_pages.pdf"
        )

    # 2.3) список страниц для извлечения из п 2.1
    pages = [2, 3]  # Пример

    # error_log_file_path - если не указан, каталог с проектом в папке "logs"
    # Если None, setup_error_logger создаст его по умолчанию
    log_path = None
    # или можно указать явно:
    # log_path = os.path.join(_get_script_directory(), "logs", "my_custom_main_log.log")

    print(f"--- Запуск обработки из __main__ ---")
    print(f"Входной файл: {input_file}")
    print(f"Ожидаемый выходной файл: {output_file_for_run}")
    print(f"Страницы: {pages}")
    print(f"Путь к логу (если None, будет по умолчанию): {log_path}")

    # Вызов основной функции
    success_status = run_extraction_process(
        input_file, output_file_for_run, pages, log_path
    )

    if success_status:
        print(
            f"--- Обработка в __main__ завершена успешно. Итоговый файл: {output_file_for_run} ---"
        )
    else:
        print(f"--- Обработка в __main__ завершена с ошибками. Проверьте лог. ---")

    # Если вы хотите вернуть интерактивный режим:
    # 1. Создайте функции get_input_pdf_path_from_user() и get_pages_to_extract_from_user()
    # 2. Вызовите их здесь, чтобы получить input_file и pages
    # 3. Сформируйте output_file_for_run и log_path на их основе
    # 4. Вызовите run_extraction_process

    # input("\nНажмите Enter для выхода из примера __main__...") # Если нужно
