# pip install groq python-dotenv
# https://console.groq.com/playground?model=qwen-qwq-32b

import os
from groq import Groq
from dotenv import load_dotenv

# Загрузка переменных окружения из файла .env
load_dotenv()

# Получаем API ключ из переменной окружения GROK_API_KEY
api_key = os.getenv("GROK_API_KEY_FREE")
if not api_key:
    print("Ошибка: Не найден API ключ GROK_API_KEY в файле .env")
    exit(1)

# Создаем клиент Groq с API ключом
client = Groq(api_key=api_key)

content = """
ELLO POLONYA SEKERI STAND 25GX150'ELLO POLONYA SEKERI STAND 25GX150',
L<PERSON><PERSON><PERSON><PERSON> MAXXI SOUR CHERRY 10X48X28G'LOLLIPOP MAXXI SOUR CHERRY 10X48X28G',
LOLLIPOP MAXXI WATERMELON 10X48X28G'LOLLIPOP MAXXI WATERMELON 10X48X28G',
Blong Energy - Lollipop 28g 12x672g(24pcs)'Blong Energy - Lollipop 28g 12x672g(24pcs)',
BlongEnergy Mix - Lollipop 28g 12x672g(24pcs)'BlongEnergy Mix - Lollipop 28g 12x672g(24pcs)',
Blong Sour - Lollipop 28g 12x672g(24pcs)'Blong Sour - Lollipop 28g 12x672g(24pcs)',
"""
prompt = """
        Максимально точно определи и извлеки данные по каждому артикулу/sku.
        Выведи в формате СТРОГО ВАЛИДНОГО JSON. 
        поля заполни.
        {articles:[{
            "sku": str,  // наименование, которое тебе дал. Без изменений
            "grams_in_pcs": float,  // grams, ml
            "pcs_in_block": float | 1,
            "box_in_cartoon": int | 1,
            "weight_unit": float, // g, ml, kg, гр, кг, мл
            "pcs_type": str, // pcs, шт
            "box_type": str // jar, box, банка, блок
        }]}
        """

# Создаем запрос к API
completion = client.chat.completions.create(
    model="openai/gpt-oss-120b",
    messages=[
        {"role": "system", "content": prompt},
        {"role": "user", "content": content},
        {"role": "user", "content": """ты вывел данные по каждому артикулу/sku?. Из там много."""}
    ],
    temperature=0.2,
    max_completion_tokens=65000,
    top_p=0.95,
    stream=True,
    stop=None,
)

print("Ответ:")
# Выводим ответ по мере поступления
for chunk in completion:
    print(chunk.choices[0].delta.content or "", end="")
