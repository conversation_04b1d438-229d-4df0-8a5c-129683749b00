import os
import json
from google.cloud import documentai_v1 as documentai
from datetime import datetime

class DocumentAIUniversalTester:
    def __init__(self, project_id, location):
        self.project_id = project_id
        self.location = location
        self.client = documentai.DocumentProcessorServiceClient()
    
    def get_all_processors(self):
        """Получает все доступные процессоры"""
        parent = f"projects/{self.project_id}/locations/{self.location}"
        
        try:
            request = documentai.ListProcessorsRequest(parent=parent)
            processors = []
            
            for processor in self.client.list_processors(request=request):
                processors.append({
                    'name': processor.name,
                    'processor_id': processor.name.split('/')[-1],
                    'display_name': processor.display_name,
                    'type': processor.type_,
                    'state': processor.state.name
                })
            
            return processors
        except Exception as e:
            print(f"Ошибка при получении процессоров: {e}")
            return []

    def test_processor(self, processor_id, file_path, version_id=None):
        """Тестирует конкретный процессор"""
        if version_id:
            processor_name = f"projects/{self.project_id}/locations/{self.location}/processors/{processor_id}/processorVersions/{version_id}"
        else:
            processor_name = f"projects/{self.project_id}/locations/{self.location}/processors/{processor_id}"

        try:
            with open(file_path, "rb") as f:
                image_content = f.read()

            raw_document = documentai.RawDocument(
                content=image_content,
                mime_type="application/pdf",
            )

            request = documentai.ProcessRequest(
                name=processor_name,
                raw_document=raw_document,
            )
            
            result = self.client.process_document(request=request)
            return result.document
            
        except Exception as e:
            print(f"Ошибка: {e}")
            return None

    def extract_basic_info(self, document):
        """Извлекает базовую информацию из документа"""
        if not document:
            return None
        
        return {
            'raw_text': document.text,
            'pages_count': len(document.pages) if document.pages else 0,
            'text_length': len(document.text) if document.text else 0
        }

    def test_all_processors(self, file_path, output_dir="results"):
        """Тестирует все доступные процессоры"""
        print("Получаем список всех процессоров...")
        processors = self.get_all_processors()
        
        if not processors:
            print("Не найдено ни одного процессора!")
            return None
        
        print(f"Найдено процессоров: {len(processors)}")
        for processor in processors:
            print(f"  - {processor['processor_id']}: {processor['display_name']} ({processor['type']})")
        
        results = {}
        
        # Тестируем каждый процессор на последней версии
        for processor in processors:
            processor_id = processor['processor_id']
            print(f"\nТестируем процессор: {processor['display_name']} ({processor_id})")
            
            document = self.test_processor(processor_id, file_path)
            if document:
                info = self.extract_basic_info(document)
                results[processor_id] = {
                    'processor_info': processor,
                    'document_info': info,
                    'success': True
                }
                print(f"  ✓ Успешно! Страниц: {info['pages_count']}, Символов: {info['text_length']}")
            else:
                results[processor_id] = {
                    'processor_info': processor,
                    'success': False
                }
                print(f"  ✗ Ошибка")
        
        # Сохраняем все результаты в одну папку
        self.save_results(results, output_dir)
        return results

    def save_results(self, results, output_dir):
        """Сохраняет все результаты в одну папку"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        os.makedirs(output_dir, exist_ok=True)
        
        summary = []
        
        for processor_id, result in results.items():
            processor_info = result['processor_info']
            status_info = {
                'processor_id': processor_id,
                'display_name': processor_info['display_name'],
                'type': processor_info['type'],
                'success': result['success']
            }
            
            if result['success']:
                data = result['document_info']
                status_info.update({
                    'pages_count': data['pages_count'],
                    'text_length': data['text_length']
                })
                
                # Сохраняем сырой текст для успешных процессоров
                text_filename = f"{processor_id}_text.txt"
                with open(os.path.join(output_dir, text_filename), "w", encoding="utf-8") as f:
                    f.write(data['raw_text'])
            
            summary.append(status_info)
        
        # Сохраняем один сводный файл
        summary_filename = f"summary_{timestamp}.json"
        with open(os.path.join(output_dir, summary_filename), "w", encoding="utf-8") as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\nВсе результаты сохранены в: {os.path.abspath(output_dir)}")
        print(f"Сводный отчет: {summary_filename}")

# Основная функция
def main():
    # Настройки
    project_id = os.getenv("GOOGLE_PROJECT_NUMBER")
    location = os.getenv("GOOGLE_PROJECT_LOCATION")
    file_path = r"C:\Users\<USER>\Desktop\2025-08-21_105102 Vozvrat-001.pdf"
    output_dir = "results"  # Только одна папка!
    
    # Создаем тестер
    tester = DocumentAIUniversalTester(project_id, location)
    
    # Запускаем тестирование всех процессоров
    results = tester.test_all_processors(file_path, output_dir)
    
    # Анализируем результаты
    if results:
        print("\n" + "="*50)
        print("АНАЛИЗ РЕЗУЛЬТАТОВ:")
        print("="*50)
        
        successful = [p for p in results.values() if p['success']]
        failed = [p for p in results.values() if not p['success']]
        
        print(f"Успешных: {len(successful)}")
        print(f"Неудачных: {len(failed)}")
        
        if successful:
            print("\nРаботающие процессоры:")
            for result in successful:
                proc = result['processor_info']
                data = result['document_info']
                print(f"  - {proc['display_name']} ({proc['type']}): {data['text_length']} chars")
        
        if failed:
            print("\nНе работающие процессоры:")
            for result in failed:
                proc = result['processor_info']
                print(f"  - {proc['display_name']} ({proc['type']})")

if __name__ == "__main__":
    main()