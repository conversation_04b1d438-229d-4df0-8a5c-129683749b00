# pip install httpx asyncpg python-dotenv tiktoken pandas
import asyncpg
import os
from dotenv import load_dotenv
from typing import List, Dict, Any, Optional, Union
import asyncio
import json
import httpx
import tiktoken
import hashlib
import sys
import os
import pandas as pd
# Добавляем корневую папку проекта в путь для импорта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt import PROMPT_EXAMPLE_GEMINI
from DataBase import (
    create_pool, save_to_pg_cache, get_from_pg_cache,
    cleanup_expired_pg_cache, get_pg_cache_stats
)

# Загрузка переменных окружения
load_dotenv()

# Конфигурация
api_key = os.getenv("GROK_API_KEY")
DB_CONFIG = {
    'user': os.getenv('PG_USER'),
    'password': os.getenv('PG_PASSWORD'),
    'host': os.getenv('PG_HOST_LOCAL'),
    'port': int(os.getenv('PG_PORT', '5432')),
    'database': os.getenv('PG_DBNAME')
}

# Лимиты Grok-3-mini
GROK_CONTEXT_WINDOW = 131072
SAFE_CONTEXT_LIMIT = int(GROK_CONTEXT_WINDOW * 0.8)
MAX_COMPLETION_TOKENS = 65000
BATCH_SIZE = 50

# Управление кэшем - если True, то обновляет кэш принудительно
FORCE_CACHE_UPDATE = True


# Инициализация токенизатора
try:
    encoding = tiktoken.get_encoding("cl100k_base")
except:
    encoding = None

# Глобальный список для накопления всех результатов
all_results = []

# Глобальный список для накопления записей для базы данных
db_batch_records = []


# ========== НАШ PostgreSQL КЭШ ==========

# Глобальный пул соединений PostgreSQL
_pg_pool = None

async def get_pg_pool():
    """Получает или создает пул соединений PostgreSQL."""
    global _pg_pool
    if _pg_pool is None:
        _pg_pool = await create_pool()
    return _pg_pool


def get_cache_key(content: str, prompt: str) -> str:
    """Создает ключ для нашего кэша"""
    combined = f"{prompt}{content}"
    return hashlib.md5(combined.encode()).hexdigest()


async def get_from_cache(cache_key: str) -> Optional[Dict[str, Any]]:
    """Получает данные из кэша PostgreSQL"""
    try:
        pool = await get_pg_pool()
        cached_data = await get_from_pg_cache(pool, "grok", cache_key)

        if cached_data:
            return {
                'response_text': cached_data['response_text'],
                'tokens_info': cached_data.get('tokens_info', {}),
                'cached_tokens': cached_data['cached_tokens'],
                'from_cache': True
            }
        return None

    except Exception as e:
        print(f"❌ Ошибка доступа к кэшу: {e}")
        return None


async def save_to_cache(cache_key: str, response_data: Union[str, dict], tokens_info: Dict[str, Any], cached_tokens: int = 0,
                        ttl_hours: int = 24):
    """Сохраняет данные в кэш PostgreSQL"""
    try:
        pool = await get_pg_pool()

        # Преобразуем response_data в JSON строку
        response_text = json.dumps(response_data, ensure_ascii=False) if isinstance(response_data, dict) else response_data

        success = await save_to_pg_cache(
            pool, "grok", cache_key, response_text,
            tokens_info, cached_tokens, "", "", 0,
            ttl_hours
        )

        if success:
            print("💾 Данные сохранены в кэш PostgreSQL")

    except Exception as e:
        print(f"❌ Ошибка сохранения в кэш: {e}")


async def delete_cache_for_id(record_id: int, prompt: str) -> bool:
    """Удаляет кэшированные данные для конкретного ID"""
    try:
        # Получаем данные записи для генерации cache_key
        pool = await get_pg_pool()
        async with pool.acquire() as conn:
            # Получаем данные записи
            record = await conn.fetchrow(
                "SELECT id, file_name, page_number, description FROM t_scan_documents_raw WHERE id = $1",
                record_id
            )

            if not record:
                print(f"❌ Запись с ID {record_id} не найдена в базе данных")
                return False

            # Генерируем контент для кэш-ключа (точно так же, как в process_single_record_with_cache)
            content = f"Файл: {record['file_name']}\nСтраница: {record['page_number']}\nОписание: {record['description']}"
            cache_key = get_cache_key(content, prompt)

            # Удаляем из кэша PostgreSQL
            await conn.execute(
                "DELETE FROM t_grok_cache WHERE content_hash = $1",
                cache_key
            )

            print(f"✅ Удалена кэшированная запись для ID {record_id}")
            print(f"📋 Файл: {record['file_name']}, страница: {record['page_number']}")
            return True

    except Exception as e:
        print(f"❌ Ошибка при удалении кэша для ID {record_id}: {e}")
        return False


# ========== ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ ==========
def count_tokens(text: str) -> int:
    """Подсчитывает количество токенов в тексте"""
    if encoding:
        return len(encoding.encode(text))
    else:
        return len(text) // 4


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    """Очищает и парсит JSON ответ"""
    if isinstance(json_string, dict):
        return json_string
    
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            
            parsed = json.loads(json_string.strip())
            return parsed if isinstance(parsed, dict) else {}
        except json.JSONDecodeError as e:
            print(f"❌ Ошибка парсинга JSON: {e}")
            print(f"❌ Проблемные данные: {json_string[:200]}...")
            return {}
    
    return {}


async def load_all_records_to_dataframe() -> pd.DataFrame:
    """Загружает все записи из БД в pandas DataFrame одним запросом"""
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        
        sql = """
            SELECT id, file_name, page_number, description
            FROM t_scan_documents_raw
            WHERE id = 28389  -- file_name = '2025-08-21_105102 Vozvrat.pdf'
            ORDER BY id;
        """
        
        records = await conn.fetch(sql)
        await conn.close()
        
        # Преобразуем в pandas DataFrame
        if records:
            df = pd.DataFrame([
                {
                    'id': record['id'],
                    'file_name': record['file_name'],
                    'page_number': record['page_number'],
                    'description': record['description']
                }
                for record in records
            ])
            print(f"📊 Успешно загружено {len(df)} записей в DataFrame")
            return df
        else:
            print("⚠️  Не найдено записей в базе")
            return pd.DataFrame()
        
    except Exception as e:
        print(f"❌ Ошибка при загрузке данных: {e}")
        return pd.DataFrame()


def get_records_batch_from_df(df: pd.DataFrame, offset: int, limit: int) -> List[Dict[str, Any]]:
    """Получает батч записей из DataFrame (работа в памяти)"""
    try:
        # Используем iloc для получения среза данных
        batch_df = df.iloc[offset:offset + limit]
        
        # Преобразуем в список словарей
        result = batch_df.to_dict('records')
        
        return result
        
    except Exception as e:
        print(f"❌ Ошибка при получении батча из DataFrame: {e}")
        return []



# ========== ФУНКЦИИ СОХРАНЕНИЯ В БД ==========
def prepare_db_record(record: Dict[str, Any], processed_data: Dict[str, Any], 
                     content: str, reasoning_content: Optional[str] = None) -> Dict[str, Any]:
    """Подготавливает запись для батчевого сохранения в базу данных"""
    # Парсим данные из processed_data
    doc_type = processed_data.get('doc_type')
    doc_date = processed_data.get('doc_date')
    doc_number = processed_data.get('doc_number')
    buyer_name = processed_data.get('buyer_name')
    buyer_code = processed_data.get('buyer_code')
    supplier_name = processed_data.get('supplier_name')
    supplier_code = processed_data.get('supplier_code')
    amount_with_vat = processed_data.get('amount_with_vat')
    
    # Преобразуем дату
    parsed_date = None
    if doc_date:
        try:
            from datetime import datetime
            if isinstance(doc_date, str):
                # Пробуем разные форматы даты
                for fmt in ['%Y-%m-%d', '%d.%m.%Y', '%d/%m/%Y']:
                    try:
                        parsed_date = datetime.strptime(doc_date, fmt).date()
                        break
                    except ValueError:
                        continue
        except Exception as e:
            print(f"⚠️  Ошибка парсинга даты: {e}")
    
    # Преобразуем supplier_code в строку
    supplier_code_str = None
    if supplier_code is not None:
        supplier_code_str = str(supplier_code)
    
    # Преобразуем buyer_code в строку  
    buyer_code_str = None
    if buyer_code is not None:
        buyer_code_str = str(buyer_code)
    
    
    # Преобразуем сумму
    parsed_amount = None
    if amount_with_vat:
        try:
            if isinstance(amount_with_vat, (int, float)):
                parsed_amount = float(amount_with_vat)
            elif isinstance(amount_with_vat, str):
                # Удаляем пробелы и нецифровые символы
                import re
                clean_amount = re.sub(r'[^\d.,]', '', amount_with_vat)
                clean_amount = clean_amount.replace(',', '.')
                if clean_amount:
                    parsed_amount = float(clean_amount)
        except Exception as e:
            print(f"⚠️  Ошибка парсинга суммы: {e}")
    
    # JSON поля
    rows_list = processed_data.get('rows_list')
    invoices_numbers = processed_data.get('invoices_numbers')
    
    return {
        'external_id': record['id'],
        'file_name': record['file_name'],
        'page_number': record['page_number'],
        'description': content,
        'thinking_content': reasoning_content,
        'doc_type': doc_type,
        'doc_date': parsed_date,
        'doc_number': doc_number,
        'buyer_name': buyer_name,
        'buyer_code': buyer_code_str,  # Используем строковое представление
        'supplier_name': supplier_name,
        'supplier_code': supplier_code_str,  # Используем строковое представление
        'amount_with_vat': parsed_amount,
        'rows_list': rows_list,  # Передаем как есть, без json.dumps
        'invoices_numbers': invoices_numbers  # Передаем как есть, без json.dumps
    }


async def save_batch_to_database(records: List[Dict[str, Any]]) -> bool:
    """Сохраняет батч записей в таблицу t_scan_documents используя PostgreSQL UNNEST"""
    if not records:
        print("📦 Нет записей для сохранения в базу")
        return True
        
    conn = None
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        
        print(f"📦 Сохраняем {len(records)} записей в базу данных батчем...")
        
        # Подготавливаем данные для всех полей, которые триггер НЕ перезаписывает
        external_ids = []
        thinking_contents = []
        doc_types = []
        doc_dates = []
        doc_numbers = []
        buyer_names = []
        buyer_codes = []
        supplier_names = []
        supplier_codes = []
        amounts_with_vat = []
        rows_lists = []
        invoices_numbers_list = []
        
        for record in records:
            external_ids.append(record['external_id'])
            thinking_contents.append(record['thinking_content'])
            doc_types.append(record['doc_type'])
            doc_dates.append(record['doc_date'])
            doc_numbers.append(record['doc_number'])
            buyer_names.append(record['buyer_name'])
            buyer_codes.append(record['buyer_code'])
            supplier_names.append(record['supplier_name'])
            supplier_codes.append(record['supplier_code'])
            amounts_with_vat.append(record['amount_with_vat'])
            
            # JSON поля - преобразуем в строки
            rows_list_json = json.dumps(record['rows_list']) if record['rows_list'] is not None else None
            invoices_numbers_json = json.dumps(record['invoices_numbers']) if record['invoices_numbers'] is not None else None
            
            rows_lists.append(rows_list_json)
            invoices_numbers_list.append(invoices_numbers_json)
        
        # SQL с UNNEST для всех полей, которые триггер НЕ перезаписывает
        sql = """
            INSERT INTO t_scan_documents 
            (external_id, thinking_content, doc_type, doc_date, doc_number, 
             buyer_name, buyer_code, supplier_name, supplier_code, amount_with_vat,
             rows_list, invoices_numbers, created_at)
            SELECT external_id, thinking_content, doc_type, doc_date, doc_number,
                   buyer_name, buyer_code, supplier_name, supplier_code, amount_with_vat,
                   CASE WHEN rows_list IS NOT NULL THEN rows_list::text::jsonb ELSE NULL END,
                   CASE WHEN invoices_numbers IS NOT NULL THEN invoices_numbers::text::jsonb ELSE NULL END,
                   NOW() as created_at
            FROM UNNEST(
                $1::integer[],
                $2::text[],
                $3::text[],
                $4::date[],
                $5::text[],
                $6::text[],
                $7::text[],
                $8::text[],
                $9::text[],
                $10::numeric[],
                $11::text[],
                $12::text[]
            ) AS data(external_id, thinking_content, doc_type, doc_date, doc_number,
                     buyer_name, buyer_code, supplier_name, supplier_code, amount_with_vat,
                     rows_list, invoices_numbers)
            ON CONFLICT (external_id) DO UPDATE SET
                thinking_content = EXCLUDED.thinking_content,
                doc_type = EXCLUDED.doc_type,
                doc_date = EXCLUDED.doc_date,
                doc_number = EXCLUDED.doc_number,
                buyer_name = EXCLUDED.buyer_name,
                buyer_code = EXCLUDED.buyer_code,
                supplier_name = EXCLUDED.supplier_name,
                supplier_code = EXCLUDED.supplier_code,
                amount_with_vat = EXCLUDED.amount_with_vat,
                rows_list = EXCLUDED.rows_list,
                invoices_numbers = EXCLUDED.invoices_numbers,
                created_at = NOW()
        """
        
        await conn.execute(
            sql,
            external_ids,
            thinking_contents,
            doc_types,
            doc_dates,
            doc_numbers,
            buyer_names,
            buyer_codes,
            supplier_names,
            supplier_codes,
            amounts_with_vat,
            rows_lists,
            invoices_numbers_list
        )
        
        print(f"✅ Успешно сохранено {len(records)} записей в базу данных")
        return True
        
    except Exception as e:
        print(f"❌ Ошибка батчевого сохранения в базу: {e}")
        return False
    finally:
        if conn:
            await conn.close()

# ========== ОСНОВНЫЕ ФУНКЦИИ ОБРАБОТКИ ==========
async def process_single_record_with_cache(record: Dict[str, Any], prompt: str, prompt_tokens: int) -> Dict[str, Any]:
    """Обрабатывает запись с использованием ДВОЙНОГО кэширования"""
    content = f"Файл: {record['file_name']}\nСтраница: {record['page_number']}\nОписание: {record['description']}"

    # 1. ПРОВЕРЯЕМ НАШ КЭШ (полное избежание запроса) - только если не требуется обновление
    cache_key = get_cache_key(content, prompt)
    
    if not FORCE_CACHE_UPDATE:
        cached_data = get_from_cache(cache_key)

        if cached_data:
            print(f"💾 Используем НАШ кэш для ID {record['id']} - стоимость: $0.00")
            
            # Получаем полный ответ из кэша
            try:
                full_response = json.loads(cached_data['response_text']) if isinstance(cached_data['response_text'], str) else cached_data['response_text']
            except json.JSONDecodeError:
                # Если это старый кэш с обычным текстом
                full_response = cached_data['response_text']
            
            # Извлекаем processed_data и reasoning_content
            if isinstance(full_response, dict) and 'choices' in full_response:
                # Полный ответ API - извлекаем данные
                response_text = full_response.get('choices', [{}])[0].get('message', {}).get('content', '')
                reasoning_content = full_response.get('choices', [{}])[0].get('message', {}).get('reasoning_content', '')
                processed_data = clear_text(response_text)
            else:
                # Старый формат кэша - пробуем обработать как текст
                processed_data = clear_text(full_response)
                reasoning_content = ''
            
            # Извлекаем данные из формата {"doc": [{...}]}
            if isinstance(processed_data, dict) and 'doc' in processed_data:
                doc_list = processed_data.get('doc', [])
                if doc_list and isinstance(doc_list, list) and len(doc_list) > 0:
                    processed_data = doc_list[0]  # Берем первый элемент массива doc
                    print(f"📋 Извлечены данные из кэша для ID {record['id']}")
                else:
                    print(f"⚠️  Пустой массив doc в кэше для ID {record['id']}")
                    processed_data = {}
            else:
                print(f"⚠️  Неожиданный формат данных из кэша для ID {record['id']}: {type(processed_data)}")
            
            # Проверяем, что processed_data не пустой
            if not processed_data:
                print(f"⚠️  Пустые данные из кэша для ID {record['id']}. Пропускаем сохранение в базу.")
                print(f"⚠️  Кэшированные данные: {cached_data['response_text'][:200]}...")
            else:
                # Накапливаем данные для батчевого сохранения в базу
                db_record = prepare_db_record(record, processed_data, content, reasoning_content)
                db_batch_records.append(db_record)
                
            all_results.append({
                'id': record['id'],
                'file_name': record['file_name'],
                'page_number': record['page_number'],
                'original_content': content,  # Исходный текст
                'full_api_response': full_response,  # Полный ответ из кэша
                'from_cache': True
            })

            return {
                'success': True,
                'processed_data': processed_data,
                'tokens': {
                    'prompt_tokens': 0,
                    'completion_tokens': 0,
                    'reasoning_tokens': 0,
                    'cached_tokens': 0,
                    'total_tokens': 0
                },
                'record_info': {
                    'id': record['id'],
                    'file_name': record['file_name'],
                    'page_number': record['page_number']
                },
                'from_cache': True
            }
    else:
        print(f"🔄 FORCE_CACHE_UPDATE=True - обновляем кэш для ID {record['id']}")

    # 2. ЕСЛИ НЕТ В НАШЕМ КЭШЕ - ДЕЛАЕМ ЗАПРОС К API
    print(f"🌐 API запрос для ID {record['id']}")

    # Проверяем размер контента
    content_tokens = count_tokens(content)
    total_estimated_tokens = prompt_tokens + content_tokens + 1000

    if total_estimated_tokens > SAFE_CONTEXT_LIMIT:
        print(f"⚠️  Превышен безопасный лимит токенов: {total_estimated_tokens}/{SAFE_CONTEXT_LIMIT}")
        if encoding:
            max_content_tokens = SAFE_CONTEXT_LIMIT - prompt_tokens - 1000
            if max_content_tokens > 0:
                tokens = encoding.encode(content)
                if len(tokens) > max_content_tokens:
                    content = encoding.decode(tokens[:max_content_tokens])
                    print(f"✂️  Контент обрезан до {max_content_tokens} токенов")

    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            messages = [
                {"role": "system", "content": prompt},
                {"role": "user", "content": content},
            ]

            request_data = {
                "model": "grok-3-mini",
                "reasoning_effort": "high",
                "messages": messages,
                "temperature": 0.1,
                "max_completion_tokens": min(MAX_COMPLETION_TOKENS, SAFE_CONTEXT_LIMIT - count_tokens(str(messages))),
                "top_p": 0.1,
                "stream": False,
                "response_format": {"type": "json_object"},
                "stop": None,
            }

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            response = await client.post(
                "https://api.x.ai/v1/chat/completions",
                json=request_data,
                headers=headers
            )
            response.raise_for_status()

            response_json = response.json()
            usage = response_json.get('usage', {})

            # ИЗВЛЕКАЕМ reasoning_content и cached_tokens
            reasoning_content = response_json["choices"][0]["message"].get('reasoning_content', '')
            reasoning_tokens = count_tokens(reasoning_content) if reasoning_content else 0

            # ПОЛУЧАЕМ cached_tokens из ответа API (системный кэш)
            prompt_details = usage.get('prompt_tokens_details', {})
            cached_tokens = prompt_details.get('cached_tokens', 0)

            tokens_info = {
                'prompt_tokens': usage.get('prompt_tokens', 0),
                'completion_tokens': usage.get('completion_tokens', 0),
                'reasoning_tokens': reasoning_tokens,
                'cached_tokens': cached_tokens,  # Токены из системного кэша
                'total_tokens': usage.get('total_tokens', 0)
            }
            
            response_text = response_json["choices"][0]["message"]["content"]
            processed_data = clear_text(response_text)

            # Извлекаем данные из формата {"doc": [{...}]}
            if isinstance(processed_data, dict) and 'doc' in processed_data:
                doc_list = processed_data.get('doc', [])
                if doc_list and isinstance(doc_list, list) and len(doc_list) > 0:
                    processed_data = doc_list[0]  # Берем первый элемент массива doc
                    print(f"📋 Извлечены данные из API для ID {record['id']}")
                else:
                    print(f"⚠️  Пустой массив doc в API ответе для ID {record['id']}")
                    processed_data = {}
            else:
                print(f"⚠️  Неожиданный формат API ответа для ID {record['id']}: {type(processed_data)}")

            # Сохраняем в кэш ТОЛЬКО если есть полезные данные
            if processed_data and any(processed_data.values()):  # Проверяем, что processed_data не пустой
                await save_to_cache(cache_key, response_json, tokens_info, cached_tokens)
                print(f"💾 Сохранено в кэш для ID {record['id']}")
            else:
                print(f"⚠️  Пустые данные - НЕ сохраняем в кэш для ID {record['id']}")

            # Накапливаем данные для батчевого сохранения в базу
            db_record = prepare_db_record(record, processed_data, content, reasoning_content)
            db_batch_records.append(db_record)

            # Добавляем результат в глобальный список - ПОЛНЫЙ ответ API для анализа
            all_results.append({
                'id': record['id'],
                'file_name': record['file_name'],
                'page_number': record['page_number'],
                'original_content': content,  # Исходный текст, отправленный в API
                'full_api_response': response_json,  # Полный сырой ответ с рассуждениями
                'from_cache': False
            })

            return {
                'success': True,
                'processed_data': processed_data,
                'tokens': tokens_info,
                'record_info': {
                    'id': record['id'],
                    'file_name': record['file_name'],
                    'page_number': record['page_number']
                },
                'from_cache': False
            }

    except Exception as e:
        print(f"❌ Ошибка при обработке записи {record['id']}: {e}")
        return {
            'success': False,
            'error': str(e),
            'record_info': {
                'id': record['id'],
                'file_name': record['file_name'],
                'page_number': record['page_number']
            },
            'tokens': {
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'reasoning_tokens': 0,
                'cached_tokens': 0,
                'total_tokens': 0
            },
            'from_cache': False
        }


async def process_all_records_safe():
    """Безопасная обработка с ДВОЙНЫМ кэшированием и pandas DataFrame"""
    # Очищаем глобальные списки перед началом
    global all_results, db_batch_records
    all_results = []
    db_batch_records = []

    print("🚀 Запускаем обработку с ДВОЙНЫМ кэшированием и pandas DataFrame...")
    print("💾 1. Наш кэш: полное избежание запросов ($0.00)")
    print("💾 2. Системный кэш: снижение стоимости ($0.075/M)")

    # Считаем токены промпта ОДИН РАЗ
    prompt_tokens = count_tokens(PROMPT_EXAMPLE_GEMINI)
    print(f"🧮 Токены промпта (учтены 1 раз): {prompt_tokens}")

    # ЗАГРУЖАЕМ ВСЕ ДАННЫЕ В DataFrame ОДНИМ ЗАПРОСОМ
    print("📊 Загружаем все данные из базы в DataFrame...")
    df = await load_all_records_to_dataframe()
    
    if df.empty:
        print("⚠️  DataFrame пуст. Обработка остановлена.")
        return
    
    total_records = len(df)
    print(f"📊 Всего записей для обработки: {total_records}")

    batch_size = BATCH_SIZE
    offset = 0
    total_stats = {
        'processed': 0,
        'our_cache': 0,
        'api_calls': 0,
        'errors': 0,
        'tokens': {
            'prompt': prompt_tokens,
            'completion': 0,
            'reasoning': 0,
            'cached': 0,  # Токены из системного кэша
            'total_cost': 0.0
        }
    }

    while offset < total_records:
        # ПОЛУЧАЕМ БАТЧ ИЗ DataFrame (без обращения к БД!)
        records = get_records_batch_from_df(df, offset, batch_size)
        if not records:
            break

        print(f"📦 Батч {offset // batch_size + 1}: {len(records)} записей (работа с DataFrame в памяти)")

        tasks = []
        for record in records:
            tasks.append(process_single_record_with_cache(record, PROMPT_EXAMPLE_GEMINI, prompt_tokens))

        results = await asyncio.gather(*tasks)

        # Анализируем результаты
        for result in results:
            total_stats['processed'] += 1

            if result['success']:
                if result.get('from_cache'):
                    total_stats['our_cache'] += 1
                else:
                    total_stats['api_calls'] += 1

                    # Суммируем токены из API запросов
                    tokens = result['tokens']
                    total_stats['tokens']['completion'] += tokens['completion_tokens']
                    total_stats['tokens']['reasoning'] += tokens['reasoning_tokens']
                    total_stats['tokens']['cached'] += tokens['cached_tokens']

            else:
                total_stats['errors'] += 1

        offset += batch_size

        # Выводим статистику
        our_cache_rate = (total_stats['our_cache'] / total_stats['processed'] * 100) if total_stats['processed'] > 0 else 0
        print(f"   📊 Наш кэш: {our_cache_rate:.1f}% | API: {total_stats['api_calls']} | Ошибки: {total_stats['errors']}")

        # Безопасная пауза
        await asyncio.sleep(0.3)

    # РАСЧЕТ СТОИМОСТИ
    fresh_prompt_tokens = total_stats['tokens']['prompt'] * total_stats['api_calls']
    cached_prompt_tokens = total_stats['tokens']['cached']
    fresh_prompt_cost = (fresh_prompt_tokens * 0.30) / 1_000_000
    cached_prompt_cost = (cached_prompt_tokens * 0.075) / 1_000_000
    completion_cost = (total_stats['tokens']['completion'] * 0.50) / 1_000_000
    reasoning_cost = (total_stats['tokens']['reasoning'] * 0.50) / 1_000_000

    total_cost = fresh_prompt_cost + cached_prompt_cost + completion_cost + reasoning_cost

    # Финальная статистика
    print(f"\n🎯 ОБРАБОТКА ЗАВЕРШЕНА!")
    print(f"✅ Всего обработано: {total_stats['processed']}")
    print(f"💾 Из нашего кэша: {total_stats['our_cache']} (${0.00})")
    print(f"🌐 API запросов: {total_stats['api_calls']}")
    print(f"❌ Ошибок: {total_stats['errors']}")

    print(f"\n🧮 ДЕТАЛЬНАЯ СТАТИСТИКА ТОКЕНОВ:")
    print(f"   Свежие промпт токены: {fresh_prompt_tokens} (${fresh_prompt_cost:.6f})")
    print(f"   Кэшированные промпт токены: {cached_prompt_tokens} (${cached_prompt_cost:.6f})")
    print(f"   Ответы: {total_stats['tokens']['completion']} (${completion_cost:.6f})")
    print(f"   Рассуждения: {total_stats['tokens']['reasoning']} (${reasoning_cost:.6f})")
    print(f"   💰 ОБЩАЯ СТОИМОСТЬ: ${total_cost:.6f}")

    if total_stats['processed'] > 0:
        economy = (total_stats['our_cache'] / total_stats['processed'] * 100)
        print(f"   💵 Экономия нашего кэша: {economy:.1f}% запросов")

    # Сохраняем все результаты в JSON файл
    if all_results:
        with open('all_grok_results.json', 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 Сохранено {len(all_results)} результатов в all_grok_results.json")
    else:
        print("\n⚠️  Нет результатов для сохранения")
    
    # Батчевое сохранение в базу данных
    if db_batch_records:
        print(f"\n📦 Начинаем батчевое сохранение в базу данных...")
        batch_success = await save_batch_to_database(db_batch_records)
        if batch_success:
            print(f"✅ Все {len(db_batch_records)} записей успешно сохранены в базу")
        else:
            print(f"❌ Ошибка при батчевом сохранении в базу")
    else:
        print("\n⚠️  Нет записей для сохранения в базу")


# ========== ФУНКЦИИ ПОДКЛЮЧЕНИЯ К БД ==========
async def check_db_connection():
    """Проверяет подключение к базе данных"""
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        print("✅ Подключение к PostgreSQL успешно")
        await conn.close()
        return True
    except Exception as e:
        print(f"❌ Ошибка подключения к PostgreSQL: {e}")
        return False


# ========== ЗАПУСК ПРОГРАММЫ ==========
async def main():
    """Основная функция"""
    # Проверка переменных окружения
    required_vars = ['PG_USER', 'PG_PASSWORD', 'PG_HOST_LOCAL', 'PG_PORT', 'PG_DBNAME', 'GROK_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"❌ Отсутствуют переменные: {missing_vars}")
        exit(1)

    # Проверяем подключение к БД
    if not await check_db_connection():
        exit(1)

    # Запускаем обработку
    await process_all_records_safe()


if __name__ == "__main__":
    # Установите зависимости: pip install httpx asyncpg python-dotenv tiktoken
    
    # Для удаления кэша конкретного ID раскомментируйте строку ниже:
    # success = delete_cache_for_id(28389, PROMPT_EXAMPLE_GEMINI)
    # if success:
    #     print("🗑️ Кэш для ID 28389 успешно удален")
    # else:
    #     print("❌ Не удалось удалить кэш для ID 28389")
    
    asyncio.run(main())
