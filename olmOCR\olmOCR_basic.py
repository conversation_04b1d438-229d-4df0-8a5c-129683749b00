# pip install olmocr
import torch
import base64
from io import BytesIO
from PIL import Image
from transformers import AutoProcessor, Qwen2VLForConditionalGeneration

from olmocr.data.renderpdf import render_pdf_to_base64png
from olmocr.prompts import build_finetuning_prompt
from olmocr.prompts.anchor import get_anchor_text

# Инициализируем модель
model = Qwen2VLForConditionalGeneration.from_pretrained(
    "allenai/olmOCR-7B-0225-preview",
    torch_dtype=torch.bfloat16
).eval()
processor = AutoProcessor.from_pretrained("Qwen/Qwen2-VL-7B-Instruct")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)

# Указываем путь к локальному PDF
local_pdf_path = r"C:\Rasim\Python\ScanDocument\ScanForTreaningNew.pdf"

# Рендерим первую страницу в изображение
image_base64 = render_pdf_to_base64png(local_pdf_path,
                                        page_num=1,
                                        target_longest_image_dim=1024)

# Строим «anchor text» для первой страницы
# Используем параметр 'page' вместо 'page_num'
anchor_text = get_anchor_text(local_pdf_path,
                             page=1,
                             pdf_engine="pdfreport",
                             target_length=4000)

# Если нужен anchor_text только для первой страницы, можно попробовать:
# anchor_text = get_anchor_text(local_pdf_path, pdf_engine="pdfreport")[:4000]

# Собираем финальный prompt для модели
prompt = build_finetuning_prompt(anchor_text)

# Формируем сообщение для модели
messages = [
    {
        "role": "user",
        "content": [
            {"type": "text", "text": prompt},
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/png;base64,{image_base64}"}
            },
        ],
    }
]

# Применяем чат-шаблон и процессор
text = processor.apply_chat_template(
    messages,
    tokenize=False,
    add_generation_prompt=True
)
main_image = Image.open(BytesIO(base64.b64decode(image_base64)))

inputs = processor(
    text=[text],
    images=[main_image],
    padding=True,
    return_tensors="pt",
)
inputs = {key: value.to(device) for (key, value) in inputs.items()}

# Генерируем ответ
output = model.generate(
    **inputs,
    temperature=0.1,
    # max_new_tokens=5000,
    num_return_sequences=1,
    do_sample=True,
)

# Извлекаем только новые токены
prompt_length = inputs["input_ids"].shape[1]
new_tokens = output[:, prompt_length:]
text_output = processor.tokenizer.batch_decode(
    new_tokens,
    skip_special_tokens=True
)

print(text_output)