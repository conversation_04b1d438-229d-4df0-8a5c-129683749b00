# check_library.py
# Этот скрипт не пытается ничего извлечь. 
# Его единственная цель — проанализировать установленную библиотеку kreuzberg
# и показать нам ее реальную структуру.

print("--- НАЧАЛО ДИАГНОСТИКИ БИБЛИОТЕКИ KREUZBERG ---")

try:
    import kreuzberg

    print(f"\n[OK] Библиотека 'kreuzberg' успешно импортирована.")
    print(f"Путь к файлу __init__.py: {kreuzberg.__file__}")

    # Получаем список всех доступных имен в главном пакете
    available_names = dir(kreuzberg)
    
    print("\n--- Содержимое верхнего уровня пакета 'kreuzberg' ---")
    # Печатаем имена в несколько колонок для удобства
    for i in range(0, len(available_names), 4):
        print("  ".join(f"{name:<30}" for name in available_names[i:i+4]))
    print("----------------------------------------------------")

    # Проверяем наличие ключевых имен, которые мы пытались использовать
    print("\n--- Проверка наличия нужных нам классов ---")
    if 'extract_file' in available_names:
        print("[OK]  'extract_file' НАЙДЕН.")
    else:
        print("[!!] 'extract_file' НЕ НАЙДЕН.")

    if 'ExtractionConfig' in available_names:
        print("[OK]  'ExtractionConfig' НАЙДЕН.")
    else:
        print("[!!] 'ExtractionConfig' НЕ НАЙДЕН.")
        
    if 'OcrConfig' in available_names:
        print("[OK]  'OcrConfig' НАЙДЕН.")
    else:
        print("[!!] 'OcrConfig' НЕ НАЙДЕН.")
    print("------------------------------------------")


    # Пытаемся импортировать предполагаемый подмодуль .config
    print("\n--- Попытка импорта подмодуля 'kreuzberg.config' ---")
    try:
        import kreuzberg.config
        print("[OK] Подмодуль 'kreuzberg.config' УСПЕШНО импортирован.")
        print("\n--- Содержимое подмодуля 'kreuzberg.config' ---")
        config_names = dir(kreuzberg.config)
        for i in range(0, len(config_names), 4):
            print("  ".join(f"{name:<30}" for name in config_names[i:i+4]))
        print("-------------------------------------------------")

    except ImportError:
        print("[INFO] Подмодуль 'kreuzberg.config' НЕ СУЩЕСТВУЕТ. Это объясняет предыдущие ошибки.")
    except Exception as e:
        print(f"[!!] Произошла неожиданная ошибка при импорте 'kreuzberg.config': {e}")
    print("------------------------------------------------------")


except ImportError:
    print("\n[КРИТИЧЕСКАЯ ОШИБКА] Не удалось импортировать даже базовый пакет 'kreuzberg'.")
    print("Пожалуйста, убедитесь, что виртуальное окружение активировано и библиотека установлена.")
except Exception as e:
    print(f"\n[КРИТИЧЕСКАЯ ОШИБКА] Произошла непредвиденная ошибка: {e}")

print("\n--- ДИАГНОСТИКА ЗАВЕРШЕНА ---")