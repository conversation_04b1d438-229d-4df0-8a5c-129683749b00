# Извлекает тип документа, номер и дату из текста

from operator import contains
import re
from datetime import datetime
import os
import json
import asyncio

if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")


def get_doc_type(text):
    if text:
        text = text.lower()

        if text in ["видаткова накладна", "sales_invoice"]:
            return "ВН"
        elif "транспорт" in text or text in [
            "товарно-транспортна накладна",
            "transport_invoice",
            "ттн",
        ]:
            return "ТТН"

    return None


def extract_doc_date_and_number_vidatkova(text) -> dict:
    # Удаляем переносы строк, лишние пробелы. Вместо № распознается как Ме, тоже удаляем
    text = (
        text.replace("\n", " ").replace(" Ме", " ").replace("  ", " ").strip().lower()
    )

    # Словарь месяцев
    months_ua = {
        "січня": "01",
        "лютого": "02",
        "березня": "03",
        "квітня": "04",
        "травня": "05",
        "червня": "06",
        "липня": "07",
        "серпня": "08",
        "вересня": "09",
        "жовтня": "10",
        "листопада": "11",
        "грудня": "12",
    }

    # Регулярное выражение: тип документа (до "№" или до даты), потом номер, потом дата
    pattern = r"([А-Яа-я\s]+накладна).*?(?:№\s*)?(\d+[-/\d\w]*)[^.\n\d]*(\d{1,2})\s+([А-Яа-я]+)\s+(\d{4})"

    match = re.search(pattern, text, re.IGNORECASE)

    if match:
        doc_type = get_doc_type(
            match.group(1).strip()
        )  # Вызов функции для получения типа документа из текста
        doc_number = match.group(2).strip()
        day = match.group(3)
        month_ua = match.group(4).lower()
        year = match.group(5)

        month = months_ua.get(month_ua, "00")

        return {
            "doc_type": doc_type,
            "doc_number": doc_number.zfill(5),
            "doc_date": f"{year} {month} {day.zfill(2)}",
        }
    else:
        return {}


def extract_doc_date_and_number_ttn(text) -> json:
    # Удаляем переносы строк, лишние пробелы. Вместо № распознается как Ме, тоже удаляем
    text = (
        text.replace("\n", " ")
        .replace(" Ме", " ")
        .replace("  ", " ")
        .replace("-", " ")
        .strip()
        .lower()
    )
    pattern = r"(товарн\w*[\s\-]+транспортн\w*[\s]+накладна).*?(\d+)|((?:видат\w*[\s]+накладна).*?)\s+(\d+)"
    matches = re.findall(pattern, text, flags=re.IGNORECASE | re.DOTALL)

    results = {}
    doc_number = None
    for match in matches:
        if match[0]:  # Если найдена ТТН
            doc_number = str(
                int(match[1])
            )  # Преобразуем в число и обратно в строку для удаления ведущих нулей
        elif match[2] and doc_number:  # Если найдена видаткова и уже есть номер ТТН
            doc_number = f"{doc_number}/{str(int(match[3])).zfill(5)}"  # Добавляем номер места в накладной
            return {"doc_type": "ТТН", "doc_number": doc_number}

    return results


if __name__ == "__main__":
    # Пример использования
    text = "Видаткова накладна № 123 від 15 травня 2023 р."  # Пример текста документа с заголовком
    result = extract_doc_date_and_number_vidatkova(
        text
    )  # Вызов функции для извлечения даты и номера документа из текста
    print(result)  # Вывод результата на консоль
