from openai import AsyncOpenAI
from openai.types.chat.chat_completion_message import ChatCompletionMessage
from openai.types.chat.chat_completion_system_message_param import ChatCompletionSystemMessageParam
from openai.types.chat.chat_completion_user_message_param import ChatCompletionUserMessageParam
from typing import Dict, Any, <PERSON>, Tuple
from dotenv import load_dotenv, dotenv_values
import os
import json
import asyncio
import hashlib
from DataBase import create_pool, get_from_pg_cache, save_to_pg_cache
from prompt import PROMPT_EXAMPLE_GEMINI

if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")

load_dotenv()
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
client = AsyncOpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]
                
            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            # print(extract_data) # <-- Оригинальный print, оставляю как есть
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}") # <-- Оригинальный print
            return {}
    return json_string if isinstance(json_string, dict) else {}

async def add_thinking_content_to_dict(model: str, response: ChatCompletionMessage) -> Dict[str, Any]:
    # Ответ. Что нам и надо. Проверяем чтобы был корректный JSON
    content = clear_text(response.content if response.content else "")

    # deepseek-reasoner есть reasoning_content. Обоснование ответа.
    # Если ответ дает не точный, можно отследить причину и изменить промт
    if model != "deepseek-reasoner": # Имя модели из вашего оригинального кода
        return content

    # Получаем обоснование ответа.
    thinking_content = getattr(response, 'reasoning_content', "")  # reasoning_content - чем обосновывает свой ответ

    # Проверка, что 'doc' есть в data и он является списком
    if not content or 'doc' not in content or not isinstance(content.get('doc', []), list):
        return content

    # Переводим обоснование на русский. Используем Gemini API
    # КЛЮЧЕВОЕ ИЗМЕНЕНИЕ: делаем вызов translate_by_gemini асинхронным
    # Это потребует, чтобы translate_by_gemini была async def или обернута
    # thinking_content = await translate_by_gemini(thinking_content)

    # Добавляем thinking_content в каждый элемент списка
    for item in content['doc']:
        if isinstance(item, dict):
            item['thinking_content'] = thinking_content 

    return content

def generate_cache_key(content: str, model: str, prompt: str) -> str:
    """Генерирует ключ кэша на основе контента, модели и промпта"""
    combined = f"{content}|{model}|{prompt}"
    return hashlib.sha256(combined.encode('utf-8')).hexdigest()

async def extract_data_by_deepseek(content: str, model: str = "deepseek-chat", prompt = PROMPT_EXAMPLE_GEMINI) -> Tuple[Dict[str, Any], Any]:
    # Генерируем ключ кэша
    cache_key = generate_cache_key(content, model, prompt)
    
    # Проверяем кэш
    pool = await create_pool()
    if pool:
        try:
            cached_result = await get_from_pg_cache(pool, "deepseek", cache_key)
            if cached_result:
                print("✅ Данные получены из кэша DeepSeek")
                # Возвращаем кэшированные данные в ожидаемом формате
                cached_data = clear_text(cached_result['response_text'])
                token_count = cached_result.get('tokens_info', {})
                return cached_data, token_count
        except Exception as e:
            print(f"⚠️ Ошибка чтения из кэша: {e}")
        finally:
            await pool.close()

    # Если в кэше нет данных, делаем API запрос
    generation_config = {
        "temperature": 0.1,  # Строгий режим для документов
        "top_p": 0.3,        # Уже диапазон (вместо 0.95)
        # "top_k": 30,       # REMOVE THIS LINE - not supported by OpenAI client
        "max_tokens": 8192   # Change 'max_output_tokens' to 'max_tokens'
    }

    system_message = ChatCompletionSystemMessageParam(role="system", content=prompt)
    user_message = ChatCompletionUserMessageParam(role="user", content=content)

    response = await client.chat.completions.create(
        model=model,
        messages=[system_message, user_message],
        response_format={"type": "json_object"},
        **generation_config
    )
    
    token_count = response.usage
    print(f"Количество токенов: {token_count}")
    data = response.choices[0].message
    result = await add_thinking_content_to_dict(model, data)
    
    # Сохраняем результат в кэш
    pool = await create_pool()
    if pool:
        try:
            await save_to_pg_cache(
                pool, "deepseek", cache_key, 
                json.dumps(result, ensure_ascii=False),
                token_count, 
                model=model, 
                prompt_hash=hashlib.sha256(prompt.encode('utf-8')).hexdigest()
            )
            print("💾 Результат сохранен в кэш DeepSeek")
        except Exception as e:
            print(f"⚠️ Ошибка сохранения в кэш: {e}")
        finally:
            await pool.close()
    
    return result, token_count


async def main(): # Имя main() оставлено как в оригинале
    contents = "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА № 11005867 від 13.12.2024 р."
    prompt = "Извлеки дату из этого текста: тип, номер и дату в формате dd.mm.yyyy. Верни только валидный JSON. "
    result = await extract_data_by_deepseek(contents, "deepseek-chat", prompt) # Модель "deepseek-chat" из вашего примера
    print(result) 


if __name__ == "__main__":        
    asyncio.run(main())
