# Запустите этот фрагмент. Он покажет вам точные имена моделей, которые вы можете использовать с вашим API-ключом.

import google.generativeai as genai
import os
from dotenv import load_dotenv
load_dotenv()

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY_PRESTIGE')
if not GEMINI_API_KEY:
    print("Ошибка: Переменная окружения GEMINI_API_KEY_PRESTIGE не установлена.")
    exit()

genai.configure(api_key=GEMINI_API_KEY)

print("Доступные модели, поддерживающие 'generateContent':")
found_models = False
for m in genai.list_models():
  if 'generateContent' in m.supported_generation_methods:
    print(f"{m.name}; Input token limit: {m.input_token_limit}; Output token limit: {m.output_token_limit}")
    found_models = True

if not found_models:
    print("Не найдено моделей, поддерживающих 'generateContent'. Проверьте ваш API ключ и квоты.")
    exit()