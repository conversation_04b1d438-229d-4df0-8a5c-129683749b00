# === ДОКУМЕНТАЦИЯ: АСИНХРОННАЯ СИСТЕМА С ДВОЙНЫМ КЭШИРОВАНИЕМ ===
"""
АСИНХРОННАЯ ОБРАБОТКА ФАЙЛОВ С КЭШИРОВАНИЕМ

Эта система предоставляет мощные возможности для асинхронной обработки файлов с помощью Google Gemini API
с двойным уровнем кэширования для оптимизации производительности и стоимости.

ОСНОВНЫЕ ФУНКЦИИ:
1. extract_entity_with_cache_async() - Обработка одного файла с кэшированием
2. process_batch_with_cache_async() - Пакетная обработка нескольких файлов
3. get_cache_stats() - Получение статистики кэша
4. cleanup_expired_cache() - Очистка истекшего кэша

ПЕРЕМЕННЫЕ ОКРУЖЕНИЯ ДЛЯ НАСТРОЙКИ ЦЕН:
- GEMINI_2_5_FLASH_INPUT_COST - Стоимость входных токенов для gemini-2.5-flash
- GEMINI_2_5_FLASH_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-flash
- GEMINI_2_5_FLASH_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-flash
- GEMINI_2_5_PRO_INPUT_COST - Стоимость входных токенов для gemini-2.5-pro
- GEMINI_2_5_PRO_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-pro
- GEMINI_2_5_PRO_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-pro
- GEMINI_2_5_FLASH_LITE_INPUT_COST - Стоимость входных токенов для gemini-2.5-flash-lite
- GEMINI_2_5_FLASH_LITE_OUTPUT_COST - Стоимость выходных токенов для gemini-2.5-flash-lite
- GEMINI_2_5_FLASH_LITE_CACHED_COST - Стоимость кэшированных токенов для gemini-2.5-flash-lite

ПАРАМЕТРЫ КОНФИГУРАЦИИ:
- CACHE_DB_PATH: Путь к файлу базы данных кэша (по умолчанию: "gemini_cache.db")
- CACHE_TTL_HOURS: Время жизни кэша в часах (по умолчанию: 24)
- FORCE_CACHE_UPDATE: Принудительное обновление кэша (по умолчанию: False)

ПРИМЕР ИСПОЛЬЗОВАНИЯ:

import asyncio
from GeminiAI import extract_entity_with_cache_async, process_batch_with_cache_async

async def main():
    # Обработка одного файла
    result = await extract_entity_with_cache_async(
        file_path="path/to/file.jpg",
        prompt="Извлеки текст из изображения",
        use_cache=True
    )

    # Пакетная обработка
    files = ["file1.jpg", "file2.jpg", "file3.jpg"]
    results = await process_batch_with_cache_async(
        file_paths=files,
        prompt="OCR обработка",
        max_concurrent=3,
        use_cache=True
    )

asyncio.run(main())

ПРЕИМУЩЕСТВА:
- Высокая производительность за счет асинхронной обработки
- Значительная экономия на повторяющихся запросах
- Кэширование в PostgreSQL (общий пул с основными данными)
- Автоматическая очистка истекшего кэша
- Детальная статистика использования и стоимости
- Устойчивость к ошибкам API с повторными попытками
- Гибкая настройка цен через переменные окружения
- Единая система управления данными
"""
# pip install google-genai

import ast
import base64
import hashlib
import json
import os
import sys
from typing import Union, Dict, Any
import fitz
from google import genai
from google.genai import types
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from prompt import PROMPT_EXAMPLE_GEMINI, PROMPT_OCR_CONTROL
from DataBase import (
    create_pool, save_to_pg_cache, get_from_pg_cache,
    cleanup_expired_pg_cache, get_pg_cache_stats
)
from dotenv import load_dotenv
import asyncio
import aiofiles

load_dotenv()
 
"""
"gemini-2.5-flash-lite" Reasonably; Free 15 RPM 500 RPD; IN 0.10$ OUT 0.40$. 4000 RPM
"gemini-2.5-flash" Reasonably; Free 10 RPM 500 RPD; IN 0.30$ OUT 2.50$. 1000 RPM
"gemini-2.5-pro" Not Free 150 RPM; IN 1.25$ OUT 10$
GEMINI_MODEL_2F = "gemini-2.0-flash"; Не рассуждает; Free 15 RPM 1500 RPD; IN 0.10$ OUT 0.40$ 2000 RPM
"""
MODEL = "gemini-2.5-flash"  # "gemini-2.5-flash-lite"  # os.getenv('GEMINI_MODEL_2F')  # "gemini-2.5-flash"  # "gemini-2.5-pro"
# Глобальная конфигурация API ключа
api_key = os.environ.get("GEMINI_API_KEY_PRESTIGE")  # GEMINI_API_KEY
if not api_key:
    raise ValueError("API ключ 'GEMINI_API_KEY' не найден в .env файле.")

# === НАСТРОЙКИ КЭША ===
CACHE_TTL_HOURS = 24  # Время жизни кэша в часах
FORCE_CACHE_UPDATE = False  # Принудительное обновление кэша

# === НАСТРОЙКИ АВТОМАТИЧЕСКОГО ПЕРЕКЛЮЧЕНИЯ МОДЕЛЕЙ ===
PRIMARY_MODEL = "gemini-2.5-flash"      # Основная модель (самая мощная)
FALLBACK_MODEL = "gemini-2.5-flash-lite" # Резервная модель (выше лимиты)
CURRENT_MODEL = PRIMARY_MODEL            # Текущая активная модель
RATE_LIMIT_COOLDOWN = 300                # 5 минут cooldown перед возвратом к основной модели
LAST_RATE_LIMIT_TIME = 0                 # Время последней ошибки 429


def switch_to_fallback_model():
    """Переключается на резервную модель при ошибке 429."""
    global CURRENT_MODEL, LAST_RATE_LIMIT_TIME
    if CURRENT_MODEL == PRIMARY_MODEL:
        CURRENT_MODEL = FALLBACK_MODEL
        LAST_RATE_LIMIT_TIME = asyncio.get_event_loop().time()
        print(f"Переключаемся на резервную модель: {CURRENT_MODEL}")
        print(f"Лимиты: 15 RPM (вместо 10 RPM)")
        return True
    return False


def try_switch_back_to_primary():
    """Пытается вернуться к основной модели после cooldown периода."""
    global CURRENT_MODEL, LAST_RATE_LIMIT_TIME
    if CURRENT_MODEL == FALLBACK_MODEL:
        current_time = asyncio.get_event_loop().time()
        if current_time - LAST_RATE_LIMIT_TIME >= RATE_LIMIT_COOLDOWN:
            CURRENT_MODEL = PRIMARY_MODEL
            print(f"Возвращаемся к основной модели: {CURRENT_MODEL}")
            print(f"Лимиты: 10 RPM (повышенная мощность)")
            return True
    return False


def get_current_model():
    """Получает текущую активную модель."""
    try_switch_back_to_primary()  # Проверяем, можно ли вернуться к основной
    return CURRENT_MODEL


def get_model_limits_info(model: str) -> str:
    """Получает информацию о лимитах модели."""
    limits = {
        "gemini-2.5-flash": "10 RPM / 500 RPD",
        "gemini-2.5-flash-lite": "15 RPM / 500 RPD",
        "gemini-2.5-pro": "150 RPM / ∞ RPD"
    }
    return limits.get(model, "Неизвестно")

# Стоимость токенов (USD за 1M токенов) - можно переопределить через переменные окружения
DEFAULT_COST_PRICING = {
    "gemini-2.5-flash": {"input": 0.30, "output": 2.50, "cached": 0.075},
    "gemini-2.5-pro": {"input": 1.25, "output": 10.00, "cached": 0.30},
    "gemini-2.5-flash-lite": {"input": 0.10, "output": 0.40, "cached": 0.0375}
}

def get_cost_pricing():
    """Получает актуальные цены из переменных окружения или использует значения по умолчанию."""
    pricing = {}

    for model in DEFAULT_COST_PRICING.keys():
        model_pricing = {}

        # Input cost
        input_env = f"{model.upper().replace('-', '_')}_INPUT_COST"
        model_pricing['input'] = float(os.getenv(input_env, DEFAULT_COST_PRICING[model]['input']))

        # Output cost
        output_env = f"{model.upper().replace('-', '_')}_OUTPUT_COST"
        model_pricing['output'] = float(os.getenv(output_env, DEFAULT_COST_PRICING[model]['output']))

        # Cached cost
        cached_env = f"{model.upper().replace('-', '_')}_CACHED_COST"
        model_pricing['cached'] = float(os.getenv(cached_env, DEFAULT_COST_PRICING[model]['cached']))

        pricing[model] = model_pricing

    return pricing

# Глобальная переменная с актуальными ценами
COST_PRICING = get_cost_pricing()


# === ФУНКЦИИ УПРАВЛЕНИЯ КЭШЕМ ===

# Глобальный пул соединений PostgreSQL
_pg_pool = None

async def get_pg_pool():
    """Получает или создает пул соединений PostgreSQL."""
    global _pg_pool
    if _pg_pool is None:
        _pg_pool = await create_pool()
    return _pg_pool


def get_cache_key(file_path: str, prompt: str, model: str) -> str:
    """Генерирует уникальный ключ кэша на основе файла, промпта и модели."""
    try:
        # Получаем размер файла
        file_size = os.path.getsize(file_path)

        # Создаем хэш на основе пути к файлу, размера, промпта и модели
        content = f"{file_path}:{file_size}:{prompt}:{model}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    except Exception as e:
        print(f"Ошибка при генерации ключа кэша: {e}")
        return ""


async def get_from_cache(cache_key: str) -> Union[str, None]:
    """Получает результат из кэша PostgreSQL."""
    if not cache_key:
        return None

    try:
        pool = await get_pg_pool()
        cached_data = await get_from_pg_cache(pool, "gemini", cache_key)

        if cached_data:
            if cached_data['cached_tokens'] > 0:
                print(f"Найдено в кэше (системный кэш: {cached_data['cached_tokens']} токенов)")
            else:
                print("Найдено в локальном кэше")
            return cached_data['response_text']

        return None

    except Exception as e:
        print(f"Ошибка при чтении из кэша: {e}")
        return None


async def save_to_cache(cache_key: str, response_text: str, tokens_info = None, cached_tokens: int = 0):
    """Сохраняет результат в кэш PostgreSQL."""
    if not cache_key or not response_text:
        return

    try:
        pool = await get_pg_pool()

        # Получаем размер файла из cache_key (если нужно)
        file_size = 0  # Пока не используем

        # Генерируем prompt_hash
        prompt_hash = ""  # Пока не используем

        # Получаем текущую модель для сохранения
        current_model = get_current_model()

        success = await save_to_pg_cache(
            pool, "gemini", cache_key, response_text,
            tokens_info or {}, cached_tokens, current_model, prompt_hash, file_size,
            CACHE_TTL_HOURS
        )

        if success:
            if cached_tokens > 0:
                print(f"Сохранено в кэш (системный кэш: {cached_tokens} токенов)")
            else:
                print("Сохранено в локальный кэш")

    except Exception as e:
        print(f"Ошибка при сохранении в кэш: {e}")


async def cleanup_expired_cache():
    """Удаляет истекшие записи из кэша PostgreSQL."""
    try:
        pool = await get_pg_pool()
        await cleanup_expired_pg_cache(pool, "gemini")
    except Exception as e:
        print(f"Ошибка при очистке кэша: {e}")


async def get_cache_stats() -> dict:
    """Получает статистику кэша PostgreSQL."""
    try:
        pool = await get_pg_pool()
        stats = await get_pg_cache_stats(pool, "gemini")

        if 'gemini' in stats:
            gemini_stats = stats['gemini']
            # Получаем текущую модель для статистики
            current_model = get_current_model()
            return {
                "total_entries": gemini_stats['total_entries'],
                "active_entries": gemini_stats['active_entries'],
                "expired_entries": gemini_stats['expired_entries'],
                "model_stats": {current_model: gemini_stats['active_entries']}
            }

        return {
            "total_entries": 0,
            "active_entries": 0,
            "expired_entries": 0,
            "model_stats": {}
        }

    except Exception as e:
        print(f"Ошибка при получении статистики кэша: {e}")
        return {
            "total_entries": 0,
            "active_entries": 0,
            "expired_entries": 0,
            "model_stats": {}
        }


def calculate_cost(tokens_info: dict, model: str) -> dict:
    """Рассчитывает стоимость запроса."""
    if not tokens_info or model not in COST_PRICING:
        return {"total_cost": 0.0, "input_cost": 0.0, "output_cost": 0.0, "cached_cost": 0.0}

    pricing = COST_PRICING[model]

    input_tokens = tokens_info.get('input_tokens', 0)
    output_tokens = tokens_info.get('output_tokens', 0)
    cached_tokens = tokens_info.get('cached_tokens', 0)

    # Стоимость в USD за миллион токенов
    input_cost = (input_tokens / 1_000_000) * pricing['input']
    output_cost = (output_tokens / 1_000_000) * pricing['output']
    cached_cost = (cached_tokens / 1_000_000) * pricing['cached']

    total_cost = input_cost + output_cost + cached_cost

    return {
        "total_cost": round(total_cost, 6),
        "input_cost": round(input_cost, 6),
        "output_cost": round(output_cost, 6),
        "cached_cost": round(cached_cost, 6),
        "input_tokens": input_tokens,
        "output_tokens": output_tokens,
        "cached_tokens": cached_tokens
    }


def encode_pdf(pdf_path: str) -> Union[str, None]:
    """Синхронно кодирует файл в base64."""
    try:
        if not os.path.exists(pdf_path):
            return None
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except Exception as e:
        print(f"Ошибка при кодировании файла: {e}")
        return None


def get_pages_count(file_path: str) -> int:
    """Синхронно получает количество страниц в PDF."""
    if file_path.lower().endswith('.pdf'):
        try:
            doc = fitz.open(file_path)
            pages_count = len(doc)
            doc.close()
            return pages_count
        except Exception as e:
            print(f"Ошибка при чтении PDF: {e}")
            return 1
    return 1


def get_file_extension(file_path: str) -> Union[str, None]:
    if not os.path.exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None
    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_mime_type(file_path: str) -> str:
    """Синхронно определяет MIME-тип файла."""
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'bmp', 'tiff']:
        return f'image/{ext}'
    elif ext in ['jpg', 'jpeg']:
        return 'image/jpeg'
    return 'text/plain'


def smart_parse(obj: str) -> Any:
    """Пытается распарсить строку как JSON или Python литерал."""
    if isinstance(obj, (dict, list)):
        return obj
    try:
        return json.loads(obj)
    except json.JSONDecodeError:
        try:
            return ast.literal_eval(obj)
        except (ValueError, SyntaxError):
            return obj # Возвращаем как есть, если не удалось распарсить


def clear_text(json_string: str) -> Any:
    """Очищает ответ модели от markdown и парсит его."""
    if isinstance(json_string, str):
        try:
            text = json_string.strip()
            if text.startswith('```json'):
                text = text[7:]
            if text.endswith('```'):
                text = text[:-3]
            return smart_parse(text)
        except Exception as e:
            print(f"Ошибка при очистке текста: {e}")
            return json_string
    return json_string




def extract_entity_from_page_by_gemini(pdf_path: str, prompt: str = PROMPT_EXAMPLE_GEMINI) -> Union[str, None]:
    """Синхронно извлекает данные из файла с помощью Gemini."""
    try:
        if not os.path.exists(pdf_path):
            print(f"Файл не найден: {pdf_path}")
            return None

        # Читаем файл как байты (как в асинхронной версии)
        with open(pdf_path, "rb") as f:
            file_data = f.read()

        if not file_data:
            print(f"Не удалось прочитать файл: {pdf_path}")
            return None

        mime_type = get_mime_type(pdf_path)

        # Используем текущую активную модель
        model = get_current_model()
        print(f"Используем модель: {model} ({get_model_limits_info(model)})")

        client = genai.Client(
            api_key=api_key,
        )

        contents = types.Content(
            parts=[
                types.Part.from_text(text=prompt),
                types.Part.from_bytes(
                    mime_type=mime_type,
                    data=file_data,  # Передаем байты напрямую, без двойного кодирования
                ),
            ],
        )
        generate_content_config = types.GenerateContentConfig(
            temperature=0.1,
            thinking_config=types.ThinkingConfig(  # Добавляем thinking_config как в асинхронной версии
                thinking_budget=-1,
            ),
        )
        response = client.models.generate_content(
            model=model,
            contents=contents,
            config=generate_content_config,
        )

        return response.text

    except Exception as e:
        print(f"Ошибка в синхронной функции: {e}")
        return None


async def encode_file_async(file_path: str) -> Union[bytes, None]:
    """Асинхронно читает файл и возвращает его содержимое в виде байтов."""
    try:
        if not await aiofiles.os.path.exists(file_path):
            return None
        async with aiofiles.open(file_path, "rb") as f:
            return await f.read()
    except Exception as e:
        print(f"Ошибка при асинхронном чтении файла: {e}")
        return None


async def get_mime_type_async(file_path: str) -> str:
    """Асинхронно определяет MIME-тип файла."""
    try:
        if not await aiofiles.os.path.exists(file_path):
            return 'text/plain'
        _, ext = os.path.splitext(file_path)
        ext = ext.lower().lstrip('.')
        if ext == 'pdf':
            return 'application/pdf'
        elif ext in ['png', 'bmp', 'tiff']:
            return f'image/{ext}'
        elif ext in ['jpg', 'jpeg']:
            return 'image/jpeg'
        return 'text/plain'
    except Exception as e:
        print(f"Ошибка при определении MIME-типа: {e}")
        return 'text/plain'


async def extract_entity_from_file_async(file_path: str, prompt: str = PROMPT_EXAMPLE_GEMINI, max_retries: int = 3) -> Union[str, None]:
    """Асинхронно извлекает данные из файла с помощью Gemini API с механизмом повторных попыток."""
    try:
        # Асинхронное чтение файла
        file_data = await encode_file_async(file_path)
        if not file_data:
            print(f"Не удалось прочитать файл: {file_path}")
            return None

        # Определение MIME-типа
        mime_type = await get_mime_type_async(file_path)

        print(f"Выполняется асинхронный запрос к Gemini API для файла: {os.path.basename(file_path)}...")

        # Выполнение запроса с повторными попытками
        for attempt in range(max_retries):
            try:
                def _sync_request():
                    # Используем текущую активную модель (с автоматическим переключением)
                    current_model = get_current_model()
                    print(f"Используем модель: {current_model} ({get_model_limits_info(current_model)})")

                    # client = genai.Client(api_key=api_key)
                    client = genai.Client(api_key=api_key)
                    contents = types.Content(
                        parts=[
                            types.Part.from_text(text=prompt),
                            types.Part.from_bytes(
                                mime_type=mime_type,
                                data=file_data,
                            ),
                        ],
                    )
                    generate_content_config = types.GenerateContentConfig(
                        temperature=0.1,
                        thinking_config=types.ThinkingConfig(
                            thinking_budget=-1,
                        ),
                    )
                    response = client.models.generate_content(
                        model=current_model,
                        contents=contents,
                        config=generate_content_config,
                    )
                    return response.text

                response_text = await asyncio.get_event_loop().run_in_executor(None, _sync_request)
                return response_text

            except Exception as e:
                error_message = str(e).lower()

                # Проверяем HTTP статус коды (для httpx исключений)
                http_status = None
                try:
                    if hasattr(e, 'response'):
                        response_obj = getattr(e, 'response', None)
                        if response_obj and hasattr(response_obj, 'status_code'):
                            http_status = getattr(response_obj, 'status_code', None)
                except (AttributeError, TypeError):
                    pass

                if not http_status:
                    if "429" in error_message:
                        http_status = 429
                    elif "503" in error_message:
                        http_status = 503
                    elif "400" in error_message:
                        http_status = 400
                    elif "401" in error_message:
                        http_status = 401

                # Обработка по HTTP статус кодам
                if http_status == 429 or "rate limit" in error_message or "quota" in error_message:
                    if attempt < max_retries - 1:
                        # АВТОМАТИЧЕСКОЕ ПЕРЕКЛЮЧЕНИЕ НА РЕЗЕРВНУЮ МОДЕЛЬ
                        if switch_to_fallback_model():
                            print(f"Автоматическое переключение на резервную модель для повторной попытки")
                            continue  # Повторяем запрос с новой моделью

                        # Если уже на резервной модели - увеличиваем задержку
                        wait_time = (2 ** attempt) * 60
                        print(f"HTTP 429: Превышен лимит запросов (попытка {attempt + 1}/{max_retries}). Ждем {wait_time} сек...")
                        print(f"Уже используем резервную модель. Ждем восстановления лимитов...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print(f"HTTP 429: Лимит запросов превышен после {max_retries} попыток")
                        print(f"Решение: 1) Подождите 1 час, 2) Используйте другой API ключ, 3) Перейдите на платный тариф")
                        return None

                elif http_status == 503 or "unavailable" in error_message or "overloaded" in error_message:
                    if attempt < max_retries - 1:
                        wait_time = (2 ** attempt) + 1  # Экспоненциальная задержка: 1, 3, 5 секунд
                        print(f"HTTP 503: Сервис перегружен (попытка {attempt + 1}/{max_retries}). Ждем {wait_time} сек...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print(f"HTTP 503: Сервис остается перегруженным после {max_retries} попыток")
                        return None

                elif http_status == 400 or "invalid" in error_message or "bad request" in error_message:
                    print(f"HTTP 400: Ошибка в запросе: {e}")
                    return None

                elif http_status == 401 or "unauthorized" in error_message:
                    print(f"HTTP 401: Ошибка авторизации - проверьте API ключ")
                    return None

                elif http_status == 500 or "internal server error" in error_message:
                    if attempt < max_retries - 1:
                        wait_time = (2 ** attempt) * 2  # Задержка: 2, 4, 8 секунд
                        print(f"HTTP 500: Внутренняя ошибка сервера (попытка {attempt + 1}/{max_retries}). Ждем {wait_time} сек...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        print(f"HTTP 500: Сервер недоступен после {max_retries} попыток")
                        return None

                else:
                    # Для других ошибок не повторяем попытки
                    print(f"Неизвестная ошибка при обращении к Gemini API (HTTP {http_status}): {e}")
                    return None

        return None

    except Exception as e:
        print(f"Критическая ошибка при асинхронном обращении к Gemini API: {e}")
        return None


async def extract_entity_with_cache_async(file_path: str, prompt: str = PROMPT_EXAMPLE_GEMINI, use_cache: bool = True) -> Union[str, None]:
    """Асинхронно извлекает данные из файла с двойным кэшированием (локальный + системный)."""
    try:
        # Проверяем файл
        if not await aiofiles.os.path.exists(file_path):
            print(f"Файл не найден: {file_path}")
            return None

        # Получаем текущую модель для кэширования
        current_model = get_current_model()

        # Генерируем ключ кэша с учетом текущей модели
        cache_key = get_cache_key(file_path, prompt, current_model)

        # Проверяем локальный кэш
        if use_cache and not FORCE_CACHE_UPDATE:
            cached_result = await get_from_cache(cache_key)
            if cached_result:
                return cached_result

        print(f"Обрабатываем файл: {os.path.basename(file_path)}")

        # Читаем файл асинхронно
        file_data = await encode_file_async(file_path)
        if not file_data:
            print(f"Не удалось прочитать файл: {file_path}")
            return None

        # Определяем MIME-тип
        mime_type = await get_mime_type_async(file_path)

        # Выполняем запрос к Gemini API
        def _sync_request():
            # Используем текущую активную модель
            active_model = get_current_model()
            print(f"Используем модель: {active_model} ({get_model_limits_info(active_model)})")

            # client = genai.Client(api_key=api_key)
            client = google_genai.Client(api_key=api_key)
            contents = types.Content(
                role="user",
                parts=[
                    types.Part.from_text(text=prompt),
                    types.Part.from_bytes(
                        mime_type=mime_type,
                        data=file_data,
                    ),
                ],
            )
            generate_content_config = types.GenerateContentConfig(
                temperature=0.1,
                thinking_config=types.ThinkingConfig(
                    thinking_budget=-1,
                ),
            )
            response = client.models.generate_content(
                model=active_model,
                contents=contents,
                config=generate_content_config,
            )
            return response.text

        response_text = await asyncio.get_event_loop().run_in_executor(None, _sync_request)

        # Сохраняем в кэш с текущей моделью
        if use_cache and response_text:
            await save_to_cache(cache_key, response_text)

        return response_text

    except Exception as e:
        print(f"Ошибка при обработке файла с кэшированием: {e}")
        return None


async def process_batch_with_cache_async(file_paths: list[str], prompt: str = PROMPT_EXAMPLE_GEMINI,
                                        max_concurrent: int = 3, use_cache: bool = True) -> Dict[str, Union[str, None]]:
    """Асинхронно обрабатывает несколько файлов с кэшированием и контролем конкуренции."""
    print(f"Начинаем пакетную обработку {len(file_paths)} файлов (макс. {max_concurrent} одновременно)...")

    # Очищаем истекший кэш
    await cleanup_expired_cache()

    # Получаем статистику кэша
    cache_stats = await get_cache_stats()
    print(f"Статистика кэша: {cache_stats['active_entries']} активных записей")

    # Используем семафор для ограничения количества одновременных запросов
    semaphore = asyncio.Semaphore(max_concurrent)

    # Счетчики для статистики
    total_cost = 0.0
    cache_hits = 0
    api_calls = 0

    async def process_single_file(file_path: str) -> tuple[str, Union[str, None], dict]:
        nonlocal cache_hits, api_calls, total_cost

        async with semaphore:
            try:
                # Получаем текущую модель для кэширования
                current_model = get_current_model()

                # Проверяем кэш перед обработкой
                cache_key = get_cache_key(file_path, prompt, current_model)
                cached_result = await get_from_cache(cache_key) if use_cache else None

                if cached_result and not FORCE_CACHE_UPDATE:
                    cache_hits += 1
                    # Для кэшированных результатов стоимость минимальная
                    cost_info = {"total_cost": 0.001, "cached": True}
                    return file_path, cached_result, cost_info

                # Выполняем API запрос
                api_calls += 1
                result = await extract_entity_with_cache_async(file_path, prompt, use_cache)

                # Рассчитываем стоимость (примерная)
                cost_info = {
                    "total_cost": 0.01,  # Примерная стоимость
                    "cached": False,
                    # "input_tokens": 10000,  # Примерные значения
                    # "output_tokens": 500
                }
                total_cost += cost_info["total_cost"]

                return file_path, result, cost_info

            except Exception as e:
                print(f"Ошибка при обработке файла {os.path.basename(file_path)}: {e}")
                return file_path, None, {"total_cost": 0.0, "cached": False, "error": str(e)}

    # Создаем задачи для каждого файла
    tasks = [process_single_file(file_path) for file_path in file_paths]

    # Выполняем все задачи параллельно
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Формируем результат
    result_dict = {}
    successful_count = 0
    failed_count = 0
    total_files = len(file_paths)

    for i, result in enumerate(results):
        file_path = file_paths[i]

        if isinstance(result, Exception):
            print(f"Критическая ошибка при обработке файла {os.path.basename(file_path)}: {result}")
            result_dict[file_path] = None
            failed_count += 1
            continue

        # result должен быть кортежем (file_path, file_result, cost_info)
        if isinstance(result, tuple) and len(result) == 3:
            actual_file_path, file_result, cost_info = result
            result_dict[actual_file_path] = file_result

            if file_result is not None:
                successful_count += 1
            else:
                failed_count += 1
        else:
            print(f"Неожиданный формат результата для файла {os.path.basename(file_path)}")
            result_dict[file_path] = None
            failed_count += 1

    # Выводим итоговую статистику
    print(f"\nРЕЗУЛЬТАТЫ ОБРАБОТКИ:")
    print(f"   Успешно обработано: {successful_count}/{total_files}")
    print(f"   Ошибок: {failed_count}/{total_files}")
    print(f"   Попаданий в кэш: {cache_hits}/{total_files}")
    print(f"   API запросов: {api_calls}/{total_files}")
    print(f"   Общая стоимость: ${total_cost:.4f}")
    print(f"   Экономия: ${total_cost * (cache_hits / max(total_files, 1)):.4f} (от кэша)")

    return result_dict


async def process_multiple_files_async(file_paths: list[str], prompt: str = PROMPT_EXAMPLE_GEMINI, max_concurrent: int = 3) -> Dict[str, Union[str, None]]:
    """Асинхронно обрабатывает несколько файлов параллельно с контролем concorrenza."""
    print(f"Начинаем обработку {len(file_paths)} файлов (макс. {max_concurrent} одновременно)...")

    # Используем семафор для ограничения количества одновременных запросов
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_single_file(file_path: str) -> tuple[str, Union[str, None]]:
        async with semaphore:
            result = await extract_entity_from_file_async(file_path, prompt)
            return file_path, result

    # Создаем задачи для каждого файла
    tasks = [process_single_file(file_path) for file_path in file_paths]

    # Выполняем все задачи параллельно
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Формируем словарь с результатами
    result_dict = {}
    successful_count = 0
    failed_count = 0

    for i, result in enumerate(results):
        file_path = file_paths[i]

        if isinstance(result, Exception):
            print(f"Критическая ошибка при обработке файла {os.path.basename(file_path)}: {result}")
            result_dict[file_path] = None
            failed_count += 1
            continue

        # result должен быть кортежем (file_path, file_result)
        if isinstance(result, tuple) and len(result) == 2:
            actual_file_path, file_result = result
            result_dict[actual_file_path] = file_result

            if file_result is not None:
                successful_count += 1
            else:
                failed_count += 1
        else:
            print(f"Неожиданный формат результата для файла {os.path.basename(file_path)}")
            result_dict[file_path] = None
            failed_count += 1

    print(f"Обработка завершена: {successful_count} успешно, {failed_count} неудачно")
    return result_dict


async def main_async():
    """Асинхронная главная функция для демонстрации."""
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    # Тестовый файл
    test_file = r"C:\Rasim\Python\ScanDocument\ScanForTreaningNew.pdf"

    if not os.path.exists(test_file):
        print(f"Тестовый файл не найден: {test_file}")
        return

    print("=== АСИНХРОННАЯ ОБРАБОТКА ФАЙЛОВ ===\n")

    # --- Пример вызова СИНХРОННОЙ функции ---
    print("--- 1. Тестирование СИНХРОННОЙ функции ---")
    try:
        sync_result = await asyncio.get_event_loop().run_in_executor(None, extract_entity_from_page_by_gemini, test_file, PROMPT_OCR_CONTROL)
        print("\n[СИНХРОННЫЙ РЕЗУЛЬТАТ]:")
        print(sync_result)
    except Exception as e:
        print(f"\n[ОШИБКА В СИНХРОННОЙ ФУНКЦИИ]: {e}")
        print("Пропускаем синхронный тест и переходим к асинхронному...")
    print("-" * 40)

    # --- Пример вызова АСИНХРОННОЙ функции для одного файла ---
    print("\n--- 2. Тестирование АСИНХРОННОЙ функции (один файл) ---")
    async_result = await extract_entity_from_file_async(test_file, PROMPT_OCR_CONTROL)
    print("\n[АСИНХРОННЫЙ РЕЗУЛЬТАТ]:")
    print(async_result)
    print("-" * 40)

    # --- Пример пакетной обработки ---
    print("\n--- 3. Тестирование ПАКЕТНОЙ обработки ---")
    files_to_process = [test_file]  # Можно добавить больше файлов
    batch_results = await process_multiple_files_async(files_to_process, PROMPT_OCR_CONTROL)

    print("\n[РЕЗУЛЬТАТЫ ПАКЕТНОЙ ОБРАБОТКИ]:")
    for file_path, result in batch_results.items():
        print(f"\nФайл: {os.path.basename(file_path)}")
        print(f"Результат: {result[:200]}..." if result and len(result) > 200 else f"Результат: {result}")
    print("-" * 40)


if __name__ == "__main__":
    # Запускаем асинхронную главную функцию
    asyncio.run(main_async())
