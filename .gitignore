# Виртуальные окружения
.venv/
.venv_*/
venv/
ENV/
Gemini/user_data
.kreuzberg/
downloads/

# IDE и редакторы
.vscode/
.idea/
*.swp
*.swo
*.iml
__*.py

# Временные папки проекта
logs/
temp_image/
corrected_pages/
temp_processing/
cache_dir_gemini_ocr*/
temp_images_for_gemini_*/
tesseract_language/
pdf_extracted_text/
build/
dist/

# Кэш Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/

# Локальные файлы
*.log
*.pdf
*.env
.env
*.xml
~$*.*
*.txt
*.md
*.spec
*.xls*
*.json

# Временные файлы и результаты
analyzed_document_output.*
output.txt
pdf_data.txt

# Файлы и папки Gemini
.gemini_cache*
fitz-stubs/
token_count.txt
SavePDFPageAsImage.bat
