# To run this code you need to install the following dependencies:
# pip install google-genai
# pip install --upgrade google-generativeai

import base64
import os
from google import genai
from google.genai import types
from typing import Dict, Any, Union, Optional
import json
from prompt import GEMINI_AI_PROMPT, GEMINI_AI_THINKS
from dotenv import load_dotenv
load_dotenv()

def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    """Очищает и парсит JSON строку в словарь."""
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()

            # Удаляем обертки JSON
            for prefix in ['```json', '"']:
                if json_string.startswith(prefix):
                    json_string = json_string[len(prefix):]

            for suffix in ['```', '"']:
                if json_string.endswith(suffix):
                    json_string = json_string[:-len(suffix)]

            return json.loads(json_string)
        except json.JSONDecodeError as e:
            print(f"Ошибка парсинга JSON: {e}")
            return {}
    return json_string if isinstance(json_string, dict) else {}


def encode_pdf(pdf_path: str) -> Optional[str]:
    """Кодирует PDF в base64."""
    try:
        if not os.path.exists(pdf_path):
            print(f"Файл не найден: {pdf_path}")
            return None

        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except Exception as e:
        print(f"Ошибка кодирования PDF: {e}")
        return None


def generate(pdf_path: str) -> Optional[Dict[str, Any]]:
    """Генерирует анализ документа с помощью Gemini API."""

    pdf_decoded = encode_pdf(pdf_path)
    if pdf_decoded is None:
        return None

    client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))

    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text=f"file_name: {pdf_path}\n\n{GEMINI_AI_PROMPT}"),
            ],
        ),
        types.Content(
            role="model",
            parts=[types.Part.from_text(text=GEMINI_AI_THINKS)],
        ),
        types.Content(
            role="user",
            parts=[types.Part.from_text(text="INSERT_INPUT_HERE")],
        ),
    ]

    config = types.GenerateContentConfig(
        temperature=0.1,
        response_mime_type="application/json",
    )

    try:
        response_text = ""
        for chunk in client.models.generate_content_stream(
                model="gemini-2.5-pro-preview-05-06",
                contents=contents,
                config=config,
        ):
            response_text += chunk.text

        return clear_text(response_text)
    except Exception as e:
        print(f"Ошибка генерации контента: {e}")
        return None


def save_result_to_json(result: Dict[str, Any], output_path: str) -> None:
    """Сохраняет результат в JSON файл."""
    try:
        with open(output_path, "w", encoding="utf-8") as json_file:
            json.dump(result, json_file, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Ошибка сохранения JSON: {e}")


def create_filename_from_json(json_data: Dict[str, Any]) -> str:
    """Создает имя файла на основе данных JSON."""
    buyer_name = json_data.get("buyer_name", "unknown").replace(" ", "_").upper()
    buyer_code = json_data.get("buyer_code", "unknown")
    doc_type = json_data.get("doc_type", "unknown").replace(" ", "_").upper()
    doc_date = json_data.get("doc_date", "unknown")
    doc_number = json_data.get("doc_number", "unknown")

    page_numbers = json_data.get("page_numbers", [])
    if not isinstance(page_numbers, list):
        page_numbers = [page_numbers]

    pages_str = '_'.join(map(str, page_numbers))
    return f"{buyer_code}_{buyer_name}_{doc_type}_{doc_date}_{doc_number}_pages_{pages_str}.json"


if __name__ == "__main__":
    from datetime import datetime
    # Очистка экрана
    os.system("cls" if os.name == "nt" else "clear")

    pdf_path = r"d:\Scan\20250430_Merge 001-100.pdf"
    result = generate(pdf_path)

    if result:
        current_date_time = datetime.now().strftime("%Y%m%d %H%M%S")
        output_path = str(pdf_path).replace(".pdf", f" {current_date_time}.json")
        save_result_to_json(result, output_path)
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print("Ошибка обработки документа")