# -*- coding: utf-8 -*-
"""
Проверка содержимого созданных PDF файлов
"""

import fitz  # PyMuPDF
import os
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

BASE_OUTPUT_PATH = Path(os.getenv("BASE_OUTPUT_PATH", "output_folder"))

def check_pdf_files():
    """Проверяем созданные PDF файлы"""
    
    # Ищем ТТН 221
    ttn_path = BASE_OUTPUT_PATH / "32294905_ЕКСПАНСІЯ" / "ТТН" / "ТТН 221 04 02 2025.pdf"
    
    if ttn_path.exists():
        print(f"✅ Найден файл: {ttn_path}")
        
        try:
            doc = fitz.open(str(ttn_path))
            print(f"📄 Количество страниц в PDF: {len(doc)}")
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                print(f"  Страница {page_num + 1}:")
                print(f"    Размер: {page.rect.width} x {page.rect.height}")
                
                # Проверяем, есть ли текст
                text = page.get_text().strip()
                if text:
                    print(f"    Текст: Да ({len(text)} символов)")
                    # Показываем первые 100 символов
                    preview = text[:100].replace('\n', ' ')
                    print(f"    Превью: {preview}...")
                else:
                    print(f"    Текст: Нет (скан)")
                
                # Проверяем изображения
                images = page.get_images()
                print(f"    Изображения: {len(images)}")
            
            doc.close()
            
        except Exception as e:
            print(f"❌ Ошибка при чтении PDF: {e}")
    else:
        print(f"❌ Файл не найден: {ttn_path}")
        
        # Ищем все ТТН файлы
        ttn_dir = BASE_OUTPUT_PATH / "32294905_ЕКСПАНСІЯ" / "ТТН"
        if ttn_dir.exists():
            print(f"\n📁 Содержимое папки ТТН:")
            for file in ttn_dir.glob("*.pdf"):
                print(f"  - {file.name}")
                if "221" in file.name:
                    print(f"    🔍 Это может быть наш файл!")
        else:
            print(f"❌ Папка ТТН не найдена: {ttn_dir}")

def check_multiple_files():
    """Проверяем несколько файлов для сравнения"""
    
    files_to_check = [
        "ТТН 221 04 02 2025.pdf",  # Проблемный
        "ТТН 144 21 01 2025.pdf",  # Для сравнения
        "ТТН 106 14 01 2025.pdf",  # Для сравнения
    ]
    
    ttn_dir = BASE_OUTPUT_PATH / "32294905_ЕКСПАНСІЯ" / "ТТН"
    
    for filename in files_to_check:
        filepath = ttn_dir / filename
        print(f"\n=== {filename} ===")
        
        if filepath.exists():
            try:
                doc = fitz.open(str(filepath))
                print(f"📄 Страниц: {len(doc)}")
                doc.close()
            except Exception as e:
                print(f"❌ Ошибка: {e}")
        else:
            print(f"❌ Файл не найден")

if __name__ == "__main__":
    print("=== Проверка PDF файлов ===")
    check_pdf_files()
    
    print("\n=== Проверка нескольких файлов ===")
    check_multiple_files()
