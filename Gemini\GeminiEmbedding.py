# Gemini Embedding — универсальный, мощный инструмент преобразования смысла текста в числовые векторы.
# Лидер по качеству среди современных моделей.

import os
from google import genai
from dotenv import load_dotenv
load_dotenv()


api_key = os.environ.get("GEMINI_API_KEY_PRESTIGE")
client = genai.Client(api_key=api_key)

result = client.models.embed_content(
    model="gemini-embedding-001",  # MODEL_ID = "gemini-embedding-001" # @param ["gemini-embedding-001", "text-embedding-004"] {"allow-input":true, isTemplate: true}
    contents="В чём смысл жизни?"
)
print(result.embeddings)  # массив float чисел (~3072 элементов)


result = client.models.embed_content(
    model="gemini-embedding-001",
    contents="Hello World!",
    config={"output_dimensionality": 10}
)
print(result.embeddings)  # 10‑мерный вектор :contentReference[oaicite:11]{index=11}