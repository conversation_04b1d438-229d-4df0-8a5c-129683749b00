# To run this code you need to install the following dependencies:
# pip install google-genai
# pip install --upgrade google-generativeai
# программа создана с помощью https://aistudio.google.com/prompts/1VvAiD-oYWC3g94h0wTOUMvzU7BpKrgn_

import base64
import os
from google import genai
from google.genai import types
from typing import Dict, Any, Union
import ast
import json
from dotenv import load_dotenv

from prompt import PROMPT_EXAMPLE_GEMINI

# Загружаем переменные окружения из .env файла
load_dotenv()


def clear_text(obj):
    if isinstance(obj, (dict, list)):
        return obj  # Уже готовый объект — возвращаем как есть
    elif isinstance(obj, str):
        try:
            return json.loads(obj)
        except json.JSONDecodeError:
            try:
                return ast.literal_eval(obj)
            except (ValueError, SyntaxError):
                raise ValueError("Строка не является валидным JSON или Python-литералом")
    else:
        raise TypeError(f"Неподдерживаемый тип: {type(obj)}")


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def generate(pdf_path: str):
    """Generate a response from the model."""
    
    pdf_decoded = encode_pdf(pdf_path)
    if pdf_decoded is None:
        return None
    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )
    file_name = os.path.basename(pdf_path)
    model = "gemini-2.5-flash-preview-05-20"  # "gemini-2.5-pro-preview-05-06"
    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type="application/pdf",
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text= "file_name: " + file_name
                                           + f"\n\n{PROMPT_EXAMPLE_GEMINI}"
                                     + "\n В одном документе не может быть две первые или последние страницы. "
                                       "Поэтому НЕ МОЖЕТ БЫТЬ несколько page_type=1 или page_type=3. У тебя как? Перепроверил?"
                                     ),
            ],
        )]
    generate_content_config = types.GenerateContentConfig(
        thinking_config=types.ThinkingConfig(
            thinking_budget=0,
        ),
        response_mime_type="application/json",
        response_schema=genai.types.Schema(
            type=genai.types.Type.OBJECT,
            required=['doc'],
            properties={
                "doc": genai.types.Schema(
                    type=genai.types.Type.ARRAY,
                    items=genai.types.Schema(
                        type=genai.types.Type.OBJECT,
                        required=["doc_type", "doc_date", "doc_number", "buyer_name", "buyer_code", "invoices_numbers",
                                  "amount_with_vat", "page_number", "page_type"],
                        properties={
                            "doc_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["ТТН", "ВН", "АКТ", "ПН", "ДОВ", "ПП", "ПРОЧИЙ"],
                                description="""
                                    "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА"-ТТН,
                                    "ВИДАТКОВА НАКЛАДНА"-ВН,
                                    "АКТ"-АКТ,
                                    "ПРИБУТКОВА НАКЛАДНА"-ПН,
                                    "ДОВЕРЕННОСТЬ"-ДОВ,
                                    "ПОВЕРНЕННАЯ ПОСТАВЩИКУ"-ПП,
                                    "ПРОЧИЙ"-ПРОЧИЙ
                                    """,
                            ),
                            "doc_date": genai.types.Schema(  # type: ignore
                                type=genai.types.Type.STRING,
                                description="Дата в формате dd.mm.yyyy or None",
                            ),
                            "doc_number": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                description="только число"
                            ),
                            "buyer_name": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                description="Коротко. Без кавычек и юридического статуса. Например: АШАН/МЕТРО"
                            ),
                            "buyer_code": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                description="только число 8 или 10 цифр"
                            ),
                            "invoices_numbers": genai.types.Schema(
                                type=genai.types.Type.ARRAY,
                                items=genai.types.Schema(
                                    type=genai.types.Type.INTEGER,
                                ),
                                description="Заполнить только для ТТН: из 'Супровідні документи на вантаж' или из колонки 'Документи з вантажем'. "
                                            "Только уникальные значения. По возрастанию."
                            ),
                            "amount_with_vat": genai.types.Schema(
                                type=genai.types.Type.NUMBER,
                                description="""У ТТН - Извлеки из "Усього відпущено на загальну суму" или из колонки "Загальна сума з ПДВ".
                                            Для всех документов бери только сумму написанную прописью. Переведи в число. 
                                            Если не смог извлечь, тогда бери "Усього з ПДВ"
                                            ** "У т.ч. ПДВ" - ИГНОРИРУЙ.
                                            ** СУММУ ИЗВЛЕКАЙ СТРОГО ИЗ ДАННОЙ СТРАНИЦЫ!!! **
                                            ** СУММЫ ИЗ ДРУГИХ СТРАНИЦ ПЕРЕНОСИТЬ ЗАПРЕЩЕНО!!! **
                                            Если нет суммы - ставь 0.
                                            """
                            ),
                            "page_number": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                description="Номер, что тебе дал",
                            ),
                            "page_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["1", "2", "3"],
                                description="""Страница в документе. НЕ ФАЙЛЕ!!!
                                    1-лицевая сторона документа,  // Уникальный. Две первые страницы не может быть
                                    2-средняя страница в документе, // Может дублироваться
                                    3-последняя страница документа. // Уникальный. Две последние страницы не может быть
                                """
                            ),
                            "comment": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                description="Почему сделал page_type именно такой",
                            ),
                        },
                    ),
                ),
                "total_pages_count": genai.types.Schema(
                    type=genai.types.Type.INTEGER,
                    description="Общее количество страниц в документе",
                ),
            },
        ),
    )
    response = client.models.generate_content(model=model, contents=contents, config=generate_content_config)
    cleared_response = clear_text(response.text)
    return cleared_response


def save_result_to_json(result:str, output_path: str) -> None:
    """Сохраняет результат анализа в JSON файл.

    Args:
        result: Словарь с результатами анализа
        output_path: Путь к файлу для сохранения
    """
    with open(output_path, "w", encoding="utf-8") as json_file:
        json.dump(result, json_file, ensure_ascii=False, indent=2)


def create_filename_from_json(json_data: Dict[str, Any]) -> str:
    buyer_name = json_data.get("buyer_name", "unknown").replace(" ","_").upper()
    buyer_code = json_data.get("buyer_code", "unknown")
    doc_type = json_data.get("doc_type", "unknown").replace(" ", "_").upper()
    doc_date = json_data.get("doc_date", "unknown")
    doc_number = json_data.get("doc_number", "unknown")
    page_numbers = json_data.get("page_numbers", [])
    if not isinstance(page_numbers, list):
        page_numbers = [page_numbers]  # Ensure page_numbers is a list
    folder_name = os.path.join(f"{buyer_code} {buyer_name}",doc_type)
    file_name = f"{buyer_code}_{buyer_name}_{doc_type}_{doc_date}_{doc_number}_pages_{'_'.join(map(str, page_numbers))}.json"
    return file_name


if __name__ == "__main__":
    from datetime import datetime
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")
        
    pdf_path = r"c:\Scan\All\2025-05-31_1_compressed.pdf "
    result = generate(pdf_path)
    current_date_time = datetime.now().strftime("%Y%m%d %H%M%S")
    base_name = str(pdf_path).replace(".pdf", f" {current_date_time}.json")
    save_result_to_json(result,  base_name)
    print(result)
