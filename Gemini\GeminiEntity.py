
# To run this code you need to install the following dependencies:
# pip install google-genai

import base64
import os
from os.path import exists

import fitz
from google import genai
from google.genai import types
from dotenv import load_dotenv

from prompt import PROMPT_EXAMPLE_GEMINI

load_dotenv()


def encode_pdf(pdf_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(pdf_path):
        return None
      with open(pdf_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def get_pages_count(file_path):
    if get_file_extension(file_path) in ['.pdf']:
        # количество страниц в pdf
        doc = fitz.open(file_path)
        pages_count = len(doc)
        doc.close()
        return pages_count
    else:
        return 1


def get_file_extension(file_path: str) -> str:
    if not exists(file_path):
        print(f"Файл не найден: {file_path}")
        return None

    _, ext = os.path.splitext(file_path)
    return ext.lower().lstrip('.')


def get_mime_type(file_path: str) -> str:
    ext = get_file_extension(file_path)
    if ext == 'pdf':
        return 'application/pdf'
    elif ext in ['png', 'bmp', 'tiff']:
        return f'image/{ext}'
    elif ext in ['jpg', 'jpeg']:
        return f'image/jpeg'

    return 'text/plain'


def extract_entity_by_gemini(pdf_path: str):
    pdf_decoded = encode_pdf(pdf_path)
    pages_count = get_pages_count(pdf_path)
    filename_with_extension = os.path.basename(pdf_path).strip()
    mime_type = get_mime_type(pdf_path)

    client = genai.Client(
        api_key=os.environ.get("GEMINI_API_KEY"),
    )

    model = "gemini-2.5-flash-preview-05-20"
    local_prompt = f"""
                Просканируй весь документ и извлеки данные по каждой из {pages_count} страниц.
                
                Если страница пустая — верни только номер страницы и её тип (1, 2 или 3).
                Дата может быть указана словами. Преобразуй её в формат dd.mm.yyyy.
                Если дата отсутствует или не может быть точно определена — верни null.
                
                Количество объектов в массиве должно точно соответствовать количеству страниц.
                """

    contents = [
        types.Content(
            role="user",
            parts=[
                types.Part.from_bytes(
                    mime_type=mime_type,
                    data=base64.b64decode(pdf_decoded),
                ),
                types.Part.from_text(text=local_prompt)
                    ],
                )
            ]
    generate_content_config = types.GenerateContentConfig(
        thinking_config=types.ThinkingConfig(thinking_budget=0),
        response_mime_type="application/json",
        response_schema=genai.types.Schema(
            type=genai.types.Type.OBJECT,
            required=[filename_with_extension],
            properties={
                filename_with_extension: genai.types.Schema(
                    type=genai.types.Type.ARRAY,
                    min_items=pages_count,
                    max_items=pages_count,
                    items=genai.types.Schema(
                        type=genai.types.Type.OBJECT,
                        required=[
                            "doc_type", "doc_date", "doc_number", "buyer_name",
                            "buyer_code", "invoices_numbers", "amount_with_vat",
                            "page_number", "page_type"
                        ],
                        properties={
                            "doc_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["ТТН", "ВН", "АКТ", "ПН", "ДОВ", "ПП", "ПРОЧИЙ"],
                                description="""
                                    "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА"-ТТН,
                                    "ВИДАТКОВА НАКЛАДНА"-ВН,
                                    "АКТ"-АКТ,
                                    "ПРИБУТКОВА НАКЛАДНА"-ПН,
                                    "ДОВЕРЕННОСТЬ"-ДОВ,
                                    "ПОВЕРНЕННАЯ ПОСТАВЩИКУ"-ПП,
                                    "ПРОЧИЙ"-ПРОЧИЙ
                                    """
                            ),
                            "doc_date": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                description="Дата документа в формате dd.mm.yyyy. Если указана словами — преобразуй. Если определить невозможно — верни null."
                            ),
                            "doc_number": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                description="Номер документа. Только числовая часть"
                            ),
                            "buyer_name": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                description="Краткое имя покупателя. Без юр. форм и кавычек"
                            ),
                            "buyer_code": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                description="Код покупателя (8 или 10 цифр)"
                            ),
                            "invoices_numbers": genai.types.Schema(
                                type=genai.types.Type.ARRAY,
                                items=genai.types.Schema(type=genai.types.Type.INTEGER),
                                description = "Заполнить только для ТТН: из 'Супровідні документи на вантаж' или из колонки 'Документи з вантажем'. Только уникальные значения. По возрастанию"
                            ),
                            "amount_with_vat": genai.types.Schema(
                                type=genai.types.Type.NUMBER,
                                description="""У ТТН - Извлеки из "Усього відпущено на загальну суму" или из колонки "Загальна сума з ПДВ".
                                                Для всех документов бери только сумму написанную прописью. Переведи в число. 
                                                Если не смог извлечь, тогда бери "Усього з ПДВ"
                                                ** "У т.ч. ПДВ" - ИГНОРИРУЙ.
                                                ** СУММУ ИЗВЛЕКАЙ СТРОГО ИЗ ДАННОЙ СТРАНИЦЫ!!! **
                                                ** СУММЫ ИЗ ДРУГИХ СТРАНИЦ ПЕРЕНОСИТЬ ЗАПРЕЩЕНО!!! **
                                                Если нет суммы - ставь 0.
                                                """
                            ),
                            "page_number": genai.types.Schema(
                                type=genai.types.Type.INTEGER,
                                maximum=pages_count,
                                description="Номер страницы в файле (не документа)"
                            ),
                            "page_type": genai.types.Schema(
                                type=genai.types.Type.STRING,
                                enum=["1", "2", "3"],
                                description="1 — лицевая, 2 — внутренняя, 3 — последняя"
                            ),
                        }
                    )
                ),
            }
        )
    )

    print(f"Количество токенов входящих: {client.models.count_tokens(model=model, contents=contents)}")
    response = client.models.generate_content(model=model, contents=contents, config=generate_content_config)
    print(f'prompt_token_count: {response.usage_metadata.prompt_token_count}')
    print(f'candidates_token_count: {response.usage_metadata.candidates_token_count}')
    print(f"total_token_count: {response.usage_metadata.total_token_count}")

    return response.text


if __name__ == "__main__":
    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    pdf_path = r"C:\Rasim\Python\ScanDocument\temp_image\202410 Merge_page_1.png"
    result = extract_entity_by_gemini(pdf_path)
    print(result)