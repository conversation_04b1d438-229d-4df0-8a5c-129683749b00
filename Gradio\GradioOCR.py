# pip install gradio_client
# https://huggingface.co/spaces/Souvik3333/Nanonets-ocr-s
import base64
import os

from gradio_client import Client, handle_file

def encode_pdf(file_path: str):
    """Encode the pdf to base64."""
    try:
      if not os.path.exists(file_path):
        return None
      with open(file_path, "rb") as pdf_file:
          return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {file_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


image_path = r"/temp_image/2025-04-30_172222_page_36.png"
encode_data = encode_pdf(image_path)
client = Client("Souvik3333/Nanonets-ocr-s")
result = client.predict(
    image=handle_file(encode_data),
    max_tokens=4096,
    with_img_desc=False,
    api_name="/process_document"
)
print(result)