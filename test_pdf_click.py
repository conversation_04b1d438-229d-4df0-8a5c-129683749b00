# -*- coding: utf-8 -*-
"""
Тестовый скрипт для проверки поиска и клика по PDF элементам
"""

import os
import time
from dotenv import load_dotenv

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# Загружаем переменные окружения
load_dotenv()

LOGIN_EMAIL = os.getenv("VCHASNO_LOGIN_EMAIL")
LOGIN_PASSWORD = os.getenv("VCHASNO_LOGIN_PASSWORD")

def configure_driver():
    """Настраивает и возвращает экземпляр Chrome WebDriver."""
    print("Конфигурируем браузер...")
    
    chrome_options = Options()
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.maximize_window()
    return driver

def test_pdf_search():
    """Тестирует поиск PDF элементов на странице документа"""
    driver = configure_driver()
    wait = WebDriverWait(driver, 20)
    
    try:
        print("=== ТЕСТ ПОИСКА PDF ЭЛЕМЕНТОВ ===")
        
        # Авторизация (упрощенная)
        print("1. Авторизация...")
        driver.get("https://edo.vchasno.ua/auth/start")
        
        # Email
        email_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login input")))
        email_input.clear()
        email_input.send_keys(LOGIN_EMAIL)
        
        continue_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login > div > div > button")))
        continue_button.click()
        
        # Password
        password_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#password")))
        password_input.clear()
        password_input.send_keys(LOGIN_PASSWORD)
        
        submit_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']")))
        submit_button.click()
        
        wait.until(EC.url_contains("/app/"))
        print("✓ Авторизация успешна")
        
        # Переходим к тестовому документу
        test_url = "https://edo.vchasno.ua/app/documents/768fb833-aac1-4889-8365-940e87ab2ad6"
        print(f"2. Переходим к документу: {test_url}")
        driver.get(test_url)
        
        # Ждем загрузки страницы
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.document__content__bs1ZT")))
        time.sleep(3)
        print("✓ Страница документа загружена")
        
        # Ищем кнопку меню
        print("3. Ищем кнопку меню скачивания...")
        download_menu_selectors = [
            "div.documentHeader__cellTools__WCXBK > div > div:nth-child(4)",
            "div.documentHeader__cellTools__WCXBK div:nth-child(4)",
            "div.documentHeader__cellTools__WCXBK button",
            "[class*='download']",
            "[class*='menu']"
        ]
        
        download_menu = None
        for selector in download_menu_selectors:
            try:
                download_menu = driver.find_element(By.CSS_SELECTOR, selector)
                if download_menu:
                    print(f"✓ Кнопка меню найдена по селектору: {selector}")
                    break
            except:
                continue
        
        if not download_menu:
            print("✗ Кнопка меню не найдена")
            return
        
        # Кликаем на кнопку меню
        print("4. Кликаем на кнопку меню...")
        try:
            download_menu.click()
            print("✓ Кликнули обычным кликом")
        except:
            try:
                driver.execute_script("arguments[0].click();", download_menu)
                print("✓ Кликнули через JavaScript")
            except Exception as e:
                print(f"✗ Не удалось кликнуть: {e}")
                return
        
        time.sleep(2)
        
        # Ищем PDF элементы
        print("5. Ищем элементы с PDF...")
        menu_items = driver.find_elements(By.XPATH, "//li | //span | //div | //a | //button")
        print(f"Найдено {len(menu_items)} элементов для проверки")
        
        pdf_found = False
        for i, item in enumerate(menu_items):
            try:
                text = item.text.strip()
                if text:
                    contains_pdf = 'pdf' in text.lower()
                    print(f"Элемент {i+1}: '{text}' - {'СОДЕРЖИТ PDF' if contains_pdf else 'не содержит PDF'}")
                    
                    if contains_pdf and not pdf_found:
                        print(f">>> НАЙДЕН PDF ЭЛЕМЕНТ: '{text}' - пробуем кликнуть...")
                        try:
                            item.click()
                            print("✓ Успешно кликнули!")
                            pdf_found = True
                            break
                        except:
                            try:
                                driver.execute_script("arguments[0].click();", item)
                                print("✓ Успешно кликнули через JavaScript!")
                                pdf_found = True
                                break
                            except:
                                print("✗ Не удалось кликнуть на этот элемент")
            except:
                continue
        
        if pdf_found:
            print("✓ PDF элемент найден и кликнут!")
        else:
            print("✗ PDF элемент не найден или не удалось кликнуть")
        
        # Ждем немного для наблюдения
        print("6. Ждем 10 секунд для наблюдения...")
        time.sleep(10)
        
    except Exception as e:
        print(f"✗ ОШИБКА: {e}")
    
    finally:
        input("Нажмите Enter для закрытия браузера...")
        driver.quit()

if __name__ == "__main__":
    test_pdf_search()
