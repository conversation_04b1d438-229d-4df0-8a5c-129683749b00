import os
import google.generativeai as genai
from dotenv import load_dotenv

# Загрузка переменных окружения
load_dotenv()

# Конфигурация API
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
MODEL = os.getenv("GEMINI_MODEL_2F")
# Создаем модель
model = genai.GenerativeModel(MODEL)


def chat_with_gemini():
    print("Gemini AI Assistant (для выхода введите 'quit')")
    print("=" * 50)

    # Настройки чата
    generation_config = {
        "temperature": 0.7,
        "top_p": 1,
        "top_k": 1,
        "max_output_tokens": 2048,
    }

    # Инициализация чата
    chat = model.start_chat(history=[])

    while True:
        user_input = input("Вы: ")
        if user_input.lower() == 'quit':
            break

        try:
            # Отправка сообщения
            response = chat.send_message(user_input, stream=True)

            # Потоковый вывод ответа
            print("Gemini: ", end="")
            for chunk in response:
                print(chunk.text, end="")
            print("\n")

        except Exception as e:
            print(f"Ошибка: {str(e)}")


if __name__ == "__main__":
    chat_with_gemini()