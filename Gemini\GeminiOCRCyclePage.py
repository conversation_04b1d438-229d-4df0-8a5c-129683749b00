import fitz
import os
import asyncio
from typing import List, Optional
from GeminiOCRAll import extract_text_from_pdf_with_gemini
import time
from DataBase import create_pool, create_tables_base, insert_document_base
from pathlib import Path
import logging
from prompt import PROMPT_EXAMPLE, PROMPT_OCR

from process_documents import add_to_db_sync


# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Настройки ограничения скорости
PAGES_PER_MINUTE = 1  # Максимальное количество страниц в минуту
GROUP_SIZE = PAGES_PER_MINUTE  # Размер группы страниц для обработки
MAX_RETRIES = 3  # Максимальное количество повторных попыток
RETRY_DELAY = 5  # Базовая задержка между попытками в секундах


async def call_gemini_with_retry(image_path: str, retries: int = MAX_RETRIES) -> Optional[str]:
    """
    Вызывает Gemini OCR с повторными попытками при возникновении ошибок 503.
    
    Использует экспоненциальную задержку между попытками.
    """
    attempt = 0
    base_delay = RETRY_DELAY
    prompt = PROMPT_OCR + '\n\n' + PROMPT_EXAMPLE
    while attempt < retries:
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None,
                extract_text_from_pdf_with_gemini,
                image_path, 
                prompt
            )
        except Exception as e:
            if "503" in str(e) or "quota" in str(e):
                attempt += 1
                delay = base_delay * (2 ** attempt)
                logger.warning(f"Ошибка 503 (попытка {attempt}/{retries}). Повтор через {delay} сек.")
                await asyncio.sleep(delay)
            else:
                logger.error(f"Критическая ошибка при обработке {image_path}: {e}")
                return None
    logger.error(f"Не удалось обработать {image_path} после {retries} попыток")
    return None


async def safe_file_cleanup(file_path: Path, max_attempts: int = 5) -> bool:
    """Безопасное удаление файла с множественными попытками."""
    for attempt in range(max_attempts):
        try:
            if file_path.exists():
                file_path.unlink()
                return True
            return True  # Файл уже не существует
        except PermissionError:
            if attempt < max_attempts - 1:
                await asyncio.sleep(0.5 * (attempt + 1))
            else:
                logger.error(f"Не удалось удалить файл {file_path} после {max_attempts} попыток")
                return False
        except Exception as e:
            logger.error(f"Неожиданная ошибка при удалении {file_path}: {e}")
            return False
    return False


async def process_image(image_data: dict, output_folder: str, pool, pdf_path: str) -> None:
    """Обрабатывает отдельное изображение без зависимости от объекта Document."""
    page_num = image_data['page_num']
    img_index = image_data['img_index']
    image_bytes = image_data['image_bytes']
    image_ext = image_data['image_ext']
    
    # Создаем временный путь к файлу
    image_path = os.path.join(output_folder, f"image_page_{page_num + 1}_idx_{img_index}.{image_ext}")
    temp_file = Path(image_path)
    
    try:
        # Асинхронная запись изображения
        await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: temp_file.write_bytes(image_bytes)
        )
        
        if not temp_file.exists():
            logger.error(f"Файл не создан: {image_path}")
            return

        # Вызов Gemini с повторными попытками
        result = await call_gemini_with_retry(str(temp_file))
        
        if result:
            # Сохраняем в базу данных
            data = [(os.path.abspath(pdf_path), page_num + 1, result)]
            # await insert_document_base(pool, data)
            
            # добавляем в другую таблицу
            data = {"doc": data}
            add_to_db_sync(data)
    
    except Exception as e:
        logger.error(f"Ошибка при обработке изображения: {e}")
    
    finally:
        # Гарантированное удаление временного файла
        await safe_file_cleanup(temp_file)


async def extract_images_data(pdf_path: str) -> tuple:
    """Извлекает данные изображений из PDF и возвращает их вместе с информацией о пустых страницах."""
    def _extract_sync():
        images_data = []
        empty_pages = []
        
        with fitz.open(pdf_path) as doc:
            total_pages = len(doc)
            
            for page_num in range(total_pages):
                page = doc.load_page(page_num)
                image_list = page.get_images(full=True)

                if image_list:
                    for img_index, img in enumerate(image_list):
                        xref = img[0]
                        base_image = doc.extract_image(xref)
                        
                        images_data.append({
                            'page_num': page_num,
                            'img_index': img_index,
                            'image_bytes': base_image["image"],
                            'image_ext': base_image["ext"]
                        })
                else:
                    empty_pages.append(page_num)
        
        return images_data, empty_pages, total_pages
    
    # Выполняем синхронную операцию в отдельном потоке
    return await asyncio.get_event_loop().run_in_executor(None, _extract_sync)


async def verify_pages_in_db(pool, pdf_path: str, total_pages: int) -> None:
    """Проверяет соответствие количества страниц в PDF и записей в базе данных."""
    try:
        async with pool.acquire() as conn:
            count = await conn.fetchval(
                "SELECT COUNT(*) FROM t_scan_documents_raw WHERE full_path = $1",
                os.path.abspath(pdf_path)
            )
            
            if count != total_pages:
                logger.warning(f"Несоответствие записей: БД={count}, PDF={total_pages}")
                pages_in_db = await conn.fetch(
                    "SELECT page_number FROM t_scan_documents_raw WHERE full_path = $1",
                    os.path.abspath(pdf_path)
                )
                db_pages = {row['page_number'] for row in pages_in_db}
                missing_pages = [i+1 for i in range(total_pages) if i+1 not in db_pages]
                if missing_pages:
                    logger.error(f"Отсутствуют страницы: {missing_pages}")
                    raise ValueError(f"Отсутствуют страницы: {missing_pages}")
            else:
                logger.info(f"✓ Проверка завершена: {count}/{total_pages} страниц")
    except Exception as e:
        logger.error(f"Ошибка при проверке БД: {e}")
        raise


async def extract_images_from_pdf(pdf_path: str, output_folder: str, pool) -> None:
    """Извлекает изображения из PDF с обработкой ошибок блокировки файлов."""
    os.makedirs(output_folder, exist_ok=True)
    start_time = time.time()
    
    try:
        # Извлекаем все данные изображений сразу и закрываем PDF
        images_data, empty_pages, total_pages = await extract_images_data(pdf_path)
        
        # Обрабатываем пустые страницы
        if empty_pages:
            data = [(os.path.abspath(pdf_path), p + 1, "EMPTY_PAGE") for p in empty_pages]
            await insert_document_base(pool, data)
            logger.info(f"Добавлено {len(empty_pages)} пустых страниц")

        # Обрабатываем изображения группами
        for i in range(0, len(images_data), GROUP_SIZE):
            group = images_data[i:i + GROUP_SIZE]
            group_start_time = time.time()
            
            logger.info(f"Обработка группы {i//GROUP_SIZE+1} [{len(group)} изображений]")
            
            # Создаем задачи для группы
            group_tasks = [process_image(img_data, output_folder, pool, pdf_path) for img_data in group]
            await asyncio.gather(*group_tasks)
            
            group_time = time.time() - group_start_time
            if i + GROUP_SIZE < len(images_data) and group_time < 60:
                wait_time = 60 - group_time
                logger.info(f"Ожидание {wait_time:.1f} сек...")
                await asyncio.sleep(wait_time)

        logger.info(f"Обработка завершена за {(time.time()-start_time)/60:.1f} мин")
        await verify_pages_in_db(pool, pdf_path, total_pages)
            
    except Exception as e:
        logger.error(f"Критическая ошибка при обработке PDF: {e}")
        raise


async def process_directory(directory_path: str, output_folder: str, pool) -> None:
    """Обрабатывает все PDF в директории с улучшенным логированием."""
    try:
        pdf_files = [
            os.path.join(root, file)
            for root, _, files in os.walk(directory_path)
            for file in files if file.lower().endswith('.pdf')
        ]

        if not pdf_files:
            logger.warning(f"PDF не найдены в {directory_path}")
            return

        logger.info(f"Найдено {len(pdf_files)} PDF-файлов")
        
        for i, pdf_path in enumerate(pdf_files, 1):
            logger.info(f"Обработка ({i}/{len(pdf_files)}): {os.path.basename(pdf_path)}")
            try:
                await extract_images_from_pdf(pdf_path, output_folder, pool)
            except Exception as e:
                logger.error(f"Ошибка обработки файла: {e}")
                continue

        logger.info(f"Обработка завершена. Успешно: {len(pdf_files)} файлов")

    except Exception as e:
        logger.error(f"Фатальная ошибка обработки директории: {e}")
        raise


async def main() -> None:
    pdf_directory = r"c:\Scan\All\ForParse"
    output_folder = "temp_image"
    
    pool = await create_pool()
    if not pool:
        logger.error("Не удалось создать пул подключений к БД")
        return

    try:
        await create_tables_base(pool)
        await process_directory(pdf_directory, output_folder, pool)
    finally:
        await pool.close()


if __name__ == "__main__":
    asyncio.run(main())