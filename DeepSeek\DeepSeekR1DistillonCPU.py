from transformers import AutoTokenizer, AutoModelForCausalLM

model_id = "deepseek-ai/DeepSeek-R1-Distill"

# Загружаем токенизатор и модель
tokenizer = AutoTokenizer.from_pretrained(model_id)
model = AutoModelForCausalLM.from_pretrained(model_id, device_map="cpu")

# Пример генерации
input_text = "2*2?"
inputs = tokenizer(input_text, return_tensors="pt")

# Генерация без градиентов (быстрее и безопаснее)
with torch.no_grad():
    output = model.generate(**inputs, max_new_tokens=100)

print(tokenizer.decode(output[0], skip_special_tokens=True))
