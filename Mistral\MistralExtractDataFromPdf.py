# Import required libraries
import base64
from pathlib import Path
from mistralai import DocumentURLChunk, ImageURLChunk, TextChunk
import json
import os
from mistralai import <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
load_dotenv()


def encode_pdf(pdf_path):
    """Encode the pdf to base64."""
    try:
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode("utf-8")
    except FileNotFoundError:
        print(f"Error: The file {pdf_path} was not found.")
        return None
    except Exception as e:  # Added general exception handling
        print(f"Error: {e}")
        return None


def main_mistral(pdf_file):

    api_key = os.environ["MISTRAL_API_KEY"]
    client = Mistral(api_key=api_key)
    pdf_file = Path(pdf_file)
    # Verify PDF file exists
    # assert pdf_file.is_file()

    # Upload PDF file to Mistral's OCR service
    uploaded_file = client.files.upload(
        file={"file_name": pdf_file.stem, "content": pdf_file.read_bytes(),},
        purpose="ocr",
    )

    # Get URL for the uploaded file
    signed_url = client.files.get_signed_url(file_id=uploaded_file.id)

    # Process PDF with OCR, including embedded images
    pdf_response = client.ocr.process(
        document=DocumentURLChunk(document_url=signed_url.url),
        model="mistral-ocr-latest",
        include_image_base64=False,
    )

    # Convert response to JSON format
    response_dict = json.loads(pdf_response.model_dump_json())

    return json.dumps(response_dict, indent=4, ensure_ascii=False)


if __name__ == "__main__":
    pdf_path = r"c:\Scan\All\TestVN2str.pdf"
    result = main_mistral(pdf_path)
    print(result)
