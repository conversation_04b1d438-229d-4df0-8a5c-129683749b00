import os
import sys
import subprocess
import urllib.request
import tempfile
import ctypes
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
load_dotenv()



def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def check_aws_cli_installed():
    try:
        subprocess.run(["aws", "--version"], capture_output=True, check=True)
        return True
    except (FileNotFoundError, subprocess.CalledProcessError):
        return False


def install_aws_cli():
    print("Downloading AWS CLI installer...")
    url = "https://awscli.amazonaws.com/AWSCLIV2.msi"
    temp_dir = tempfile.gettempdir()
    installer_path = os.path.join(temp_dir, "AWSCLIV2.msi")

    try:
        urllib.request.urlretrieve(url, installer_path)
    except Exception as e:
        print(f"Download error: {e}")
        sys.exit(1)

    print("Installing AWS CLI...")
    try:
        subprocess.run(
            ["msiexec", "/i", installer_path, "/qn", "/norestart"],
            check=True,
            shell=True
        )
        print("AWS CLI installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Installation error: {e}")
        sys.exit(1)
    finally:
        if os.path.exists(installer_path):
            os.remove(installer_path)


def configure_aws_cli(access_key, secret_key, region="eu-north-1", output="json"):
    try:
        subprocess.run(["aws", "configure", "set", "aws_access_key_id", access_key], check=True)
        subprocess.run(["aws", "configure", "set", "aws_secret_access_key", secret_key], check=True)
        subprocess.run(["aws", "configure", "set", "region", region], check=True)
        subprocess.run(["aws", "configure", "set", "output", output], check=True)
        print("AWS CLI configured successfully")
    except subprocess.CalledProcessError as e:
        print(f"Configuration error: {e}")
        sys.exit(1)


def verify_iam_user(user_name):
    iam = boto3.client('iam')
    try:
        iam.get_user(UserName=user_name)
        return True
    except iam.exceptions.NoSuchEntityException:
        print(f"User {user_name} does not exist!")
        return False
    except ClientError as e:
        print(f"Error verifying user: {e}")
        return False


def create_iam_access_key(user_name):
    iam = boto3.client('iam')
    try:
        response = iam.create_access_key(UserName=user_name)
        access_key = response['AccessKey']
        print("\nNew Access Key Created:")
        print(f"Access Key ID: {access_key['AccessKeyId']}")
        print(f"Secret Access Key: {access_key['SecretAccessKey']}")
        print("\nStore these credentials securely!")
        return access_key
    except ClientError as e:
        print(f"Error creating access key: {e}")
        sys.exit(1)


def get_current_username():
    """Автоматическое определение имени текущего пользователя"""
    sts = boto3.client('sts')
    try:
        response = sts.get_caller_identity()
        arn = response['Arn']
        # Парсим ARN формата: arn:aws:iam::123456789012:user/username
        return arn.split('/')[-1]
    except Exception as e:
        print(f"Ошибка получения имени пользователя: {e}")
        return None



def main():

    get_current_username()

    # if not is_admin():
    #     print("Please run as administrator")
    #     sys.exit(1)

    existing_access_key = os.getenv("AWS_KEY")
    existing_secret_key = os.getenv("AWS_SECURITY_KEY")
    iam_user_name = os.getenv("AWS_ARN_AWS_IAM")


    # В main()
    iam_user_name = get_current_username() or "fallback-username"
    print(f"Имя пользователя IAM: {iam_user_name}")

    if not check_aws_cli_installed():
        install_aws_cli()

    configure_aws_cli(existing_access_key, existing_secret_key)

    # if not verify_iam_user(iam_user_name):
    #     sys.exit(1)

    # Не создаем новые ключи, так как они уже существуют
    # create_iam_access_key(iam_user_name)
    print("Используются существующие ключи доступа AWS")


if __name__ == "__main__":
    main()