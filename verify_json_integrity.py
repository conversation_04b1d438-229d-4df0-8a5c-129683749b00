# -*- coding: utf-8 -*-
"""
Утилита для проверки целостности и корректности JSON файлов с результатами OCR
"""

import os
import json
import sys
from collections import Counter
from typing import Optional, Dict, List

# --- НАСТРОЙКА ---
# Путь к папке с JSON файлами для проверки
JSON_DIRECTORY = r"C:\Scan\All\AlreadyAddToDb\t\split_pages"

# Путь к папке с оригинальными PDF файлами (опционально)
ORIGINAL_PDF_DIRECTORY = r"C:\Scan\All\AlreadyAddToDb\t"
# --- КОНЕЦ НАСТРОЙКИ ---


def get_pdf_page_count(pdf_path: str) -> int:
    """Получает количество страниц в PDF файле."""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            return len(pdf_reader.pages)
    except ImportError:
        print("⚠️  PyPDF2 не установлен. Установите: pip install PyPDF2")
        return -1
    except Exception as e:
        print(f"❌ Ошибка при чтении PDF {pdf_path}: {e}")
        return -1


def verify_json_file(json_path: str, original_pdf_path: Optional[str] = None) -> Dict:
    """Проверяет один JSON файл на целостность и корректность."""
    
    result = {
        'file_path': json_path,
        'json_records': 0,
        'pdf_pages': -1,
        'numbering_correct': False,
        'no_duplicates': False,
        'counts_match': False,
        'missing_pages': [],
        'duplicate_pages': [],
        'page_range': (0, 0),
        'errors': []
    }
    
    try:
        # Читаем JSON файл
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not data:
            result['errors'].append("JSON файл пустой")
            return result
            
        result['json_records'] = len(data)
        
        # Получаем количество страниц в оригинальном PDF
        if original_pdf_path and os.path.exists(original_pdf_path):
            result['pdf_pages'] = get_pdf_page_count(original_pdf_path)
        
        # Извлекаем номера страниц
        page_numbers = []
        for i, item in enumerate(data):
            if 'page_number' not in item:
                result['errors'].append(f"Запись {i+1}: отсутствует поле 'page_number'")
                continue
            page_numbers.append(item['page_number'])
        
        if not page_numbers:
            result['errors'].append("Не найдено корректных номеров страниц")
            return result
        
        # Диапазон страниц
        result['page_range'] = (min(page_numbers), max(page_numbers))
        
        # Проверяем на дубли
        page_counter = Counter(page_numbers)
        duplicates = [page for page, count in page_counter.items() if count > 1]
        result['duplicate_pages'] = duplicates
        result['no_duplicates'] = len(duplicates) == 0
        
        # Проверяем последовательность
        unique_pages = sorted(list(set(page_numbers)))
        min_page, max_page = min(unique_pages), max(unique_pages)
        expected_sequence = list(range(min_page, max_page + 1))
        
        result['numbering_correct'] = unique_pages == expected_sequence
        
        # Находим пропущенные страницы
        missing_pages = [page for page in expected_sequence if page not in page_numbers]
        result['missing_pages'] = missing_pages
        
        # Проверяем соответствие количества
        if result['pdf_pages'] > 0:
            result['counts_match'] = result['json_records'] == result['pdf_pages']
        
        return result
        
    except json.JSONDecodeError as e:
        result['errors'].append(f"Ошибка парсинга JSON: {e}")
        return result
    except Exception as e:
        result['errors'].append(f"Общая ошибка: {e}")
        return result


def print_verification_results(result: Dict) -> None:
    """Выводит результаты проверки в удобном формате."""
    
    filename = os.path.basename(result['file_path'])
    print(f"\n📄 Файл: {filename}")
    print("-" * 80)
    
    # Ошибки в первую очередь
    if result['errors']:
        print("🚨 ОШИБКИ:")
        for error in result['errors']:
            print(f"   • {error}")
        print()
    
    # Основная информация
    print(f"📊 Записей в JSON: {result['json_records']}")
    if result['pdf_pages'] > 0:
        print(f"📖 Страниц в PDF: {result['pdf_pages']}")
    else:
        print(f"📖 Страниц в PDF: не проверялось")
    
    if result['page_range'][0] > 0:
        print(f"📋 Диапазон страниц: {result['page_range'][0]} - {result['page_range'][1]}")
    
    # Проверки
    if result['pdf_pages'] > 0:
        if result['counts_match']:
            print("✅ Количество записей соответствует количеству страниц в PDF")
        else:
            print("❌ Количество записей НЕ соответствует количеству страниц в PDF")
            diff = abs(result['json_records'] - result['pdf_pages'])
            print(f"   Разница: {diff} страниц")
    
    if result['numbering_correct']:
        print("✅ Нумерация страниц корректна (последовательная)")
    else:
        print("❌ Нумерация страниц нарушена")
    
    if result['no_duplicates']:
        print("✅ Дублирующихся страниц не найдено")
    else:
        print(f"❌ Найдены дублирующиеся страницы: {result['duplicate_pages']}")
    
    if result['missing_pages']:
        print(f"⚠️  Пропущенные страницы: {result['missing_pages']}")
    
    # Общий статус
    all_good = (result['numbering_correct'] and 
                result['no_duplicates'] and 
                (result['counts_match'] or result['pdf_pages'] == -1) and
                not result['errors'] and
                not result['missing_pages'])
    
    if all_good:
        print("🎉 ОБЩИЙ СТАТУС: ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ")
    else:
        print("⚠️  ОБЩИЙ СТАТУС: ОБНАРУЖЕНЫ ПРОБЛЕМЫ")


def main():
    """Главная функция для проверки всех JSON файлов в папке."""
    
    print("=" * 80)
    print("ПРОВЕРКА ЦЕЛОСТНОСТИ JSON ФАЙЛОВ С РЕЗУЛЬТАТАМИ OCR")
    print("=" * 80)
    
    if not os.path.isdir(JSON_DIRECTORY):
        print(f"❌ Папка не найдена: {JSON_DIRECTORY}")
        return
    
    # Находим все JSON файлы
    json_files = [f for f in os.listdir(JSON_DIRECTORY) if f.lower().endswith('.json')]
    
    if not json_files:
        print(f"❌ JSON файлы не найдены в папке: {JSON_DIRECTORY}")
        return
    
    print(f"📁 Папка с JSON: {JSON_DIRECTORY}")
    print(f"📁 Папка с PDF: {ORIGINAL_PDF_DIRECTORY}")
    print(f"📊 Найдено JSON файлов: {len(json_files)}")
    
    # Проверяем каждый файл
    total_files = len(json_files)
    passed_files = 0
    failed_files = 0
    
    for json_file in sorted(json_files):
        json_path = os.path.join(JSON_DIRECTORY, json_file)
        
        # Пытаемся найти соответствующий PDF файл
        original_pdf_path = None
        if os.path.isdir(ORIGINAL_PDF_DIRECTORY):
            # Убираем _combined из имени и меняем расширение
            pdf_name = json_file.replace('_combined.json', '.pdf').replace('.json', '.pdf')
            potential_pdf = os.path.join(ORIGINAL_PDF_DIRECTORY, pdf_name)
            if os.path.exists(potential_pdf):
                original_pdf_path = potential_pdf
        
        # Проверяем файл
        result = verify_json_file(json_path, original_pdf_path)
        print_verification_results(result)
        
        # Подсчитываем статистику
        all_good = (result['numbering_correct'] and 
                    result['no_duplicates'] and 
                    (result['counts_match'] or result['pdf_pages'] == -1) and
                    not result['errors'] and
                    not result['missing_pages'])
        
        if all_good:
            passed_files += 1
        else:
            failed_files += 1
    
    # Итоговая статистика
    print("\n" + "=" * 80)
    print("ИТОГОВАЯ СТАТИСТИКА")
    print("=" * 80)
    print(f"📊 Всего файлов проверено: {total_files}")
    print(f"✅ Прошли все проверки: {passed_files}")
    print(f"⚠️  Имеют проблемы: {failed_files}")
    print(f"📈 Процент успеха: {(passed_files/total_files*100):.1f}%")


if __name__ == "__main__":
    main()
