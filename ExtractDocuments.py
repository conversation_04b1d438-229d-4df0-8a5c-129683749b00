# Извлекает каждый документ из PDF в отдельный файл.
# Наименование дается в формате: тип_документа номер документа_дата.pdf

import pdfplumber
import re
from PyPDF2 import PdfReader, PdfWriter
from datetime import datetime

ukrainian_months = {
    'січня': 1, 'лютого': 2, 'березня': 3,
    'квітня': 4, 'травня': 5, 'червня': 6,
    'липня': 7, 'серпня': 8, 'вересня': 9,
    'жовтня': 10, 'листопада': 11, 'грудня': 12
}


def parse_date(date_str):
    try:
        # Пробуем распарсить даты формата "14 вересня 2024"
        day, month, year = re.match(r'(\d{1,2})\s+([а-яіїє]+)\s+(\d{4})', date_str).groups()
        return f"{year} {ukrainian_months[month]:02d} {int(day):02d}"
    except:
        pass

    # Для числовых форматов дат
    formats = ['%d.%m.%Y', '%d/%m/%Y', '%Y-%m-%d']
    for fmt in formats:
        try:
            return datetime.strptime(date_str, fmt).strftime('%Y %m %d')
        except:
            continue
    return None


def extract_doc_info(text):
    # Для ТТН
    if 'ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА' in text:
        number_match = re.search(r'ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА[^\d]*(\d{6,})', text, re.I)
        date_match = re.search(r'від\s*([\d./]+)', text)

        doc_number = number_match.group(1) if number_match else None
        date = parse_date(date_match.group(1)) if date_match else None
        return ('ТТН', doc_number, date)

    # Для Видаткової накладної
    elif 'Видаткова накладна' in text:
        vn_match = re.search(
            r'Видаткова накладна [№]*\s*(\d+)\s+від\s+([\d\sа-яіїє]+ 20\d{2})',
            text,
            re.I
        )
        if vn_match:
            doc_number = vn_match.group(1)
            date = parse_date(vn_match.group(2))
            return ('ВН', doc_number, date)

    return (None, None, None)


def process_pdf(input_pdf):
    with pdfplumber.open(input_pdf) as pdf:
        pages = [page.extract_text() for page in pdf.pages]

    documents = []
    current_idx = 0

    while current_idx < len(pages):
        text = pages[current_idx]
        doc_type, doc_number, date = extract_doc_info(text)

        if not doc_type:
            current_idx += 1
            continue

        # Поиск конца документа
        end_idx = current_idx
        while end_idx + 1 < len(pages):
            next_text = pages[end_idx + 1]
            if extract_doc_info(next_text)[0]:
                break
            end_idx += 1

        documents.append({
            'type': doc_type,
            'number': doc_number,
            'date': date,
            'pages': list(range(current_idx, end_idx + 1))
        })

        current_idx = end_idx + 1

    # Сохранение документов
    reader = PdfReader(input_pdf)
    for doc in documents:
        if not doc['number']:
            continue

        filename = f"{doc['type']} {doc['number']}"
        if doc['date']:
            filename += f" {doc['date']}"

        writer = PdfWriter()
        for idx in doc['pages']:
            writer.add_page(reader.pages[idx])

        with open(f"{filename}.pdf", 'wb') as f:
            writer.write(f)
        print(f"Сохранен: {filename}.pdf")

# Пример использования
pdf_path = r"d:\Scan\OCR\Parsed\202409 Scan_2_ocred.pdf"
process_pdf(pdf_path)