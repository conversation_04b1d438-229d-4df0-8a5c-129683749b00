from pathlib import Path
import ocrmypdf
import logging
import sys
import subprocess
import shutil
import ctypes
from typing import Optional


class DependencyManager:
    """Управление системными зависимостями"""

    @staticmethod
    def is_admin() -> bool:
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    @staticmethod
    def check_ghostscript() -> Optional[str]:
        return shutil.which("gswin64c") or shutil.which("gs")

    @classmethod
    def ensure_dependencies(cls) -> None:
        if not cls.check_ghostscript():
            logging.error(
                "Ghostscript не найден! Установите его:\n"
                "https://ghostscript.com/releases/gsdnld.html"
            )
            sys.exit(1)


class PDFProcessor:
    """Обработчик PDF файлов"""

    def __init__(self, config: "PDFConfig"):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def process_pdf(self, input_path: Path, output_path: Path) -> None:
        try:
            self._validate_paths(input_path, output_path)
            self._run_ocr(input_path, output_path)
        except Exception as e:
            raise

    def _validate_paths(self, input_path: Path, output_path: Path) -> None:
        if not input_path.exists():
            raise FileNotFoundError(f"Файл не найден: {input_path}")

    def _run_ocr(self, input_path: Path, output_path: Path) -> None:
        ocrmypdf.ocr(
            input_file=input_path,
            output_file=output_path,
            rotate_pages=True,
            deskew=True,
            force_ocr=True,
            optimize=1,
            language=self.config.languages,
            jobs=self.config.threads,
            progress_bar=False,
            tesseract_timeout=300,
            tesseract_config={
                "dpi": 300,
                "tessedit_pageseg_mode": "6",
                "tessedit_char_whitelist": "0123456789,-",
            },
        )


class PDFConfig:
    def __init__(self, languages: str = "ukr+eng", threads: int = 4):
        self.languages = languages
        self.threads = threads


def main():
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[logging.StreamHandler()],
    )

    DependencyManager.ensure_dependencies()

    processor = PDFProcessor(PDFConfig())
    input_pdf = Path(
        r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon.pdf"
    )
    output_pdf = Path(
        r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon_ocr.pdf"
    )

    try:
        processor.process_pdf(input_pdf, output_pdf)
        logging.info("Обработка успешно завершена!")
    except Exception as e:
        logging.error(f"Фатальная ошибка: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
