# -*- coding: utf-8 -*-
"""
# ControlPdfNoSign.py
Скрипт для проверки, есть ли подпись покупателя в документе.
Работает на вугруженных из EDIN PDF файлах - расходных накладных.
Использует Kreuzberg для извлечения текста из PDF.
"""

import re
import asyncio
from Kreuzberg.pdf_extract_by_kreuzberg import extract_text_from_pdf_by_kreuzberg
import glob


def get_buyer_code(text):
    # === ИЩЕМ КОД ПОСЛЕ "ОДЕРЖУВАЧ" ===
    buyer_code = ""
    recipient_start = text.find("Одержувач:")
    if recipient_start == -1:
        print("Рядок 'Одержувач:' не знайдено")
    else:
        # Ищем ПЕРВОЕ 8-значное число после "Одержувач:"
        match = re.search(r'\d{8}', text[recipient_start:])
        if match:
            buyer_code = match.group()
            # print(f"ЄДРПОУ одержувача: {buyer_code}")
        else:
            print("Код одержувача не знайдено")
    return buyer_code


def is_refused(text):
    if "відмовлено" in text.lower():
        return True
    return False


def is_buyer_signed(text):
    # проверка, если buyer_code есть в "Власник"
    # если есть, значит ЕЦП покупателем подписан
    buyer_code = get_buyer_code(text)
    if not buyer_code: return False  # Если код не найден, сразу возвращаем False

    owner_positions = [m.start() for m in re.finditer(r'Власник', text)]

    for i, pos in enumerate(owner_positions, 1):
        # Ищем ПЕРВОЕ 8-значное число после каждого "Власник"
        match = re.search(r'\d{8}', text[pos:])
        if match:
            current_code = match.group()  # Получаем найденное 8-значное число
            if current_code == buyer_code:
                return True
        else:
            print(f"{i}. Код не знайдено")
    return False


def main(pdf_file_path):
    text = asyncio.run(extract_text_from_pdf_by_kreuzberg(pdf_file_path))
    if not text:
        print("Не вдалося отримати текст з PDF.")
        return "Error"
    if is_refused(text):
        print(f"{pdf_file_path} Refused")
        return "Refused"
    
    result = is_buyer_signed(text)
    if result:
        print(f"{pdf_file_path} signed")
        return "signed"
    
    print(f"{pdf_file_path} NoSign")
    return "NoSign"

    
if __name__ == "__main__":
    # Путь к папке с JSON-файлами
    folder_path = r'C:\Users\<USER>\Desktop\Разблокировка\32490244_ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ ЕПІЦЕНТР К\202410\Видаткова накладна\*.pdf'
    for pdf_file_path in glob.glob(folder_path):
        main(pdf_file_path)