# подключения к базе PG для внесения информации сканированных файлах и страницах

import asyncpg
import asyncio
import os
import logging
import json
from dotenv import dotenv_values

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
config = dotenv_values(".env")
DB_USER = config.get("PG_USER", "")
DB_PASSWORD = config.get("PG_PASSWORD", "")
DB_HOST = config.get("PG_HOST_LOCAL", "")
DB_PORT = config.get("PG_PORT", "")
DB_NAME = config.get("PG_DBNAME", "")

SIMILAR = "CREATE EXTENSION IF NOT EXISTS pg_trgm;"  # Создание расширения pg_trgm для поддержки функций похожести текста

if not DB_USER or not DB_PASSWORD or not DB_HOST or not DB_PORT or not DB_NAME:
    raise ValueError("Не все переменные окружения для базы данных указаны в файле .env")

TABLE_NAME = "t_scan_documents"
TABLE_NAME_RAW = "t_scan_documents_raw"


async def create_pool():
    try:
        pool = await asyncpg.create_pool(
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
        )
        return pool
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")
        # return None


# создаем таблицу
async def create_tables(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(255) NULL,
                    buyer_code varchar(255) NULL,
                    page_type INT NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    rows_list jsonb NULL,
                    external_id INT NOT NULL,
                    page_number INT NULL,
                    date_from_1c timestamp(0) NULL,
                    file_name varchar(255) NULL,
                    description TEXT NULL,
                    thinking_content TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    UNIQUE (external_id)
                );

                COMMENT ON TABLE {TABLE_NAME} IS 'Таблица для хранения информации о сканерованных документах';
                COMMENT ON COLUMN {TABLE_NAME}.doc_type IS 'Тип';
                COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'Номер';
                COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'Дата1C';
                COMMENT ON COLUMN {TABLE_NAME}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME}.created_at IS 'Дата создания записи';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN {TABLE_NAME}.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN {TABLE_NAME}.date_from_1c IS 'Дата из 1С';
                COMMENT ON COLUMN {TABLE_NAME}.page_type IS 'тип страницы';
                COMMENT ON COLUMN {TABLE_NAME}.rows_list IS 'Список строк на странице';
                COMMENT ON COLUMN {TABLE_NAME}.external_id IS 'Внешний id';
                COMMENT ON COLUMN {TABLE_NAME}.page_number IS 'Номер страницы в исходном файле';
                COMMENT ON COLUMN {TABLE_NAME}.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN {TABLE_NAME}.thinking_content IS 'логика мышления';
                COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
                COMMENT ON COLUMN {TABLE_NAME}.amount_with_vat IS 'Сумма с НДС';
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def fn_t_scan_documents(pool):
    """Создание оптимизированного триггера и вспомогательных функций"""
    try:
        async with pool.acquire() as conn:
            async with conn.transaction():
                # Удаляем старые функции
                await conn.execute("DROP FUNCTION IF EXISTS fn_t_scan_documents() CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS normalize_text_field(TEXT, TEXT) CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS normalize_numbers(TEXT) CASCADE;")
                await conn.execute("DROP FUNCTION IF EXISTS sanitize_for_filename(TEXT) CASCADE;")
#                await conn.execute("DROP FUNCTION IF EXISTS clean_single_doc_number(TEXT) CASCADE;")

                # normalize_text_field Создаем вспомогательные функции
                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION normalize_text_field(input_text TEXT, field_type TEXT DEFAULT 'general')
                    RETURNS TEXT
                    LANGUAGE plpgsql
                    IMMUTABLE
                    AS $$
                    BEGIN
                        IF input_text IS NULL OR NULLIF(TRIM(input_text), '') IS NULL THEN
                            RETURN NULL;
                        END IF;

                        -- Базовая очистка
                        input_text := REGEXP_REPLACE(input_text, '[[:cntrl:]]', '', 'g');
                        input_text := NULLIF(TRIM(input_text), '');

                        IF input_text IS NULL THEN
                            RETURN NULL;
                        END IF;

                        -- Специфичная обработка по типу поля
                        CASE field_type
                            WHEN 'buyer_name' THEN
                                RETURN REGEXP_REPLACE(UPPER(input_text), '[^А-ЯІЇЄҐ0-9A-Z"" ]', '', 'g');
                            WHEN 'buyer_code' THEN  
                                RETURN input_text;
                            WHEN 'doc_number' THEN
                                RETURN input_text;
                            ELSE
                                RETURN UPPER(input_text);
                        END CASE;
                    END;
                    $$;
                """)

                # clean_single_doc_number Надежно нормализует номер документа: 1) обрабатывает префикс "3000", 2) удаляет ВСЕ не-цифры, 3) удаляет ведущие нули.
                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION clean_single_doc_number(input_text TEXT)
                    RETURNS TEXT
                    LANGUAGE sql
                    IMMUTABLE
                    AS $$
                    WITH step1_only_digits AS (
                        -- Этот CTE гарантированно возвращает строку, состоящую только из цифр, или пустую строку.
                        SELECT
                            -- ШАГ 2: Удаляем АБСОЛЮТНО ВСЕ не-цифровые символы (\D).
                            -- Это включает латиницу, кириллицу, тире, пробелы и т.д.
                            regexp_replace(
                                -- ШАГ 1: Обрабатываем специальный случай с префиксом '3000'.
                                REPLACE(
                                    regexp_replace(
                                        COALESCE(input_text, ''),
                                        '3000(?=0*[1-9])', '#', 'g'
                                    ),
                                    '#', ''
                                ),
                                '\D', '', 'g'
                            ) AS s
                    )
                    -- Финальный, безопасный SELECT
                    SELECT
                        CASE
                            -- Если после полной очистки ничего не осталось (например, для 'ABC'), возвращаем NULL.
                            WHEN s = '' THEN NULL
                            -- И только если остались цифры, безопасно преобразуем их для удаления ведущих нулей.
                            ELSE s::NUMERIC::TEXT
                        END
                    FROM step1_only_digits;
                    $$;

                    COMMENT ON FUNCTION clean_single_doc_number(TEXT) IS 'Надежно нормализует номер документа: 1) обрабатывает префикс "3000", 2) удаляет ВСЕ не-цифры, 3) удаляет ведущие нули.';
                """)

                # sanitize_for_filename - Очищает строку для использования в качестве имени файла, удаляя запрещенные символы и нормализуя пробелы.
                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION sanitize_for_filename(input_text TEXT)
                    RETURNS TEXT
                    LANGUAGE sql
                    IMMUTABLE
                    AS $$
                      SELECT
                        -- Шаг 3: Заменяем множественные пробелы на один
                        regexp_replace(
                            -- Шаг 2: Удаляем все запрещенные символы
                            regexp_replace(
                                -- Шаг 1: Убираем пробелы в начале и в конце
                                trim(COALESCE(input_text, '')),
                                '[\\/:*?"<>|]', -- Набор запрещенных символов. Обратный слэш нужно экранировать.
                                '',            -- Заменяем их на пустоту
                                'g'            -- 'g' означает "глобально" (заменить все вхождения)
                            ),
                            '\s+', -- Паттерн для одного или более пробельных символов
                            ' ',   -- Заменяем на один пробел
                            'g'
                        )
                    $$;

                    COMMENT ON FUNCTION sanitize_for_filename(TEXT) IS 'Очищает строку для использования в качестве имени файла, удаляя запрещенные символы и нормализуя пробелы.';
                """)

                # normalize_numbers Нормализует текстовую строку со списком номеров, возвращая отсортированный jsonb-массив УНИКАЛЬНЫХ чисел.
                await conn.execute(r"""
                    CREATE OR REPLACE FUNCTION normalize_numbers(input_text TEXT)
                    RETURNS jsonb
                    LANGUAGE sql IMMUTABLE AS $$
                      SELECT
                        COALESCE(
                          jsonb_agg(DISTINCT t.num::BIGINT ORDER BY t.num::BIGINT),
                          '[]'::jsonb
                        )
                      FROM (
                        -- Вся ваша сложная логика извлечения номеров остается нетронутой
                        SELECT matches[1] AS num
                        FROM regexp_matches(
                            REPLACE(
                                regexp_replace(
                                    regexp_replace(
                                        COALESCE(input_text, ''),
                                        '3000(?=0*[1-9])', '#', 'g'
                                    ),
                                    '[а-яА-ЯёЁіІїЇєЄґҐa-zA-Z]+', '', 'g'
                                ),
                                '#', ''
                            ),
                            '(\d+)', 'g'
                        ) AS matches
                      ) AS t;
                    $$;

                    COMMENT ON FUNCTION normalize_numbers(TEXT) IS 'Нормализует текстовую строку со списком номеров, возвращая отсортированный jsonb-массив УНИКАЛЬНЫХ чисел.';
                """)

                # fn_t_scan_documents
                await conn.execute(r"""
        CREATE OR REPLACE FUNCTION public.fn_t_scan_documents()
         RETURNS trigger
         LANGUAGE plpgsql
        AS $function$
            DECLARE
                v_current_id BIGINT;
                v_raw_data RECORD;
                v_source_doc RECORD;
                source_doc_date DATE;
                source_doc_number TEXT;
                source_buyer_name TEXT;
                source_buyer_code TEXT;
                v_invoice_number TEXT;
                v_same_file BOOLEAN := FALSE;
            BEGIN
                -- Установка ID текущей записи
                v_current_id := CASE WHEN TG_OP = 'UPDATE' THEN OLD.id ELSE NULL END;
        
                -- 1. Нормализация типа документа
                NEW.doc_type := CASE 
                    WHEN UPPER(NEW.doc_type) IN ('TTH', 'TTN') THEN 'ТТН'  -- написание латиницу -> кирилицу.
                    WHEN UPPER(NEW.doc_type) IN ('BH', 'VN') THEN 'ВН'  -- написание латиницу -> кирилицу
                    ELSE NEW.doc_type
                END;
        
                -- 2. Нормализация текстовых полей
                NEW.buyer_name := normalize_text_field(NEW.buyer_name, 'buyer_name');
                NEW.buyer_name = sanitize_for_filename(NEW.buyer_name);
                NEW.buyer_code := normalize_text_field(NEW.buyer_code, 'buyer_code'); 
--                NEW.doc_number := clean_single_doc_number(COALESCE(NEW.doc_number, ''));
        
                -- 3. Получение данных из raw таблицы
                SELECT page_number, description, file_name
                INTO v_raw_data
                FROM t_scan_documents_raw 
                WHERE id = NEW.external_id;
        
                IF FOUND THEN
                    NEW.page_number := v_raw_data.page_number;
                    NEW.description := v_raw_data.description;
                    NEW.file_name := v_raw_data.file_name;
                END IF;
        
                -- Обработка JSON полей в зависимости от их типа
                -- Проверяем тип данных и нормализуем только если это TEXT
                IF pg_typeof(NEW.rows_list) = 'text'::regtype THEN
                    NEW.rows_list := normalize_numbers(NEW.rows_list::TEXT);
                ELSIF pg_typeof(NEW.rows_list) = 'jsonb'::regtype AND NEW.rows_list IS NOT NULL THEN
                    -- Если уже JSONB, то проверяем, что это массив
                    IF jsonb_typeof(NEW.rows_list) != 'array' THEN
                        NEW.rows_list := '[]'::jsonb;
                    END IF;
                END IF;
        
                IF pg_typeof(NEW.invoices_numbers) = 'text'::regtype THEN
                    NEW.invoices_numbers := normalize_numbers(NEW.invoices_numbers::TEXT);
                ELSIF pg_typeof(NEW.invoices_numbers) = 'jsonb'::regtype AND NEW.invoices_numbers IS NOT NULL THEN
                    -- Если уже JSONB, то проверяем, что это массив
                    IF jsonb_typeof(NEW.invoices_numbers) != 'array' THEN
                        NEW.invoices_numbers := '[]'::jsonb;
                    END IF;
                END IF;
        
                -- 5. Флаг совпадения файла для приоритета
                v_same_file := (EXISTS (
                    SELECT 1 FROM t_scan_documents 
                    WHERE file_name = NEW.file_name 
                    AND id IS DISTINCT FROM v_current_id
                ));
        
        
                RETURN NEW;
        
            EXCEPTION
                WHEN others THEN
                    RAISE EXCEPTION 'Trigger error (doc_type=%, page_type=%, file=%): %', 
                        NEW.doc_type, NEW.page_type, NEW.file_name, SQLERRM;
            END;
            $function$
        ;
                """)


    except Exception as e:
        raise ValueError(f"ERROR DataBase/fn_t_scan_documents: {e}")


async def create_indexes(pool, table_name: str=TABLE_NAME, table_name_raw: str=TABLE_NAME_RAW):
    """
    Создает индексы для оптимизации работы триггера.
    Каждый индекс выполняется отдельно из-за CONCURRENTLY.
    """
    index_commands = [
        # Основной составной индекс для связывания документов
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_file_type_page 
        ON {TABLE_NAME} (file_name, doc_type, page_type) 
        WHERE doc_type IN ('ТТН', 'ВН', 'ПН');
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_invoices_gin 
        ON {TABLE_NAME} USING GIN (invoices_numbers);
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_name 
        ON {TABLE_NAME} (buyer_name) 
        WHERE buyer_name IS NOT NULL;
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_code
        ON {TABLE_NAME} (buyer_code)
        WHERE buyer_code IS NOT NULL;
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_raw_id
        ON t_scan_documents_raw (id);
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_buyer_combined
        ON {TABLE_NAME} (buyer_code, buyer_name) 
        WHERE buyer_code IS NOT NULL AND buyer_name IS NOT NULL;
        """,
        f"""
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_created
        ON {TABLE_NAME} (created_at DESC);
        """,
        f"""CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_scan_docs_page_number
        ON {TABLE_NAME} (file_name, page_number);
        """
    ]

    results = []
    async with pool.acquire() as conn:
        for i, command in enumerate(index_commands, 1):
            try:
                await conn.execute(command)
                results.append(f"✓ Index {i} created successfully")
            except Exception as e:
                results.append(f"✗ Index {i} failed: {str(e)}")
                print(f"Could not execute command {i}: {command.strip()}")
                print(f"Error: {e}\n")

    return results


async def create_function_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(r"""
            CREATE OR REPLACE FUNCTION public.fn_extract_file_name()
             RETURNS trigger
             LANGUAGE plpgsql
            AS $function$
                BEGIN
                    IF new.full_path IS NOT NULL THEN
                        new.file_name := (SELECT regexp_replace(new.full_path,  '^.*[\\/]', ''));
                        new.file_name := (SELECT split_part(new.full_path,  '\', -1));
                    ELSE
                        new.file_name := NULL;
                    END IF;
            
                    new.description = trim(replace(new.description,'  ',' '));
            
                    RETURN NEW;
                END;
            $function$
            ;
        """)
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_function_base: {e}")


async def create_trigger(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_t_scan_documents();
    """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def create_trigger_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                CREATE TRIGGER trg_{TABLE_NAME_RAW}_bfr
                BEFORE INSERT OR UPDATE ON {TABLE_NAME_RAW}
                FOR EACH ROW
                EXECUTE PROCEDURE fn_extract_file_name();
            """
            )
    except asyncpg.exceptions.DuplicateObjectError:
        pass
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_trigger: {e}")


async def insert_from_extract_info(pool, json_string, full_path, path, date_parse):
    """
    Добавляет данные из формата extract_info в таблицу

    Args:
        pool: Пул соединений с базой данных
        json_string: Данные в формате extract_info
        full_path: Имя файла
        path: Путь к файлу
        date_parse: Дата парсинга
    """
    try:
        data_to_insert = []

        # Пытаемся распарсить JSON, если json_string вернул строку
        extract_data = {}
        if isinstance(json_string, str):
            import json
            try:
                logger.info(f"Получена строка JSON длиной {len(json_string)} символов")
                json_string = json_string.strip()

                # Обрабатываем различные форматы JSON
                if json_string.startswith('```json'):
                    logger.info("Обнаружен формат markdown JSON")
                    json_string = json_string[7:]
                if json_string.endswith('```'):
                    json_string = json_string[:-3]
                if json_string.startswith('"'):
                    logger.info("Обнаружены внешние кавычки")
                    json_string = json_string[1:]
                if json_string.endswith('"'):
                    json_string = json_string[:-1]

                # Выводим первые 200 символов для отладки
                logger.info(f"Подготовленная JSON строка (первые 200 символов): {json_string[:200]}...")

                # Преобразуем строку JSON в словарь Python
                extract_data = json.loads(json_string)
                logger.info(f"JSON успешно преобразован в словарь: {list(extract_data.keys())}")
            except json.JSONDecodeError as e:
                logger.error(f"Не удалось распарсить JSON {json_string} из extract_info: {e}")
                logger.error(f"Первые 200 символов строки: {json_string[:200]}...")
                logger.error(f"Последние 200 символов строки: {json_string[-200:] if len(json_string) > 200 else json_string}")
        elif isinstance(json_string, dict):
            logger.info("Получен словарь вместо строки JSON")
            extract_data = json_string

        # Проверяем различные форматы данных, которые может вернуть extract_info
        if "doc" in extract_data and isinstance(extract_data["doc"], list):
            for doc in extract_data["doc"]:
                # Преобразуем invoices_numbers в строку, если это список
                invoices_str = None
                if "invoices_numbers" in doc and isinstance(doc["invoices_numbers"], list):
                    invoices_str = ",".join(map(str, doc["invoices_numbers"]))

                # Для каждой страницы в rows_list создаем запись
                for page_num in doc.get("rows_list", []):
                    # Создаем doc_key из типа документа и номера
                    doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                    # Формируем кортеж данных для вставки
                    record = (
                        doc.get("doc_type"),
                        doc.get("doc_date"),
                        str(doc.get("doc_number")),
                        str(doc.get("page_type")),
                        page_num,
                        None,  # sort
                        full_path,
                        None,  # description
                        path,
                        date_parse,
                        doc_key,
                        None,  # date_from_1c
                        doc.get("buyer_name"),
                        str(doc.get("buyer_code")),
                        invoices_str,
                        doc.get("ID", 0)  # external_id
                    )
                    data_to_insert.append(record)

        # Проверяем другой возможный формат данных
        elif "documents" in extract_data and isinstance(extract_data["documents"], list):
            for doc in extract_data["documents"]:
                # Создаем doc_key из типа документа и номера
                doc_key = f"{doc.get('doc_type', '')}{doc.get('doc_number', '')}"

                # Получаем информацию о покупателе
                buyer_name = None
                buyer_code = None
                if "buyer" in extract_data:
                    buyer_name = extract_data["buyer"].get("name")
                    buyer_code = extract_data["buyer"].get("code")

                # Формируем кортеж данных для вставки
                record = (
                    doc.get("doc_type"),
                    doc.get("doc_date"),
                    str(doc.get("doc_number")),
                    extract_data.get("page_type"),
                    doc.get("page", 0),  # используем page из документа или 0
                    None,  # sort
                    full_path,
                    None,  # description
                    path,
                    date_parse,
                    doc_key,
                    None,  # date_from_1c
                    buyer_name,
                    str(buyer_code) if buyer_code else None,
                    None,  # invoices_numbers
                    doc.get("ID", 0)  # external_id
                )
                data_to_insert.append(record)

        if data_to_insert:
            async with pool.acquire() as conn:
                # Счетчики для статистики
                inserted_count = 0
                updated_count = 0

                for record in data_to_insert:
                    # Проверяем, существует ли запись с таким external_id
                    external_id = record[-1]  # Последний элемент в кортеже - external_id
                    existing_record = await conn.fetchrow(
                        f"SELECT id FROM {TABLE_NAME} WHERE external_id = $1",
                        external_id
                    )

                    if existing_record:
                        # Если запись существует, обновляем её
                        await conn.execute(
                            f"""
                            UPDATE {TABLE_NAME}
                            SET
                                doc_type = $1,
                                doc_date = CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                doc_number = $3,
                                page_type = $4,
                                page = $5,
                                sort = $6,
                                full_path = $7,
                                description = $8,
                                path = $9,
                                date_parse = $10,
                                doc_key = $11,
                                date_from_1c = $12,
                                buyer_name = $13,
                                buyer_code = $14,
                                invoices_numbers = $15
                            WHERE external_id = $16
                            """,
                            *record
                        )
                        updated_count += 1
                    else:
                        # Если записи нет, вставляем новую
                        await conn.execute(
                            f"""
                            INSERT INTO {TABLE_NAME}
                            (
                                doc_type,
                                doc_date,
                                doc_number,
                                page_type,
                                page,
                                sort,
                                full_path,
                                description,
                                path,
                                date_parse,
                                doc_key,
                                date_from_1c,
                                buyer_name,
                                buyer_code,
                                invoices_numbers,
                                external_id
                            )
                            VALUES ($1,
                                   CASE WHEN $2 IS NULL THEN NULL ELSE to_timestamp($2, 'DD.MM.YYYY')::date END,
                                   $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                            """,
                            *record
                        )
                        inserted_count += 1

                logger.info(f"Обработано {len(data_to_insert)} записей из extract_info: добавлено {inserted_count}, обновлено {updated_count}")
                return True
        else:
            logger.warning("Нет данных для добавления из extract_info")
            return False

    except Exception as e:
        logger.error(f"Ошибка при вставке данных из extract_info: {e}")
        raise ValueError(f"Ошибка при вставке данных из extract_info: {e}")


async def check_pages_in_db(pool, correct_page_numbers: list):
    # проверяем все ли страницы занесены в базу
    try:
        async with pool.acquire() as conn:
            for i, page in enumerate(correct_page_numbers):
                doc_key = list(correct_page_numbers[i].values())[0]
                page_number = list(correct_page_numbers[i].values())[1]
                query = f"SELECT COUNT(*) FROM {TABLE_NAME} WHERE doc_key = $1 AND page = $2"
                result = await conn.fetchval(query, doc_key, page_number)
                if result == 0:
                    logger.error(
                        f"Страница {page_number} файла {doc_key} не занесена в базу"
                    )
                    logger.error(f"doc_key: {doc_key}, page_number: {page_number}")
                    raise ValueError("Не все страницы занесены в базу")

            return True

    except Exception as e:
        logger.error(f"Ошибка при проверке страниц в базе: {e}")
        raise ValueError(f"Ошибка при проверке страниц в базе: {e}")


async def create_table_scan_all_pages_in_one_cell(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS t_scan_all_pages_in_one_cell (
                    id SERIAL PRIMARY KEY,
                    doc_type varchar(255) NULL,
                    doc_date date NULL,
                    doc_number varchar(255) NULL,
                    buyer_name varchar(55) NULL,
                    buyer_code varchar(25) NULL,
                    page_numbers jsonb NULL,
                    invoices_numbers jsonb NULL,
                    amount_with_vat numeric(15,2) NULL DEFAULT 0,
                    reasoning_content TEXT NULL,
                    file_name varchar(255) NULL,
                    full_path varchar(255) NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
                
                COMMENT ON TABLE t_scan_all_pages_in_one_cell IS 'Таблица для хранения информации о сканированных документах. Страницы объединены в ячейку.';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_type IS 'Тип';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_number IS 'Номер';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.doc_date IS 'Дата';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_name IS 'Покупатель';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.buyer_code IS 'ОКПО';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.page_numbers IS 'Список номеров страниц';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.invoices_numbers IS 'Список номеров связанных ВН';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.amount_with_vat IS 'Сумма с НДС';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.reasoning_content IS 'логика мышления';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.file_name IS 'Имя файла';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.description IS 'текст страницы';
                COMMENT ON COLUMN t_scan_all_pages_in_one_cell.created_at IS 'Дата создания записи';
                """
                )
            
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def create_tables_base(pool):
    try:
        async with pool.acquire() as conn:
            await conn.execute(
                f"""
                -- Таблица для хранения информации о сканированных документах
                CREATE TABLE IF NOT EXISTS {TABLE_NAME_RAW} (
                    id SERIAL PRIMARY KEY,
                    page_number INT NOT NULL,
                    file_name varchar(100) NOT NULL,
                    full_path varchar(255) NOT NULL,
                    description TEXT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT file_name_page_number_unq UNIQUE (file_name, page_number)
                );

                COMMENT ON TABLE {TABLE_NAME_RAW} IS 'Текст отсканированных страниц';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.full_path IS 'Полный путь к файлу';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.page_number IS 'номер страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.description IS 'текст страницы';
                COMMENT ON COLUMN {TABLE_NAME_RAW}.created_at IS 'Дата создания записи';
                """
            )
    except Exception as e:
        raise ValueError(f"ERROR DataBase/create_tables: {e}")


async def insert_document_base(pool, data: list):
    try:
        async with pool.acquire() as conn:
            await conn.executemany(
                f"""
                INSERT INTO {TABLE_NAME_RAW}
                (
                    full_path,
                    page_number,
                    description,
                    created_at
                )
                VALUES ($1, $2, $3, now())
                ON CONFLICT (file_name, page_number) DO UPDATE
                SET
                    description = EXCLUDED.description,
                    page_number = EXCLUDED.page_number,
                    full_path = EXCLUDED.full_path,
                    created_at = now()
                """,
                data,
            )
    except Exception as e:
        logger.error(f"Ошибка при вставке данных в базу: {e}")
        raise ValueError(f"Ошибка при вставке данных в базу: {e}")


async def main_database(drop_raw_table=False):
    """
    Инициализирует базу данных

    Args:
        drop_raw_table: Если True, удаляет таблицу t_scan_documents_raw
    """
    pool = await create_pool()
    if pool:
        async with pool.acquire() as conn:
            # Устанавливаем SIMILAR
            await conn.execute(SIMILAR)
            await conn.execute("CREATE EXTENSION IF NOT EXISTS unaccent;")           

            # Удаляем таблицу t_scan_documents
            # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME};")
            await fn_t_scan_documents(pool)
            await create_function_base(pool)

            # Создаем таблицы
            await create_tables(pool)
            await create_indexes(pool)
            await create_tables_base(pool)
            
            # Создаем триггеры
            await create_trigger(pool)
            await create_trigger_base(pool)

            # Удаляем таблицу t_scan_documents_raw только если указано
            # if drop_raw_table:
            #     logger.warning("Удаление таблицы t_scan_documents_raw")
            # НЕ удалять
                # await conn.execute(f"DROP TABLE IF EXISTS {TABLE_NAME_RAW};")

        await pool.close()


async def process_raw_documents(pool):
    """
    Обрабатывает документы из таблицы t_scan_documents_raw

    Args:
        pool: Пул соединений с базой данных
    """
    try:
        # Импортируем функцию main из process_documents.py
        # Используем переданный пул соединений
        from process_documents import process_with_pool
        await process_with_pool(pool)
        return True
    except Exception as e:
        logger.error(f"Ошибка при обработке документов: {e}")
        return False


async def main_database_process():
    """Запускает процесс обработки документов из raw таблицы"""
    pool = await create_pool()
    if pool:
        try:
            await process_raw_documents(pool)
        finally:
            await pool.close()


# ========== КЭШИРОВАНИЕ В POSTGRESQL ==========

CACHE_TABLE_GEMINI = "t_gemini_cache"
CACHE_TABLE_GROK = "t_grok_cache"
CACHE_TABLE_DEEPSEEK = "t_deepseek_cache"

async def create_cache_tables(pool):
    """Создает таблицы для кэширования в PostgreSQL"""
    try:
        async with pool.acquire() as conn:
            # Таблица кэша для Gemini
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {CACHE_TABLE_GEMINI} (
                    content_hash TEXT PRIMARY KEY,
                    response_text TEXT NOT NULL,
                    tokens_info JSONB,
                    cached_tokens INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP,
                    model TEXT,
                    prompt_hash TEXT,
                    file_size INTEGER
                );

                CREATE INDEX IF NOT EXISTS idx_gemini_cache_expires ON {CACHE_TABLE_GEMINI}(expires_at);
                CREATE INDEX IF NOT EXISTS idx_gemini_cache_prompt ON {CACHE_TABLE_GEMINI}(prompt_hash);
                CREATE INDEX IF NOT EXISTS idx_gemini_cache_model ON {CACHE_TABLE_GEMINI}(model);

                COMMENT ON TABLE {CACHE_TABLE_GEMINI} IS 'Кэш для запросов к Google Gemini API';
            """)

            # Таблица кэша для Grok
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {CACHE_TABLE_GROK} (
                    content_hash TEXT PRIMARY KEY,
                    response_text TEXT,
                    tokens_info JSONB,
                    cached_tokens INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP
                );

                CREATE INDEX IF NOT EXISTS idx_grok_cache_expires ON {CACHE_TABLE_GROK}(expires_at);

                COMMENT ON TABLE {CACHE_TABLE_GROK} IS 'Кэш для запросов к Grok API';
            """)

            # Таблица кэша для DeepSeek
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {CACHE_TABLE_DEEPSEEK} (
                    content_hash TEXT PRIMARY KEY,
                    response_text TEXT NOT NULL,
                    tokens_info JSONB,
                    cached_tokens INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    expires_at TIMESTAMP,
                    model TEXT,
                    prompt_hash TEXT
                );

                CREATE INDEX IF NOT EXISTS idx_deepseek_cache_expires ON {CACHE_TABLE_DEEPSEEK}(expires_at);
                CREATE INDEX IF NOT EXISTS idx_deepseek_cache_prompt ON {CACHE_TABLE_DEEPSEEK}(prompt_hash);
                CREATE INDEX IF NOT EXISTS idx_deepseek_cache_model ON {CACHE_TABLE_DEEPSEEK}(model);

                COMMENT ON TABLE {CACHE_TABLE_DEEPSEEK} IS 'Кэш для запросов к DeepSeek API';
            """)

        print("✅ Таблицы кэша созданы в PostgreSQL")
        return True

    except Exception as e:
        print(f"❌ Ошибка создания таблиц кэша: {e}")
        return False


async def save_to_pg_cache(pool, service: str, cache_key: str, response_text: str,
                          tokens_info = None, cached_tokens: int = 0,
                          model: str = None, prompt_hash: str = None, file_size: int = None,
                          ttl_hours: int = 24):
    """Сохраняет данные в кэш PostgreSQL"""
    try:
        if service == "gemini":
            table_name = CACHE_TABLE_GEMINI
        elif service == "grok":
            table_name = CACHE_TABLE_GROK
        elif service == "deepseek":
            table_name = CACHE_TABLE_DEEPSEEK
        else:
            raise ValueError(f"Неизвестный сервис: {service}")

        async with pool.acquire() as conn:
            expires_at = f"NOW() + INTERVAL '{ttl_hours} hours'"

            if service == "gemini":
                await conn.execute(f"""
                    INSERT INTO {table_name}
                    (content_hash, response_text, tokens_info, cached_tokens, expires_at, model, prompt_hash, file_size)
                    VALUES ($1, $2, $3, $4, {expires_at}, $5, $6, $7)
                    ON CONFLICT (content_hash) DO UPDATE SET
                        response_text = EXCLUDED.response_text,
                        tokens_info = EXCLUDED.tokens_info,
                        cached_tokens = EXCLUDED.cached_tokens,
                        expires_at = EXCLUDED.expires_at,
                        model = EXCLUDED.model,
                        prompt_hash = EXCLUDED.prompt_hash,
                        file_size = EXCLUDED.file_size,
                        created_at = NOW()
                """, cache_key, response_text, json.dumps(tokens_info) if tokens_info else None,
                     cached_tokens, model, prompt_hash, file_size)
            elif service == "deepseek":
                await conn.execute(f"""
                    INSERT INTO {table_name}
                    (content_hash, response_text, tokens_info, cached_tokens, expires_at, model, prompt_hash)
                    VALUES ($1, $2, $3, $4, {expires_at}, $5, $6)
                    ON CONFLICT (content_hash) DO UPDATE SET
                        response_text = EXCLUDED.response_text,
                        tokens_info = EXCLUDED.tokens_info,
                        cached_tokens = EXCLUDED.cached_tokens,
                        expires_at = EXCLUDED.expires_at,
                        model = EXCLUDED.model,
                        prompt_hash = EXCLUDED.prompt_hash,
                        created_at = NOW()
                """, cache_key, response_text, json.dumps(tokens_info) if tokens_info else None,
                     cached_tokens, model, prompt_hash)
            else:  # grok
                await conn.execute(f"""
                    INSERT INTO {table_name}
                    (content_hash, response_text, tokens_info, cached_tokens, expires_at)
                    VALUES ($1, $2, $3, $4, {expires_at})
                    ON CONFLICT (content_hash) DO UPDATE SET
                        response_text = EXCLUDED.response_text,
                        tokens_info = EXCLUDED.tokens_info,
                        cached_tokens = EXCLUDED.cached_tokens,
                        expires_at = EXCLUDED.expires_at,
                        created_at = NOW()
                """, cache_key, response_text, json.dumps(tokens_info) if tokens_info else None, cached_tokens)

        return True

    except Exception as e:
        print(f"❌ Ошибка сохранения в кэш {service}: {e}")
        return False


async def get_from_pg_cache(pool, service: str, cache_key: str):
    """Получает данные из кэша PostgreSQL"""
    try:
        if service == "gemini":
            table_name = CACHE_TABLE_GEMINI
        elif service == "grok":
            table_name = CACHE_TABLE_GROK
        elif service == "deepseek":
            table_name = CACHE_TABLE_DEEPSEEK
        else:
            raise ValueError(f"Неизвестный сервис: {service}")

        async with pool.acquire() as conn:
            if service == "gemini":
                result = await conn.fetchrow(f"""
                    SELECT response_text, tokens_info, cached_tokens, model, prompt_hash, file_size
                    FROM {table_name}
                    WHERE content_hash = $1 AND expires_at > NOW()
                """, cache_key)
            elif service == "deepseek":
                result = await conn.fetchrow(f"""
                    SELECT response_text, tokens_info, cached_tokens, model, prompt_hash
                    FROM {table_name}
                    WHERE content_hash = $1 AND expires_at > NOW()
                """, cache_key)
            else:  # grok
                result = await conn.fetchrow(f"""
                    SELECT response_text, tokens_info, cached_tokens
                    FROM {table_name}
                    WHERE content_hash = $1 AND expires_at > NOW()
                """, cache_key)

            if result:
                data = {
                    'response_text': result['response_text'],
                    'cached_tokens': result['cached_tokens'],
                    'from_cache': True
                }

                if result['tokens_info']:
                    data['tokens_info'] = json.loads(result['tokens_info'])

                if service == "gemini":
                    data.update({
                        'model': result['model'],
                        'prompt_hash': result['prompt_hash'],
                        'file_size': result['file_size']
                    })
                elif service == "deepseek":
                    data.update({
                        'model': result['model'],
                        'prompt_hash': result['prompt_hash']
                    })

                return data

        return None

    except Exception as e:
        print(f"❌ Ошибка чтения из кэша {service}: {e}")
        return None


async def cleanup_expired_pg_cache(pool, service: str = None):
    """Удаляет истекшие записи из кэша PostgreSQL"""
    try:
        async with pool.acquire() as conn:
            if service == "gemini" or service is None:
                result_gemini = await conn.execute(f"DELETE FROM {CACHE_TABLE_GEMINI} WHERE expires_at <= NOW()")
                deleted_gemini = result_gemini.split()[-1] if result_gemini else "0"

            if service == "grok" or service is None:
                result_grok = await conn.execute(f"DELETE FROM {CACHE_TABLE_GROK} WHERE expires_at <= NOW()")
                deleted_grok = result_grok.split()[-1] if result_grok else "0"

            if service == "deepseek" or service is None:
                result_deepseek = await conn.execute(f"DELETE FROM {CACHE_TABLE_DEEPSEEK} WHERE expires_at <= NOW()")
                deleted_deepseek = result_deepseek.split()[-1] if result_deepseek else "0"

            if service is None:
                print(f"🧹 Очищено из кэша: Gemini - {deleted_gemini}, Grok - {deleted_grok}, DeepSeek - {deleted_deepseek}")
            else:
                if service == "gemini":
                    deleted_count = deleted_gemini
                elif service == "grok":
                    deleted_count = deleted_grok
                else:  # deepseek
                    deleted_count = deleted_deepseek
                print(f"🧹 Очищено из кэша {service}: {deleted_count}")

        return True

    except Exception as e:
        print(f"❌ Ошибка очистки кэша: {e}")
        return False


async def get_pg_cache_stats(pool, service: str = None):
    """Получает статистику кэша PostgreSQL"""
    try:
        async with pool.acquire() as conn:
            stats = {}

            if service == "gemini" or service is None:
                result = await conn.fetchrow(f"""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active
                    FROM {CACHE_TABLE_GEMINI}
                """)
                stats['gemini'] = {
                    'total_entries': result['total'],
                    'active_entries': result['active'],
                    'expired_entries': result['total'] - result['active']
                }

            if service == "grok" or service is None:
                result = await conn.fetchrow(f"""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active
                    FROM {CACHE_TABLE_GROK}
                """)
                stats['grok'] = {
                    'total_entries': result['total'],
                    'active_entries': result['active'],
                    'expired_entries': result['total'] - result['active']
                }

            if service == "deepseek" or service is None:
                result = await conn.fetchrow(f"""
                    SELECT
                        COUNT(*) as total,
                        COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as active
                    FROM {CACHE_TABLE_DEEPSEEK}
                """)
                stats['deepseek'] = {
                    'total_entries': result['total'],
                    'active_entries': result['active'],
                    'expired_entries': result['total'] - result['active']
                }

            return stats

    except Exception as e:
        print(f"❌ Ошибка получения статистики кэша: {e}")
        return {}


async def migrate_sqlite_to_pg():
    """Мигрирует данные из SQLite файлов в PostgreSQL"""
    import sqlite3
    import os

    pool = await create_pool()
    if not pool:
        print("❌ Не удалось подключиться к PostgreSQL")
        return False

    try:
        # Миграция Gemini кэша
        gemini_db_path = "gemini_cache.db"
        if os.path.exists(gemini_db_path):
            print("🔄 Миграция Gemini кэша...")
            sqlite_conn = sqlite3.connect(gemini_db_path)
            sqlite_cursor = sqlite_conn.cursor()

            sqlite_cursor.execute("SELECT * FROM cache")
            rows = sqlite_cursor.fetchall()

            migrated = 0
            for row in rows:
                content_hash, response_text, tokens_info, cached_tokens, created_at, expires_at, model, prompt_hash, file_size = row
                success = await save_to_pg_cache(
                    pool, "gemini", content_hash, response_text,
                    json.loads(tokens_info) if tokens_info else None,
                    cached_tokens, model, prompt_hash, file_size
                )
                if success:
                    migrated += 1

            sqlite_conn.close()
            print(f"✅ Миграция Gemini: {migrated}/{len(rows)} записей")

        # Миграция Grok кэша из корня
        grok_db_path = "grok_cache.db"
        if os.path.exists(grok_db_path):
            print("🔄 Миграция Grok кэша (корень)...")
            sqlite_conn = sqlite3.connect(grok_db_path)
            sqlite_cursor = sqlite_conn.cursor()

            sqlite_cursor.execute("SELECT * FROM cache")
            rows = sqlite_cursor.fetchall()

            migrated = 0
            for row in rows:
                content_hash, response_text, tokens_info, cached_tokens, created_at, expires_at = row
                success = await save_to_pg_cache(
                    pool, "grok", content_hash, response_text,
                    json.loads(tokens_info) if tokens_info else None,
                    cached_tokens
                )
                if success:
                    migrated += 1

            sqlite_conn.close()
            print(f"✅ Миграция Grok (корень): {migrated}/{len(rows)} записей")

        # Миграция Grok кэша из папки Grok/
        grok_db_path2 = "Grok/grok_cache.db"
        if os.path.exists(grok_db_path2):
            print("🔄 Миграция Grok кэша (Grok/)...")
            sqlite_conn = sqlite3.connect(grok_db_path2)
            sqlite_cursor = sqlite_conn.cursor()

            sqlite_cursor.execute("SELECT * FROM cache")
            rows = sqlite_cursor.fetchall()

            migrated = 0
            for row in rows:
                content_hash, response_text, tokens_info, cached_tokens, created_at, expires_at = row
                success = await save_to_pg_cache(
                    pool, "grok", content_hash, response_text,
                    json.loads(tokens_info) if tokens_info else None,
                    cached_tokens
                )
                if success:
                    migrated += 1

            sqlite_conn.close()
            print(f"✅ Миграция Grok (Grok/): {migrated}/{len(rows)} записей")

        await pool.close()
        print("🎉 Миграция завершена!")
        return True

    except Exception as e:
        print(f"❌ Ошибка миграции: {e}")
        if pool:
            await pool.close()
        return False


# ========== GOOGLE CLOUD BILLING ИНТЕГРАЦИЯ ==========

import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import json

# Google Cloud клиент для биллинга
try:
    from google.cloud import billing_v1
    from google.oauth2 import service_account
    BILLING_AVAILABLE = True
except ImportError:
    BILLING_AVAILABLE = False
    print("⚠️ Google Cloud Billing клиент не установлен. Установите: pip install google-cloud-billing")

# Таблица для хранения статистики биллинга
BILLING_STATS_TABLE = "t_billing_stats"

async def create_billing_stats_table(pool):
    """Создает таблицу для хранения статистики биллинга"""
    try:
        async with pool.acquire() as conn:
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {BILLING_STATS_TABLE} (
                    id SERIAL PRIMARY KEY,
                    billing_account_id TEXT NOT NULL,
                    service_name TEXT NOT NULL,
                    sku_id TEXT NOT NULL,
                    start_time TIMESTAMP WITH TIME ZONE,
                    end_time TIMESTAMP WITH TIME ZONE,
                    cost_amount NUMERIC(10,6),
                    currency TEXT,
                    usage_amount NUMERIC(20,6),
                    usage_unit TEXT,
                    model_name TEXT,
                    region TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(billing_account_id, service_name, sku_id, start_time, end_time)
                );

                CREATE INDEX IF NOT EXISTS idx_billing_stats_time ON {BILLING_STATS_TABLE}(start_time, end_time);
                CREATE INDEX IF NOT EXISTS idx_billing_stats_model ON {BILLING_STATS_TABLE}(model_name);
                CREATE INDEX IF NOT EXISTS idx_billing_stats_service ON {BILLING_STATS_TABLE}(service_name);

                COMMENT ON TABLE {BILLING_STATS_TABLE} IS 'Статистика использования Google Cloud сервисов';
            """)

        print("✅ Таблица статистики биллинга создана")
        return True

    except Exception as e:
        print(f"❌ Ошибка создания таблицы биллинга: {e}")
        return False


def get_billing_client():
    """Создает клиент Google Cloud Billing"""
    if not BILLING_AVAILABLE:
        return None

    try:
        # Проверяем переменные окружения для аутентификации
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        billing_account = os.getenv('GOOGLE_BILLING_ACCOUNT')

        if not credentials_path or not billing_account:
            print("⚠️ GOOGLE_APPLICATION_CREDENTIALS или GOOGLE_BILLING_ACCOUNT не установлены")
            return None

        credentials = service_account.Credentials.from_service_account_file(credentials_path)
        client = billing_v1.CloudBillingClient(credentials=credentials)

        return client, billing_account

    except Exception as e:
        print(f"❌ Ошибка создания billing клиента: {e}")
        return None


async def fetch_google_billing_data(pool, days_back: int = 7):
    """Получает данные биллинга из Google Cloud за последние N дней"""
    client_data = get_billing_client()
    if not client_data:
        return None

    client, billing_account = client_data

    try:
        # Рассчитываем период
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=days_back)

        print(f"🔍 Получаем данные биллинга за период: {start_time} - {end_time}")

        # Получаем данные использования
        request = billing_v1.ListProjectBillingInfoRequest(
            name=f"billingAccounts/{billing_account}"
        )

        # Получаем список проектов
        projects = client.list_project_billing_info(request)

        billing_data = []
        total_cost = 0.0

        for project in projects:
            if project.billing_enabled:
                # Получаем детальную статистику использования
                usage_request = billing_v1.ListServicesRequest()

                # Здесь нужно использовать Cloud Billing API для получения usage
                # Но для упрощения используем заглушку с примерными данными

                # В реальном коде здесь будет:
                # usage_data = client.get_billing_usage_report(...)

                # Примерные данные для демонстрации
                sample_data = {
                    'service': 'AI Platform',
                    'model': 'gemini-2.5-flash',
                    'cost': 2.45,
                    'tokens': 150000,
                    'requests': 1200,
                    'period': f"{start_time.date()} - {end_time.date()}"
                }

                billing_data.append(sample_data)
                total_cost += sample_data['cost']

        # Сохраняем в базу данных
        async with pool.acquire() as conn:
            for data in billing_data:
                await conn.execute(f"""
                    INSERT INTO {BILLING_STATS_TABLE}
                    (billing_account_id, service_name, sku_id, start_time, end_time,
                     cost_amount, currency, usage_amount, usage_unit, model_name)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    ON CONFLICT (billing_account_id, service_name, sku_id, start_time, end_time) DO UPDATE SET
                        cost_amount = EXCLUDED.cost_amount,
                        usage_amount = EXCLUDED.usage_amount
                """, billing_account, data['service'], f"ai-{data['model']}",
                     start_time, end_time, data['cost'], 'USD',
                     data['tokens'], 'tokens', data['model'])

        print(f"✅ Данные биллинга сохранены. Общая стоимость: ${total_cost:.2f}")
        return billing_data

    except Exception as e:
        print(f"❌ Ошибка получения данных биллинга: {e}")
        return None


async def get_billing_stats_from_db(pool, model_name: str = None, days_back: int = 30):
    """Получает статистику биллинга из базы данных"""
    try:
        async with pool.acquire() as conn:
            query = f"""
                SELECT
                    model_name,
                    SUM(cost_amount) as total_cost,
                    SUM(usage_amount) as total_usage,
                    usage_unit,
                    currency,
                    MIN(start_time) as period_start,
                    MAX(end_time) as period_end,
                    COUNT(*) as records_count
                FROM {BILLING_STATS_TABLE}
                WHERE start_time >= NOW() - INTERVAL '{days_back} days'
            """

            if model_name:
                query += f" AND model_name = '{model_name}'"

            query += " GROUP BY model_name, usage_unit, currency ORDER BY total_cost DESC"

            rows = await conn.fetch(query)

            stats = []
            for row in rows:
                stats.append({
                    'model': row['model_name'],
                    'total_cost': float(row['total_cost'] or 0),
                    'total_usage': float(row['total_usage'] or 0),
                    'usage_unit': row['usage_unit'],
                    'currency': row['currency'],
                    'period_start': row['period_start'],
                    'period_end': row['period_end'],
                    'records_count': row['records_count']
                })

            return stats

    except Exception as e:
        print(f"❌ Ошибка получения статистики биллинга: {e}")
        return []


async def check_model_limits():
    """Проверяет лимиты для моделей Gemini"""
    print("🔍 Проверяем лимиты моделей Gemini...")

    # Известные лимиты Google AI (могут меняться)
    model_limits = {
        'gemini-2.5-flash': {
            'rpm': 10,  # requests per minute
            'rpd': 500,  # requests per day
            'tpm': 1000000,  # tokens per minute
            'tpd': 10000000,  # tokens per day
            'shared_limit': True  # Общий лимит с flash-lite
        },
        'gemini-2.5-flash-lite': {
            'rpm': 15,
            'rpd': 1500,
            'tpm': 1000000,
            'tpd': 10000000,
            'shared_limit': True  # Общий лимит с flash
        },
        'gemini-2.5-pro': {
            'rpm': 150,
            'rpd': 10000,
            'tpm': 4000000,
            'tpd': 100000000,
            'shared_limit': False
        }
    }

    print("📊 Лимиты моделей Gemini:")
    print("=" * 60)

    for model, limits in model_limits.items():
        print(f"🔹 {model}:")
        print(f"   • RPM: {limits['rpm']} requests/minute")
        print(f"   • RPD: {limits['rpd']} requests/day")
        print(f"   • TPM: {limits['tpm']:,} tokens/minute")
        print(f"   • TPD: {limits['tpd']:,} tokens/day")
        print(f"   • Shared limit: {'Да' if limits['shared_limit'] else 'Нет'}")
        print()

    # Важное замечание про общие лимиты
    flash_models = [m for m, l in model_limits.items() if l['shared_limit']]
    if len(flash_models) > 1:
        print("⚠️ ВАЖНО:")
        print(f"Модели {', '.join(flash_models)} имеют ОБЩИЙ лимит!")
        print("Это значит, что запросы к flash и flash-lite суммируются.")
        print("Рекомендуется использовать только одну из этих моделей.")
        print()

    return model_limits


async def run_migration():
    """Запускает миграцию данных из SQLite в PostgreSQL"""
    print("🚀 Начинаем миграцию кэша из SQLite в PostgreSQL...")

    # Создаем таблицы кэша
    pool = await create_pool()
    if pool:
        await create_cache_tables(pool)
        await create_billing_stats_table(pool)
        await pool.close()

    # Запускаем миграцию
    success = await migrate_sqlite_to_pg()
    if success:
        print("✅ Миграция завершена успешно!")
        print("💡 Теперь можно удалить старые SQLite файлы:")
        print("   - gemini_cache.db")
        print("   - grok_cache.db")
        print("   - Grok/grok_cache.db")
    else:
        print("❌ Миграция завершилась с ошибками")


if __name__ == "__main__":
    # Для тестирования можно запустить обработку документов
    # asyncio.run(main_database_process())
    asyncio.run(main_database())
