#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# https://hatchcanvas.com/project/proj_0VJyo-HbTui7vTw6SsMXP

"""
OCR для извлечения текста из отсканированных документов
Использует PyMuPDF (fitz) для работы с PDF - быстрее и эффективнее!
"""

import os
import sys
import io
from pathlib import Path
from typing import List, Optional
import argparse

try:
    import pytesseract
    from PIL import Image, ImageEnhance, ImageFilter
    import cv2
    import numpy as np
    import fitz  # PyMuPDF - лучше чем pdf2image!
except ImportError as e:
    print(f"Ошибка импорта: {e}")
    print("Установите недостающие библиотеки:")
    print("pip install pytesseract pillow opencv-python PyMuPDF")
    print("PyMuPDF (fitz) - быстрее и эффективнее pdf2image!")
    sys.exit(1)


class OCRProcessor:
    """Класс для обработки OCR различных типов документов"""

    def __init__(self, language: str = 'ukr+eng'):
        """
        Инициализация OCR процессора

        Args:
            language: Языки для распознавания (ukr+eng, eng, rus и т.д.)
        """
        self.language = language
        self.supported_formats = ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.pdf']

        # Проверяем, установлен ли Tesseract
        try:
            pytesseract.get_tesseract_version()
        except Exception:
            print("Tesseract OCR не найден!")
            print("Установите Tesseract OCR:")
            print("Windows: https://github.com/UB-Mannheim/tesseract/wiki")
            print("Linux: sudo apt install tesseract-ocr tesseract-ocr-rus")
            print("macOS: brew install tesseract tesseract-lang")
            sys.exit(1)

    def preprocess_image(self, image: Image.Image) -> Image.Image:
        """
        Предобработка изображения для улучшения качества OCR

        Args:
            image: Исходное изображение

        Returns:
            Обработанное изображение
        """
        # Конвертируем в оттенки серого
        if image.mode != 'L':
            image = image.convert('L')

        # Увеличиваем контрастность
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2.0)

        # Увеличиваем резкость
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.5)

        # Применяем фильтр для уменьшения шума
        image = image.filter(ImageFilter.MedianFilter())

        return image

    def preprocess_with_opencv(self, image_path: str) -> np.ndarray:
        """
        Продвинутая предобработка с помощью OpenCV

        Args:
            image_path: Путь к изображению

        Returns:
            Обработанное изображение как numpy array
        """
        # Загружаем изображение
        img = cv2.imread(image_path)

        # Конвертируем в оттенки серого
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # Применяем гауссово размытие для уменьшения шума
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Применяем адаптивную бинаризацию
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY, 11, 2
        )

        # Морфологические операции для очистки
        kernel = np.ones((1, 1), np.uint8)
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

        return processed

    def extract_text_from_image(self, image_path: str, use_opencv: bool = False) -> str:
        """
        Извлечение текста из изображения

        Args:
            image_path: Путь к файлу изображения
            use_opencv: Использовать ли предобработку OpenCV

        Returns:
            Извлеченный текст
        """
        try:
            if use_opencv:
                # Используем предобработку OpenCV
                processed_img = self.preprocess_with_opencv(image_path)
                # Конвертируем numpy array в PIL Image
                pil_img = Image.fromarray(processed_img)
            else:
                # Обычная предобработка PIL
                image = Image.open(image_path)
                pil_img = self.preprocess_image(image)

            # Выполняем OCR
            text = pytesseract.image_to_string(
                pil_img,
                lang=self.language,
                config='--psm 6'  # Предполагаем единый блок текста
            )

            return text.strip()

        except Exception as e:
            return f"Ошибка при обработке {image_path}: {str(e)}"

    def extract_text_from_pdf(self, pdf_path: str) -> List[str]:
        """
        Извлечение текста из PDF файла используя PyMuPDF (fitz)
        ПРЕИМУЩЕСТВА FITZ:
        - В 3-5 раз быстрее pdf2image
        - Меньше использует памяти
        - Может извлекать текст напрямую без OCR
        - Высокое качество рендеринга

        Args:
            pdf_path: Путь к PDF файлу

        Returns:
            Список текстов для каждой страницы
        """
        try:
            # Открываем PDF с помощью PyMuPDF
            pdf_document = fitz.open(pdf_path)
            texts = []

            print(f"📄 Обнаружено страниц: {len(pdf_document)}")

            for page_num in range(len(pdf_document)):
                print(f"📖 Обрабатываем страницу {page_num + 1}/{len(pdf_document)}...")

                page = pdf_document[page_num]

                # УМНЫЙ ПОДХОД: Сначала пытаемся извлечь текст напрямую
                direct_text = page.get_text().strip()

                if direct_text and len(direct_text) > 50:
                    # Если найден текстовый слой - используем его (БЫСТРО!)
                    texts.append(direct_text)
                    print(f"  ✅ Текст извлечен напрямую ({len(direct_text)} символов)")
                else:
                    # Если текста мало/нет - используем OCR (медленнее)
                    print(f"  🔍 Мало текста напрямую, используем OCR...")

                    # Рендерим страницу в высоком разрешении
                    mat = fitz.Matrix(2.0, 2.0)  # 2x увеличение = 144 DPI
                    pix = page.get_pixmap(matrix=mat)

                    # Конвертируем в PIL Image
                    img_data = pix.tobytes("ppm")
                    pil_img = Image.open(io.BytesIO(img_data))

                    # Предобрабатываем и извлекаем текст через OCR
                    processed_img = self.preprocess_image(pil_img)
                    ocr_text = pytesseract.image_to_string(
                        processed_img,
                        lang=self.language,
                        config='--psm 6'
                    ).strip()

                    texts.append(ocr_text)
                    print(f"  ✅ OCR завершен ({len(ocr_text)} символов)")

            pdf_document.close()
            print(f"🎉 PDF обработан успешно!")
            return texts

        except Exception as e:
            return [f"❌ Ошибка при обработке PDF {pdf_path}: {str(e)}"]

    def process_file(self, file_path: str, output_file: Optional[str] = None) -> str:
        """
        Обработка файла (изображение или PDF)

        Args:
            file_path: Путь к файлу
            output_file: Путь для сохранения результата (опционально)

        Returns:
            Извлеченный текст
        """
        file_path = Path(file_path)

        if not file_path.exists():
            return f"❌ Файл не найден: {file_path}"

        if file_path.suffix.lower() not in self.supported_formats:
            return f"❌ Неподдерживаемый формат файла: {file_path.suffix}"

        print(f"🚀 Обрабатываем файл: {file_path}")

        if file_path.suffix.lower() == '.pdf':
            # Обрабатываем PDF с помощью PyMuPDF
            texts = self.extract_text_from_pdf(str(file_path))
            result = "\n\n--- НОВАЯ СТРАНИЦА ---\n\n".join(texts)
        else:
            # Обрабатываем изображение
            result = self.extract_text_from_image(str(file_path), use_opencv=True)

        # Сохраняем результат в файл, если указан путь
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"💾 Результат сохранен в: {output_file}")

        return result

    def process_directory(self, directory: str, output_dir: Optional[str] = None):
        """
        Обработка всех поддерживаемых файлов в директории

        Args:
            directory: Путь к директории
            output_dir: Директория для сохранения результатов
        """
        dir_path = Path(directory)

        if not dir_path.exists():
            print(f"❌ Директория не найдена: {directory}")
            return

        if output_dir:
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)

        files_found = []
        for file_path in dir_path.iterdir():
            if file_path.suffix.lower() in self.supported_formats:
                files_found.append(file_path)

        if not files_found:
            print(f"⚠️ Не найдено поддерживаемых файлов в {directory}")
            return

        print(f"📁 Найдено файлов для обработки: {len(files_found)}")

        for i, file_path in enumerate(files_found, 1):
            print(f"\n[{i}/{len(files_found)}] 🔄 Обрабатываем: {file_path.name}")

            # Определяем путь выходного файла
            if output_dir:
                output_file = output_path / f"{file_path.stem}_ocr.txt"
            else:
                output_file = None

            # Обрабатываем файл
            text = self.process_file(str(file_path), str(output_file) if output_file else None)

            if not output_file:
                print(f"📄 Результат для {file_path.name}:")
                print("-" * 50)
                print(text[:300] + "..." if len(text) > 300 else text)
                print("-" * 50)


def main(file_path: str, output_file: Optional[str] = None, language: str = 'ukr+eng'):
    """
    Основная функция для обработки файла

    Args:
        file_path: Путь к файлу для обработки
        output_file: Путь для сохранения результата (опционально)
        language: Языки для распознавания (по умолчанию: ukr+eng)

    Returns:
        str: Извлеченный текст
    """
    print("🔍 OCR Processor с PyMuPDF (fitz)")
    print("=" * 40)

    # Создаем процессор OCR
    ocr = OCRProcessor(language=language)

    # Обрабатываем файл
    result = ocr.process_file(file_path, output_file)

    if not output_file:
        print("\n📋 Извлеченный текст:")
        print("=" * 60)
        print(result)
        print("=" * 60)

    return result


def main_cli():
    """Основная функция с интерфейсом командной строки"""
    parser = argparse.ArgumentParser(
        description='OCR для извлечения текста из документов (с PyMuPDF)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:
  python ocr_fitz.py документ.jpg                    # Обработать изображение
  python ocr_fitz.py скан.pdf -o результат.txt       # PDF в текстовый файл  
  python ocr_fitz.py папка_файлов -d                 # Обработать всю папку
  python ocr_fitz.py документ.pdf -l eng             # Только английский
        """
    )

    parser.add_argument('input', help='Путь к файлу или директории')
    parser.add_argument('-o', '--output', help='Путь для сохранения результата')
    parser.add_argument('-l', '--language', default='ukr+eng',
                        help='Языки для распознавания (по умолчанию: ukr+eng)')
    parser.add_argument('-d', '--directory', action='store_true',
                        help='Обработать все файлы в директории')

    args = parser.parse_args()

    # Создаем процессор OCR
    ocr = OCRProcessor(language=args.language)

    if args.directory:
        # Обрабатываем директорию
        ocr.process_directory(args.input, args.output)
    else:
        # Используем новую функцию main
        main(args.input, args.output, args.language)


# Пример использования
if __name__ == "__main__":
    pdf_file_path_in = r"C:\Users\<USER>\Desktop\2025-08-21_105102 Vozvrat-001.pdf"
    # сохраняем в текстовый файл
    pdf_file_path_out = Path(pdf_file_path_in).with_suffix('.txt')
    
    main(pdf_file_path_in, pdf_file_path_out)
    # Если запускается без аргументов
    if len(sys.argv) == 1:
        print("🔍 OCR с PyMuPDF (fitz) - улучшенная версия!")
        print("=" * 50)

        ocr = OCRProcessor(language='ukr+eng')

        print(f"\n📋 Поддерживаемые форматы: {', '.join(ocr.supported_formats)}")
        print(f"🌐 Язык распознавания: {ocr.language}")

        print("\n💡 Примеры использования:")
        print("  python script.py документ.pdf")
        print("  python script.py скан.jpg -o результат.txt")
        print("  python script.py папка_сканов -d -o папка_результатов")

        # Проверяем наличие файлов в текущей папке
        current_files = [f for f in os.listdir('.')
                         if any(f.lower().endswith(ext) for ext in ocr.supported_formats)]
        if current_files:
            print(f"\n📁 Файлы в текущей директории: {current_files[:3]}...")
    else:
        main_cli()
