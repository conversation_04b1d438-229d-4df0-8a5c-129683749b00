import os
import re
import pandas as pd
from datetime import datetime

def rename_files_with_block(folder_path, doc_numbers):
    """
    Переименовывает файлы, добавляя 'Block' к названию для указанных номеров документов
    и создает Excel файл с результатами
    
    Args:
        folder_path (str): путь к папке с файлами
        doc_numbers (list): список номеров документов в формате УУ000011066
    """
    
    # Создаем список для результатов
    results = []
    
    # Получаем список всех файлов в папке
    try:
        files = os.listdir(folder_path)
    except FileNotFoundError:
        print(f"Ошибка: Папка '{folder_path}' не найдена")
        return
    except PermissionError:
        print(f"Ошибка: Нет доступа к папке '{folder_path}'")
        return
    
    # Создаем словарь для сопоставления номеров без префикса и ведущих нулей
    numbers_to_find = {}
    for doc_number in doc_numbers:
        # Извлекаем только цифровую часть номера и убираем ведущие нули
        numeric_part = re.search(r'\d+', doc_number)
        if numeric_part:
            # Преобразуем в число и обратно в строку, чтобы убрать ведущие нули
            clean_number = str(int(numeric_part.group()))
            numbers_to_find[clean_number] = doc_number
            print(f"Ищем номер: {clean_number} (из {doc_number})")
    
    renamed_count = 0
    found_files = {}  # Словарь для найденных файлов (номер -> имя файла)
    
    for filename in files:
        # Ищем файлы с расширением .pdf
        if filename.lower().endswith('.pdf'):
            # Ищем номер в названии файла
            number_match = re.search(r'(\d+) ', filename)
            if number_match:
                file_number = number_match.group(1)
                # Также убираем ведущие нули из номера в文件名
                clean_file_number = str(int(file_number))
                
                # Проверяем, есть ли этот номер в нашем списке
                if clean_file_number in numbers_to_find:
                    # Сохраняем найденный файл
                    found_files[clean_file_number] = filename
                    
                    # Формируем новое имя файла
                    if 'Block' not in filename:
                        # Добавляем 'Block' перед расширением .pdf
                        new_filename = filename.replace('.pdf', ' Block.pdf')
                        
                        # Полные пути к файлам
                        old_path = os.path.join(folder_path, filename)
                        new_path = os.path.join(folder_path, new_filename)
                        
                        try:
                            os.rename(old_path, new_path)
                            print(f"Переименован: {filename} -> {new_filename}")
                            renamed_count += 1
                        except Exception as e:
                            print(f"Ошибка при переименовании {filename}: {e}")
    
    # Формируем данные для Excel
    for doc_number in doc_numbers:
        # Извлекаем цифровую часть для поиска
        numeric_part = re.search(r'\d+', doc_number)
        if numeric_part:
            # Убираем ведущие нули
            num_key = str(int(numeric_part.group()))
            filename = found_files.get(num_key, '')  # Пустое значение если файл не найден
        else:
            filename = ''
        
        results.append({
            'Номер документа': doc_number,
            'Имя файла': filename
        })
    
    # Создаем DataFrame и сохраняем в Excel
    df = pd.DataFrame(results)
    
    # Формируем путь для Excel файла
    excel_filename = "ПН_ВН_сверка_документов.xlsx"
    excel_path = os.path.join(folder_path, excel_filename)
    
    # Проверяем, существует ли файл, и добавляем timestamp если нужно
    if os.path.exists(excel_path):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        excel_filename = f"ПН_ВН_сверка_документов_{timestamp}.xlsx"
        excel_path = os.path.join(folder_path, excel_filename)
    
    try:
        # Сохраняем в Excel с настройкой ширины колонок
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Сверка документов')
            
            # Настраиваем ширину колонок
            worksheet = writer.sheets['Сверка документов']
            worksheet.column_dimensions['A'].width = 20
            worksheet.column_dimensions['B'].width = 70
        
        print(f"\nExcel файл создан: {excel_path}")
        
    except Exception as e:
        print(f"Ошибка при создании Excel файла: {e}")
        # Альтернативное сохранение без форматирования
        try:
            df.to_excel(excel_path, index=False)
            print(f"Excel файл создан (без форматирования): {excel_path}")
        except Exception as e2:
            print(f"Не удалось создать Excel файл: {e2}")
    
    # Выводим статистику
    found_count = len([x for x in results if x['Имя файла'] != ''])
    print(f"\nСтатистика:")
    print(f"Всего документов в списке: {len(doc_numbers)}")
    print(f"Найдено файлов: {found_count}")
    print(f"Не найдено файлов: {len(doc_numbers) - found_count}")
    print(f"Переименовано файлов: {renamed_count}")


if __name__ == "__main__":
    # Использование
    folder_path = r"C:\Users\<USER>\Desktop\Разблокировка\32490244 Епіцентр К\202411\РК"
    doc_numbers = [
'15606',
'15607',
'15608',
'15609',
'15338',
'15435',
'15434',
'15439',
'15428',
'15433',
'15437',
'15442',
'15427',
'15441',
'15431',
'15440',
'15446',
'15443',
'15445',
'15432',
'15438',
'15444',
'15487',
'15500',
'15493',
'15490',
'15498',
'15424',
'15496',
'15426',
'15488',
'15489',
'15499',
'15497',
'15425',
'15429',
'15430',
'15480',
'15481',
'15483',
'15485',
'15484',
'15562',
'15568',
'15558',
'15564',
'15550',
'15863',
'15571',
'15855',
'15857',
'15555',
'15554',
'15573',
'15859',
'15856',
'15570',
'15841',
'15557',
'15566',
'15565',
'15860',
'15849',
'15850',
'15847',
'15845',
'15843',
    ]
    rename_files_with_block(folder_path, doc_numbers)