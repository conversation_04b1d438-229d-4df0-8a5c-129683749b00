import asyncio
import time
from collections import deque

class GeminiRateLimiter:
    def __init__(self, max_calls=14, period=60):
        """
        Ограничитель частоты вызовов Gemini API
        
        :param max_calls: Максимальное количество вызовов за период
        :param period: Период в секундах (по умолчанию 60 секунд = 1 минута)
        """
        self.max_calls = max_calls
        self.period = period
        self.call_times = deque()
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """
        Ожидает, если достигнут лимит вызовов
        """
        async with self.lock:
            now = time.time()
            
            # Удаляем устаревшие метки времени
            while self.call_times and now - self.call_times[0] > self.period:
                self.call_times.popleft()
            
            # Если достигнут лимит, ждем
            if len(self.call_times) >= self.max_calls:
                wait_time = self.call_times[0] + self.period - now
                if wait_time > 0:
                    print(f"[{time.strftime('%H:%M:%S')}] Достигнут лимит API. Ожидание {wait_time:.2f} сек...")
                    await asyncio.sleep(wait_time)
            
            # Добавляем новую метку времени
            self.call_times.append(time.time())