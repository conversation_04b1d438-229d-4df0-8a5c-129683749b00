import os
import json
from pathlib import Path
from dotenv import load_dotenv
# import google.generativeai as genai
from google import genai
from pdf2image import convert_from_path, pdfinfo_from_path, exceptions as pdf2image_exceptions
import hashlib
from joblib import Memory
import time
import shutil
import sys
from datetime import datetime
from PIL import Image 

# --- Глобальные настраиваемые параметры ---
# Если Poppler в PATH, можно оставить None или пустую строку ""
POPPLER_PATH_CONFIG = r"C:\Users\<USER>\Desktop\poppler-24.02.0\Library\bin" 
TARGET_DPI = 200  # Целевое разрешение для конвертации PDF в изображения

# Используйте актуальные и доступные вам идентификаторы моделей
GEMINI_OCR_MODEL_NAME = 'gemini-2.0-flash' # или 'gemini-2.0-flash', 'gemini-2.5-pro-preview-03-25' если доступны
GEMINI_ANALYSIS_MODEL_NAME = 'gemini-2.0-flash' # или 'gemini-1.5-pro-latest', 'gemini-2.0-flash', 'gemini-2.5-pro-preview-03-25'

# --- Функции проверки ---
def check_poppler_installation(poppler_bin_path_str: str = None) -> str | None:
    poppler_executables_win = ["pdfinfo.exe", "pdftoppm.exe"]
    poppler_executables_nix = ["pdfinfo", "pdftoppm"]
    required_executables = poppler_executables_win if os.name == 'nt' else poppler_executables_nix

    # 1. Проверка пути, указанного в конфигурации
    if poppler_bin_path_str:
        poppler_bin_path = Path(poppler_bin_path_str)
        if poppler_bin_path.is_dir():
            all_found = True
            for exe in required_executables:
                if not (poppler_bin_path / exe).is_file():
                    all_found = False
                    # print(f"ПРЕДУПРЕЖДЕНИЕ: Исполняемый файл Poppler '{exe}' не найден в '{poppler_bin_path}'.")
                    break
            if all_found:
                print(f"ИНФО: Poppler найден по указанному пути: '{poppler_bin_path}'.")
                return str(poppler_bin_path)
        # else:
            # print(f"ПРЕДУПРЕЖДЕНИЕ: Указанный POPPLER_PATH_CONFIG '{poppler_bin_path_str}' не является директорией или не существует.")

    # 2. Попытка найти в системном PATH
    # print("ИНФО: Попытка найти Poppler в системном PATH...")
    found_in_path = True
    first_exe_path_in_system = None
    for exe in required_executables:
        exe_path = shutil.which(exe)
        if not exe_path:
            found_in_path = False
            # print(f"ПРЕДУПРЕЖДЕНИЕ: Исполняемый файл Poppler '{exe}' не найден в системном PATH.")
            break
        if first_exe_path_in_system is None:
            first_exe_path_in_system = Path(exe_path).parent
    
    if found_in_path and first_exe_path_in_system:
        print(f"ИНФО: Poppler (утилита '{required_executables[0]}') найдена в системном PATH: '{first_exe_path_in_system}'.")
        return str(first_exe_path_in_system)

    print("\nОШИБКА КРИТИЧЕСКАЯ: Утилиты Poppler не найдены в системе!")
    print("Poppler необходим для конвертации PDF в изображения.")
    print("\nПОЖАЛУЙСТА, ВЫПОЛНИТЕ СЛЕДУЮЩЕЕ:")
    print("1. СКАЧАЙТЕ Poppler для Windows:")
    print("   - Ищите 'poppler windows binary'. Рекомендуются сборки от 'milosz Danczak' (через GitHub) или от conda-forge.")
    print("   - Распакуйте архив в удобное место, например, 'C:\\Program Files\\poppler' или 'D:\\tools\\poppler'.")
    print("\n2. НАСТРОЙТЕ ПУТЬ К Poppler ОДНИМ ИЗ ДВУХ СПОСОБОВ:")
    print("   А) (РЕКОМЕНДУЕТСЯ) Добавьте путь к директории 'bin' (или 'Library\\bin') внутри папки Poppler в системную переменную PATH.")
    print("      - ПОСЛЕ ИЗМЕНЕНИЯ PATH ОБЯЗАТЕЛЬНО ПЕРЕЗАПУСТИТЕ ВАШУ IDE ИЛИ ТЕРМИНАЛ!")
    print("   Б) Укажите АБСОЛЮТНЫЙ путь к директории 'bin' (или 'Library\\bin') Poppler в переменной 'POPPLER_PATH_CONFIG' в самом начале этого Python скрипта.")
    print(f"      - Текущее значение в скрипте: '{POPPLER_PATH_CONFIG if POPPLER_PATH_CONFIG else 'Не указано (поиск в PATH)'}'")
    print("\nПосле выполнения этих шагов, перезапустите скрипт.")
    return None

# --- Конфигурация ---
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY: print("ОШИБКА КРИТИЧЕСКАЯ: Не найден GEMINI_API_KEY в .env файле."); sys.exit(1)
genai.configure(api_key=GEMINI_API_KEY)

print("--- Проверка необходимых компонентов ---")
VALID_POPPLER_PATH = check_poppler_installation(POPPLER_PATH_CONFIG)
if not VALID_POPPLER_PATH: sys.exit(1)
print("--- Проверка компонентов завершена успешно ---\n")

PDF_INPUT_DIR = Path(r"C:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\Parsed")
TXT_OUTPUT_DIR = PDF_INPUT_DIR.parent / "OCR_Text_Output_v4" # Папка для ВСЕХ исходящих TXT с OCR
JSON_OUTPUT_DIR = PDF_INPUT_DIR.parent / "JSON_Output_v4"
TEMP_IMAGE_DIR = Path("temp_images_for_gemini_v4") 
CACHE_DIR = Path("cache_dir_gemini_ocr_v4")       

for p_dir in [TXT_OUTPUT_DIR, JSON_OUTPUT_DIR, TEMP_IMAGE_DIR, CACHE_DIR]: # TXT_OUTPUT_DIR добавлена сюда
    p_dir.mkdir(exist_ok=True, parents=True)

memory = Memory(CACHE_DIR, verbose=1)

# --- Промпты ---
GEMINI_OCR_PROMPT = """Тебе дано изображение страницы документа.
Твоя задача - выполнить качественное оптическое распознавание символов (OCR) на украинском (приоритет) и русском языках.
Верни ТОЛЬКО чистый распознанный текст этой страницы. Никаких дополнительных объяснений или форматирования, только текст.
Если изображение пустое или очевидно не содержит текста (например, полностью черное, белое или случайный шум без текстовых символов), верни строку "<ПУСТАЯ_СТРАНИЦА>".
"""

GEMINI_ANALYSIS_PROMPT_TEMPLATE = """
Тебе дан OCR-текст страниц ОДНОГО PDF-файла. Страницы разделены маркерами "--- СТРАНИЦА N ---".
Некоторые страницы могут быть "<ПУСТАЯ_СТРАНИЦА>" - их следует ИГНОРИРОВАТЬ при формировании документов, если они не являются явно внутренним пустым разворотом уже идентифицированного многостраничного документа.
PDF-файл может содержать НЕСКОЛЬКО РАЗНЫХ документов. Твоя задача - идентифицировать ТОЛЬКО документы типа "ВН" (Видаткова накладна) и "ТТН" (Товарно-транспортна накладна).

ТВОЯ ЗАДАЧА - СТРОГО ПОШАГОВО:
ШАГ 1: ИДЕНТИФИКАЦИЯ ПЕРВЫХ СТРАНИЦ ДОКУМЕНТОВ
    - Просмотри все НЕПУСТЫЕ страницы.
    - Найди страницы, которые являются ПЕРВЫМИ (лицевыми) страницами ВН или ТТН по следующим признакам:
        - **Первая страница ВН:** Содержит заголовок "Видаткова накладна №..." И информацию о "Постачальник:" И "Покупець:".
        - **Первая страница ТТН:** Содержит заголовок "ТОВАРНО-ТРАНСПОРТНА НАКЛАДНА №..." И информацию о "Вантажовідправник:" И "Вантажоодержувач:".
    - Для каждой найденной первой страницы запомни ее номер (из маркера "--- СТРАНИЦА N ---") и тип документа.

ШАГ 2: ГРУППИРОВКА ПОСЛЕДУЮЩИХ СТРАНИЦ ДЛЯ КАЖДОГО НАЙДЕННОГО ДОКУМЕНТА
    - Для каждой идентифицированной на ШАГЕ 1 первой страницы документа:
        - Начиная со следующей за ней НЕПУСТОЙ страницы в общем потоке, проверяй, относится ли она к текущему документу, пока не встретишь признаков НАЧАЛА НОВОГО ДОКУМЕНТА (см. ШАГ 1) или конца всех страниц.
        - **Признаки средних и последних страниц ВН, относящихся к НАЧАТОЙ ВН:**
            - Средняя ВН: Продолжение таблицы товаров. Обычно нет полных реквизитов Поставщика/Покупателя.
            - Последняя ВН: Наличие итоговой суммы прописью (например, "П'ять тисяч...") И подписей "Від постачальника" / "Отримав(ла)". Это КОНЕЦ документа ВН.
        - **Признаки средних и последних страниц ТТН, относящихся к НАЧАТОЙ ТТН:**
            - Средняя ТТН (оборотная): Таблица с заголовком "ВІДОМОСТІ ПРО ВАНТАЖ". Может быть несколько.
            - Последняя ТТН: Наличие НЕ МЕНЕЕ 7 из 10 слов/фраз: "вантаж", "габарити", "вага", "автомобіль", "причіп", "прибуття", "довжина", "ширина", "висота", "усього місць/брутто" И/ИЛИ разделы "ВІДОМОСТІ ПРО ΠΑΡΑΜЕТРИ ТРАНСПОРТНОГО ЗАСОБУ", "ВАНТАЖО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ" И подписи "Здав"/"Прийняв". КРИТИЧНО: ОТСУТСТВУЕТ сумма прописью как у ВН. Это КОНЕЦ документа ТТН.
    - Собери все страницы (первую, средние, последнюю), которые ты отнес к одному документу.

ШАГ 3: ФОРМИРОВАНИЕ ВЫВОДА ДЛЯ КАЖДОГО УНИКАЛЬНОГО ДОКУМЕНТА
    - Для каждого собранного на ШАГЕ 2 документа извлеки следующую информацию и предоставь ОБОСНОВАНИЕ ГРУППИРОВКИ.

ДЕТАЛИ ИЗВЛЕЧЕНИЯ для каждого документа:
    a. `document_type`: "ТТН" или "ВН" (определено на ШАГЕ 1).
    b. `document_number`: Номер документа (с первой страницы).
    c. `document_date`: Дата документа (с первой страницы, формат dd.mm.yyyy).
    d. `buyer_name`, `buyer_code`: Покупатель.
        - ВН: из поля "Покупець:" на первой странице ВН.
        - ТТН: из секции "Вантажоодержувач" на первой странице ТТН. (Игнорируй Поставщика/Замовника).
        - Общее: Без "ТОВ/ПП", код ЕДРПОУ 8 цифр (null, если не найден/неверный формат). Игнорируй поставщика "ПРЕСТИЖ".
    e. `pages_original_order`: Список ИСХОДНЫХ номеров страниц (из маркеров), отнесенных к этому документу на ШАГЕ 2.
    f. `pages_sorted_order`: Те же номера из `pages_original_order`, но отсортированные: [первая, все средние (по возрастанию исходных номеров), последняя].
    g. `invoices_numbers`: Для ТТН - список номеров связанных ВН. Для ВН - null.
    h. `file_name_suggestion`: Имя файла (ТИП_НОМЕР_ГГГГ_ММ_ДД.json).
    i. `page_assignment_reasoning`: Твое краткое обоснование, ПОЧЕМУ эти страницы (`pages_original_order`) были сгруппированы в этот документ, со ссылкой на КЛЮЧЕВЫЕ ПРИЗНАКИ первой, средних (если есть) и последней страниц для ЭТОГО документа.

ФОРМАТ ВЫВОДА: JSON-массив. Каждый элемент - словарь для одного документа.
Если нет документов ВН или ТТН, верни [].

ОПИСАНИЕ JSON-СТРУКТУРЫ для каждого документа:
{ 
    "document_type": "ТТН" | "ВН",
    "document_number": "string",
    "document_date": "dd.mm.yyyy",
    "pages_original_order": [int, ...],
    "pages_sorted_order": [int, ...],
    "invoices_numbers": ["string", ...] | null,
    "buyer_name": "string" | null,
    "buyer_code": "string" | null,
    "file_name_suggestion": "string",
    "page_assignment_reasoning": "string"
}

ТЕКСТ ДЛЯ АНАЛИЗА:
--- ВСЕ СТРАНИЦЫ PDF НАЧАЛО ---
{all_pages_text_content}
--- ВСЕ СТРАНИЦЫ PDF КОНЕЦ ---
"""

# --- Функции ---
def get_file_hash(filepath: Path) -> str:
    h = hashlib.sha256()
    try:
        with open(filepath, 'rb') as f:
            while True:
                chunk = f.read(8192)
                if not chunk: break
                h.update(chunk)
        return h.hexdigest()
    except FileNotFoundError: return "file_not_found"

@memory.cache
def get_ocr_text_from_image_gemini(image_path_str: str, image_hash: str) -> str:
    img_path = Path(image_path_str)
    if not img_path.exists():
        print(f"    Изображение {img_path.name} не найдено для OCR.")
        return "<ОШИБКА_ФАЙЛ_НЕ_НАЙДЕН>"
    model = genai.GenerativeModel(GEMINI_OCR_MODEL_NAME)
    uploaded_file_gemini = None; ocr_text = ""
    try:
        uploaded_file_gemini = genai.upload_file(path=img_path)
        # Для OCR обычно temperature не так критичен, но можно задать для большей стабильности
        generation_config_ocr = genai.types.GenerationConfig(temperature=0.1) 
        response = model.generate_content(
            [GEMINI_OCR_PROMPT, uploaded_file_gemini],
            generation_config=generation_config_ocr
            )
        ocr_text = response.text.strip()
        if not ocr_text: ocr_text = "<ПУСТАЯ_СТРАНИЦА_ОТ_GEMINI>"
    except Exception as e:
        print(f"    Ошибка OCR Gemini для {img_path.name}: {e}"); ocr_text = f"<ОШИБКА_OCR:{str(e)[:50]}>"
        if 'response' in locals() and hasattr(response, 'text'): print(f"    Ответ Gemini при ошибке OCR: {response.text}")
    finally:
        if uploaded_file_gemini and hasattr(uploaded_file_gemini, 'name'):
            try: genai.delete_file(name=uploaded_file_gemini.name)
            except Exception as e_del: print(f"    Не удалось удалить файл Gemini (OCR) {uploaded_file_gemini.name}: {e_del}")
    return ocr_text

@memory.cache
def analyze_ocr_text_gemini(all_pages_text_content: str, source_filename_for_log: str) -> list:
    print(f"  Этап 2 (Анализ): Отправка текста ({len(all_pages_text_content)} симв.) из '{source_filename_for_log}' в Gemini.")
    model = genai.GenerativeModel(GEMINI_ANALYSIS_MODEL_NAME)
    generation_config = genai.types.GenerationConfig(temperature=0.0) # КРИТИЧНО для детерминированности анализа
    
    # Важно: Убеждаемся, что в GEMINI_ANALYSIS_PROMPT_TEMPLATE только ОДИН плейсхолдер {all_pages_text_content}
    # и все остальные фигурные скобки, описывающие JSON, являются ОДИНАРНЫМИ.
    prompt_with_data = GEMINI_ANALYSIS_PROMPT_TEMPLATE.format(all_pages_text_content=all_pages_text_content)
    
    response_data = []
    try:
        response = model.generate_content(prompt_with_data, generation_config=generation_config)
        cleaned_response_text = response.text.strip()
        # ... (остальная логика парсинга JSON и обработки ошибок как была) ...
        if cleaned_response_text.startswith("```json"): cleaned_response_text = cleaned_response_text[7:]
        if cleaned_response_text.endswith("```"): cleaned_response_text = cleaned_response_text[:-3]
        cleaned_response_text = cleaned_response_text.strip()
        if not cleaned_response_text: print(f"    Gemini (Анализ) вернул пустой ответ для {source_filename_for_log}.")
        else:
            try: response_data = json.loads(cleaned_response_text)
            except json.JSONDecodeError as je:
                print(f"    Ошибка декодирования JSON (Анализ) для {source_filename_for_log}: {je}")
                print(f"    Текст ответа Gemini (Анализ): \n---\n{cleaned_response_text}\n---")
        if not isinstance(response_data, list):
            print(f"    Gemini (Анализ) вернул не список для {source_filename_for_log}. Ответ: {response_data}")
            response_data = []
    except Exception as e:
        prompt_feedback = None
        if hasattr(e, 'response') and e.response and hasattr(e.response, 'prompt_feedback'):
            prompt_feedback = e.response.prompt_feedback
        if prompt_feedback and hasattr(prompt_feedback, 'block_reason') and prompt_feedback.block_reason:
            print(f"    Ответ от Gemini (Анализ) для {source_filename_for_log} был заблокирован. Причина: {prompt_feedback.block_reason}")
        elif "429" in str(e) or "ResourceExhausted" in str(e) or "rate limit" in str(e).lower():
            print(f"    Превышен лимит запросов к Gemini (Анализ) для {source_filename_for_log}. Ошибка: {e}")
        else:
            print(f"    Ошибка при взаимодействии с Gemini API (Анализ) для {source_filename_for_log}: {e}")
        if 'response' in locals() and hasattr(response, 'text'):
            print(f"    Полный ответ Gemini (Анализ) (если был): {response.text}")
    return response_data

# --- Основная функция обработки PDF ---
def process_pdf_file(pdf_path: Path, poppler_runtime_path: str | None, total_pdfs: int, current_pdf_num: int):
    print(f"\nОбработка PDF-файла [{current_pdf_num}/{total_pdfs}]: {pdf_path.name} (Размер: {pdf_path.stat().st_size / 1024:.2f} KB)...")
    pdf_temp_image_dir = TEMP_IMAGE_DIR / pdf_path.stem
    pdf_temp_image_dir.mkdir(exist_ok=True, parents=True)

    pil_images_objects = []
    pages_count_from_pdfinfo = 0
    try:
        pdf_info = pdfinfo_from_path(pdf_path, poppler_path=poppler_runtime_path, timeout=60)
        pages_count_from_pdfinfo = pdf_info.get("Pages", 0)
        print(f"  PDFInfo: Обнаружено {pages_count_from_pdfinfo} страниц.")
        if pages_count_from_pdfinfo == 0:
             print(f"    ПРЕДУПРЕЖДЕНИЕ: pdfinfo не вернуло количество страниц для {pdf_path.name}.")

        print(f"  Конвертация {pdf_path.name} в изображения (DPI={TARGET_DPI})...")
        pil_images_objects = convert_from_path(pdf_path, dpi=TARGET_DPI, thread_count=4, fmt='png', 
                                               poppler_path=poppler_runtime_path, timeout=300)
        if not pil_images_objects:
            print(f"    Не удалось получить PIL изображения из {pdf_path.name} после конвертации.")
            if pdf_temp_image_dir.exists(): shutil.rmtree(pdf_temp_image_dir, ignore_errors=True)
            return
    except Exception as e:
        print(f"    Ошибка на этапе pdfinfo или конвертации PDF для {pdf_path.name}: {e}")
        if pdf_temp_image_dir.exists(): shutil.rmtree(pdf_temp_image_dir, ignore_errors=True)
        return

    all_pages_ocr_data = []
    display_total_pages = pages_count_from_pdfinfo if pages_count_from_pdfinfo > 0 else len(pil_images_objects)

    for i, image_obj in enumerate(pil_images_objects):
        page_num_actual = i + 1
        img_filename = f"{pdf_path.stem}_page_{page_num_actual}.png"
        img_save_path = pdf_temp_image_dir / img_filename
        try:
            image_obj.save(img_save_path, "PNG")
            # ... (проверка размера PNG, если нужно) ...
            image_hash = get_file_hash(img_save_path)
            ocr_text = get_ocr_text_from_image_gemini(str(img_save_path), image_hash)
            all_pages_ocr_data.append({'page_num': page_num_actual, 'text': ocr_text})
        except Exception as e_save_ocr:
            print(f"    Ошибка сохранения/OCR для стр. {page_num_actual}: {e_save_ocr}")
            all_pages_ocr_data.append({'page_num': page_num_actual, 'text': f"<ОШИБКА_ОБРАБОТКИ_СТРАНИЦЫ_{page_num_actual}>"})
        finally:
            if image_obj: image_obj.close()
    
    if pdf_temp_image_dir.exists(): # Очищаем временные изображения PDF сразу после OCR
        try: shutil.rmtree(pdf_temp_image_dir)
        except Exception as e_clean_img: print(f"    Ошибка удаления {pdf_temp_image_dir}: {e_clean_img}")

    if all_pages_ocr_data:
        now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Используем TXT_OUTPUT_DIR, которая определена глобально
        txt_filename = TXT_OUTPUT_DIR / f"{pdf_path.stem}_{now_str}.txt" 
        try:
            with open(txt_filename, "w", encoding="utf-8") as f_txt:
                f_txt.write(f"--- OCR для файла: {pdf_path.name} ({display_total_pages} стр. в PDF) ---\n")
                for ocr_item in all_pages_ocr_data:
                    f_txt.write(f"\n******* Страница {ocr_item['page_num']}/{display_total_pages} *******\n")
                    f_txt.write(ocr_item['text'] + "\n")
            print(f"  Весь OCR текст (из PDF) сохранен в: {txt_filename}")
        except Exception as e_txt:
            print(f"    Ошибка сохранения OCR текста в TXT файл {txt_filename}: {e_txt}")
            
    # Сохранение OCR текста в TXT файл
    now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    txt_filename = GENERATED_TXT_OUTPUT_DIR / f"{pdf_path.stem}_{now_str}.txt"
    try:
        with open(txt_filename, "w", encoding="utf-8") as f_txt:
            f_txt.write(f"--- OCR для файла: {pdf_path.name} ({display_total_pages} стр. в PDF) ---\n")
            for ocr_item in all_pages_ocr_data:
                f_txt.write(f"\n******* Страница {ocr_item['page_num']}/{display_total_pages} *******\n")
                f_txt.write(ocr_item['text'] + "\n")
        print(f"  Весь OCR текст (из PDF) сохранен в: {txt_filename}")
    except Exception as e_txt:
        print(f"    Ошибка сохранения OCR текста в TXT файл {txt_filename}: {e_txt}")
    
    # Этап 2: Анализ
    formatted_text_for_analysis = []
    for ocr_item in all_pages_ocr_data:
        formatted_text_for_analysis.append(
            f"--- СТРАНИЦА {ocr_item['page_num']} ---\n{ocr_item['text']}\n--- КОНЕЦ СТРАНИЦЫ {ocr_item['page_num']} ---"
        )
    full_text_blob_for_analysis = "\n\n".join(formatted_text_for_analysis)
    
    extracted_documents_data = analyze_ocr_text_gemini(full_text_blob_for_analysis, pdf_path.name)

    if extracted_documents_data:
        for doc_data in extracted_documents_data:
            # ... (логика сохранения JSON и вывода reasoning как была) ...
            if not isinstance(doc_data, dict): continue
            reasoning = doc_data.get("page_assignment_reasoning", "Обоснование не предоставлено.")
            doc_id_for_log = f"{doc_data.get('document_type', '?')}_{doc_data.get('document_number', '?')}"
            print(f"    Документ {doc_id_for_log}: Обоснование страниц {doc_data.get('pages_original_order')}: \"{reasoning}\"")
            suggested_filename_raw = doc_data.get("file_name_suggestion")
            if not suggested_filename_raw:
                doc_type = doc_data.get("document_type", "UNKNOWN_TYPE")
                doc_num = doc_data.get("document_number", "UNKNOWN_NUM")
                safe_doc_num = "".join(c if c.isalnum() or c in ('-', '_') else '_' for c in str(doc_num))
                suggested_filename_raw = f"{doc_type}_{safe_doc_num}_{pdf_path.stem}.json"
            final_filename = "".join(c for c in suggested_filename_raw if c.isalnum() or c in ('.', '-', '_'))
            if not final_filename.endswith(".json"): final_filename += ".json"
            output_file_path = JSON_OUTPUT_DIR / final_filename
            try:
                with open(output_file_path, "w", encoding="utf-8") as f_json:
                    json.dump(doc_data, f_json, ensure_ascii=False, indent=4)
                print(f"  Сохранен результат: {output_file_path}")
            except Exception as e_save_json: print(f"    Ошибка сохранения JSON для {final_filename}: {e_save_json}")
    else:
        print(f"  Не удалось извлечь структурированные данные из {pdf_path.name} после анализа (пустой список).")

# --- Основной блок ---
if __name__ == "__main__":
    if not VALID_POPPLER_PATH and not shutil.which("pdfinfo.exe" if os.name == 'nt' else "pdfinfo"):
        sys.exit(1)

    # Обрабатываем ТОЛЬКО PDF файлы из входной директории
    pdf_files_to_process = sorted(list(PDF_INPUT_DIR.glob("*.pdf")))
    total_pdfs_count = len(pdf_files_to_process)

    if not pdf_files_to_process:
        print(f"Не найдены PDF файлы в директории: {PDF_INPUT_DIR}")
    else:
        print(f"Найдено {total_pdfs_count} PDF файлов для обработки из {PDF_INPUT_DIR}.")
        for i, pdf_file_path in enumerate(pdf_files_to_process):
            process_pdf_file(pdf_file_path, VALID_POPPLER_PATH, total_pdfs_count, i + 1) # Изменил имя функции
            if i < total_pdfs_count - 1 :
                 time.sleep(1)

    print("\n--- Обработка всех PDF файлов завершена. ---")