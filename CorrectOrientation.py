import cv2
import numpy as np
from pdf2image import convert_from_path, pdfinfo_from_path
import matplotlib.pyplot as plt  # Keep for potential debugging/visualization
from PIL import Image

# import io # Not used
# import math # Not used
from skimage.transform import hough_line, hough_line_peaks
import os
import warnings

# Suppress specific warnings if needed (e.g., from skimage)
warnings.filterwarnings("ignore", category=UserWarning, module="skimage")
warnings.filterwarnings("ignore", category=FutureWarning, module="skimage")


def detect_skew_hough(image, debug_image_path=None):
    """
    Detect skew angle using Hough Line Transform for more accuracy.
    Better at detecting text line angles.
    Args:
        image (np.ndarray): Input image (BGR or Grayscale).
        debug_image_path (str, optional): Path to save intermediate debug images. Defaults to None.
    Returns:
        float: Detected skew angle in degrees, or 0.0 if detection fails.
    """
    # 1. Convert to grayscale
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()  # Avoid modifying original if already grayscale

    # 2. Improve contrast (Optional but can help)
    # gray = cv2.equalizeHist(gray) # Can sometimes worsen things, use with caution
    # clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    # gray = clahe.apply(gray)

    # 3. Invert and Threshold
    # Using adaptive thresholding might be more robust than Otsu sometimes
    # _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    binary = cv2.adaptiveThreshold(
        cv2.bitwise_not(gray),  # Invert first
        255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY,
        15,  # Block size - experiment with this
        -2,  # Constant subtracted - experiment with this
    )

    if debug_image_path:
        cv2.imwrite(debug_image_path.replace(".jpg", "_01_binary.jpg"), binary)

    # 4. Morphological Closing to connect text horizontally
    # Kernel size might need tuning based on font size and spacing
    kernel_height = 3
    kernel_width = 25  # Increased width might help connect words better
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (kernel_width, kernel_height))
    connected = cv2.morphologyEx(
        binary, cv2.MORPH_CLOSE, kernel, iterations=1
    )  # iterations=1 is usually enough

    if debug_image_path:
        cv2.imwrite(debug_image_path.replace(".jpg", "_02_connected.jpg"), connected)

    # Optional: Find contours and filter small ones (noise reduction)
    # contours, _ = cv2.findContours(connected, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
    # mask = np.zeros_like(binary)
    # min_area = 50 # Adjust as needed
    # for cnt in contours:
    #     if cv2.contourArea(cnt) > min_area:
    #         cv2.drawContours(mask, [cnt], -1, (255), -1)
    # connected = cv2.bitwise_and(connected, connected, mask=mask)
    # if debug_image_path:
    #     cv2.imwrite(debug_image_path.replace(".jpg", "_03_filtered.jpg"), connected)

    # Check if image is mostly blank after processing
    if cv2.countNonZero(connected) < 0.01 * connected.shape[0] * connected.shape[1]:
        print(
            "   [Warning] Very few non-zero pixels for Hough transform, might fail or use fallback."
        )
        # Optionally return fallback immediately: return detect_skew_fallback(image, debug_image_path)

    # 5. Perform Hough Transform
    # Tested points are white on black background
    # Use a finer angle resolution if needed
    tested_angles = np.linspace(
        -np.pi / 2, np.pi / 2, 360, endpoint=False
    )  # Check angles from -90 to +90
    h, theta, d = hough_line(connected, theta=tested_angles)

    # 6. Find Peaks in Hough Space
    # Adjust num_peaks and threshold based on expected line density
    try:
        hspace, angles_rad, dists = hough_line_peaks(
            h, theta, d, num_peaks=20, threshold=0.3 * np.max(h)
        )  # Use relative threshold
    except Exception as e:
        print(f"   [Error] Hough peak finding failed: {e}. Using fallback.")
        return detect_skew_fallback(image, debug_image_path)

    if angles_rad is None or len(angles_rad) == 0:
        print("   [Info] No prominent lines found via Hough. Using fallback.")
        return detect_skew_fallback(image, debug_image_path)

    # 7. Filter and Analyze Angles
    angles_deg = np.degrees(angles_rad)
    valid_angles = []
    angle_range = 45  # Consider angles within +/- 45 degrees of horizontal/vertical
    for angle in angles_deg:
        # Normalize angle to be between -90 and +90
        current_angle = angle % 180
        if current_angle > 90:
            current_angle -= 180  # Map (90, 180] to (-90, 0]

        # We are interested in lines close to horizontal (0 degrees)
        if abs(current_angle) < angle_range:
            valid_angles.append(current_angle)
        # Also consider lines close to vertical (90 degrees) if needed, but usually text skew aligns horizontally
        # elif abs(abs(current_angle) - 90) < angle_range:
        #     valid_angles.append(current_angle - np.sign(current_angle) * 90) # Adjust vertical lines relative to horizontal

    if not valid_angles:
        print(
            "   [Info] No angles within the desired range found after Hough. Using fallback."
        )
        return detect_skew_fallback(image, debug_image_path)

    # 8. Return Median Angle (more robust to outliers than mean)
    median_angle = np.median(valid_angles)
    print(f"   Hough detected angles (deg): {[f'{a:.2f}' for a in valid_angles]}")
    print(f"   Hough median angle: {median_angle:.2f}°")
    return median_angle


def detect_skew_fallback(image, debug_image_path=None):
    """
    Fallback skew detection method using minimum area rectangle.
    More sensitive to the overall content block shape.
    Args:
        image (np.ndarray): Input image (BGR or Grayscale).
        debug_image_path (str, optional): Path to save intermediate debug images. Defaults to None.
    Returns:
        float: Detected skew angle in degrees, or 0.0 if detection fails.
    """
    print("   [Info] Using fallback skew detection (minAreaRect).")
    # 1. Convert to grayscale
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    else:
        gray = image.copy()

    # 2. Invert and Threshold (Otsu is fine here)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    if debug_image_path:
        cv2.imwrite(debug_image_path.replace(".jpg", "_F01_binary.jpg"), binary)

    # 3. Find coordinates of all non-zero pixels
    # Note: np.where returns (row, col), cv2 expects (col, row) or (x, y)
    coords = np.column_stack(np.where(binary > 0))  # shape: (N, 2), format: (row, col)

    if len(coords) < 50:  # Need a minimum number of points
        print(
            "   [Warning] Fallback: Too few points found to determine skew reliable. Returning 0."
        )
        return 0.0

    # 4. Find the minimum area rectangle
    # cv2.minAreaRect expects points in (x, y) format, so swap columns
    # Also needs float32
    rect = cv2.minAreaRect(coords[:, ::-1].astype(np.float32))
    # rect = ((center_x, center_y), (width, height), angle)
    # Angle is degrees, range [-90, 0). Angle is between the horizontal axis and the box's first side (width).

    # 5. Extract and adjust the angle
    angle = rect[2]
    box_width, box_height = rect[1]

    # OpenCV's angle logic for minAreaRect:
    # - angle is from horizontal x-axis to the 'width' side.
    # - angle is in [-90, 0).
    # - 'width' is the side encountered first when rotating counter-clockwise from positive x-axis.
    # - Therefore, if height > width, the box is "standing up", and the angle is near -90.
    # - If width > height, the box is "lying down", and the angle is near 0.

    # We want the angle of the *text lines* (usually aligned with the longer side if it's a text block).
    if box_width < box_height:  # Box is taller than wide (standing up)
        # The angle `rect[2]` is the angle of the shorter side (width) relative to horizontal.
        # The angle of the longer side (text lines) is 90 degrees away from this.
        skew_angle = angle + 90
    else:  # Box is wider than tall (lying down)
        # The angle `rect[2]` is the angle of the longer side (width) relative to horizontal.
        # This is likely the text skew angle.
        skew_angle = angle

    # Ensure the final angle is within a reasonable range, e.g., -45 to 45
    # The above logic should already place it close to 0 if text is near horizontal.
    # Let's refine the typical adjustment:
    # if angle < -45.0:
    #     angle = angle + 90.0 # Adjust for vertical orientation

    print(
        f"   Fallback minAreaRect raw angle: {rect[2]:.2f}°, (w,h): ({box_width:.0f}, {box_height:.0f}), calculated skew: {skew_angle:.2f}°"
    )

    # Limit angle magnitude to prevent extreme corrections
    max_angle = 45
    if abs(skew_angle) > max_angle:
        print(
            f"   [Warning] Fallback angle {skew_angle:.2f}° exceeds limit {max_angle}°, capping."
        )
        # Decide whether to cap or return 0. Capping might be better.
        # skew_angle = np.sign(skew_angle) * max_angle
        # Or maybe trust Hough more if fallback gives extreme angle? For now, return the calculated angle.
        pass  # Don't cap yet, just report

    return skew_angle


def correct_skew(image, angle, background_color=(255, 255, 255)):
    """
    Rotate the image to correct the skew.
    Args:
        image (np.ndarray): Input image (BGR or Grayscale).
        angle (float): Skew angle in degrees. Negative angle means clockwise rotation needed.
        background_color (tuple): Color for border filling (BGR). Defaults to white.
    Returns:
        np.ndarray: Rotated image.
    """
    if abs(angle) < 0.01:  # Don't rotate if angle is negligible
        return image.copy()

    h, w = image.shape[:2]
    center = (w // 2, h // 2)

    # Get rotation matrix (angle is counter-clockwise)
    # Our detected 'angle' is the orientation of the text.
    # To make it horizontal, we need to rotate by '-angle'.
    rotation_matrix = cv2.getRotationMatrix2D(
        center, angle, 1.0
    )  # Corrected: Pass angle directly

    # Perform the rotation
    # Use BORDER_CONSTANT and fill with background color (usually white for documents)
    rotated = cv2.warpAffine(
        image,
        rotation_matrix,
        (w, h),
        flags=cv2.INTER_CUBIC,  # Higher quality interpolation
        borderMode=cv2.BORDER_CONSTANT,
        borderValue=background_color,
    )

    return rotated


def process_pdf(
    pdf_path,
    output_dir=None,
    use_hough=True,
    dpi=300,
    save_comparison=True,
    save_debug_images=False,
):
    """
    Process a PDF file to detect and correct skew.

    Parameters:
    - pdf_path: Path to the input PDF file
    - output_dir: Directory to save the corrected images and optional comparisons/debug images
    - use_hough: Whether to use Hough transform (True) or fallback (False) as primary
    - dpi: Resolution for rendering PDF pages
    - save_comparison: Whether to save side-by-side comparison images
    - save_debug_images: Whether to save intermediate images from the detection process

    Returns:
    - corrected_images: List of corrected images (numpy arrays in BGR format)
    - angles: List of detected angles
    """
    try:
        pdf_info = pdfinfo_from_path(pdf_path, userpw=None, poppler_path=None)
        num_pages = pdf_info["Pages"]
        print(f"Processing PDF: {pdf_path} ({num_pages} pages)")
    except Exception as e:
        print(f"Error getting PDF info: {e}")
        return [], []

    # Convert PDF to images
    try:
        images = convert_from_path(pdf_path, dpi=dpi)
    except Exception as e:
        print(f"Error converting PDF to images: {e}")
        return [], []

    # Create output directory if it doesn't exist
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        if save_debug_images:
            os.makedirs(os.path.join(output_dir, "debug"), exist_ok=True)

    corrected_images_list = []
    detected_angles = []

    for i, pil_image in enumerate(images):
        page_num = i + 1
        print(f"\nProcessing Page {page_num}/{num_pages}...")

        # Convert PIL Image (RGB) to numpy array (BGR for OpenCV)
        img_np_bgr = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

        debug_path = None
        if output_dir and save_debug_images:
            debug_path = os.path.join(output_dir, "debug", f"page_{page_num}_debug.jpg")

        # Detect skew angle
        if use_hough:
            angle = detect_skew_hough(img_np_bgr, debug_path)
        else:
            angle = detect_skew_fallback(img_np_bgr, debug_path)

        print(f" Page {page_num}: Detected skew angle = {angle:.2f}°")
        detected_angles.append(angle)

        # Correct skew
        # Determine background color (simple check: check corners)
        # More robust methods exist, but this is often good enough
        h, w = img_np_bgr.shape[:2]
        corners = [
            img_np_bgr[0, 0],
            img_np_bgr[0, w - 1],
            img_np_bgr[h - 1, 0],
            img_np_bgr[h - 1, w - 1],
        ]
        # Assuming background is dominant color in corners, find most frequent color
        corner_colors_tuple = [tuple(c) for c in corners]
        most_common_corner = max(
            set(corner_colors_tuple), key=corner_colors_tuple.count
        )
        bg_color = (
            most_common_corner if most_common_corner else (255, 255, 255)
        )  # Default white

        corrected_bgr = correct_skew(img_np_bgr, angle, background_color=bg_color)
        corrected_images_list.append(corrected_bgr)  # Store BGR for consistency

        # Save corrected image if output directory is provided
        if output_dir:
            output_file = os.path.join(output_dir, f"page_{page_num}_corrected.jpg")
            cv2.imwrite(output_file, corrected_bgr)
            # print(f" Saved corrected image to {output_file}") # Reduce verbosity

            # Save before/after comparison
            if save_comparison:
                # Ensure both images have same height for hstack
                h1, w1 = img_np_bgr.shape[:2]
                h2, w2 = corrected_bgr.shape[:2]
                max_h = max(h1, h2)
                img1_resized = cv2.resize(img_np_bgr, (int(w1 * max_h / h1), max_h))
                img2_resized = cv2.resize(corrected_bgr, (int(w2 * max_h / h2), max_h))

                comparison = np.hstack((img1_resized, img2_resized))
                comparison_file = os.path.join(
                    output_dir, f"page_{page_num}_comparison.jpg"
                )
                cv2.imwrite(comparison_file, comparison)

    if detected_angles:
        avg_angle = sum(detected_angles) / len(detected_angles)
        print(f"\nAverage detected skew angle across all pages: {avg_angle:.2f}°")
    else:
        print("\nNo angles detected.")

    return corrected_images_list, detected_angles


def create_corrected_pdf(corrected_images_bgr, output_pdf):
    """
    Create a PDF from the corrected images (provided as BGR numpy arrays).
    """
    if not corrected_images_bgr:
        print("No corrected images to save!")
        return False

    pil_images = []
    for i, img_bgr in enumerate(corrected_images_bgr):
        try:
            # Convert BGR (OpenCV) back to RGB (PIL)
            img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
            pil_images.append(Image.fromarray(img_rgb))
        except Exception as e:
            print(f"Error converting page {i+1} from numpy array to PIL Image: {e}")
            continue  # Skip this page

    if not pil_images:
        print("Conversion to PIL images failed for all pages.")
        return False

    # Save the first image, append the rest
    try:
        pil_images[0].save(
            output_pdf,
            "PDF",  # Specify format for clarity
            resolution=100.0,  # Standard PDF resolution unit
            save_all=True,
            append_images=pil_images[1:] if len(pil_images) > 1 else [],
        )
        print(f"\nSuccessfully saved corrected PDF to {output_pdf}")
        return True
    except Exception as e:
        print(f"\nError saving corrected PDF: {e}")
        return False


def correct_pdf_skew(
    input_pdf,
    output_pdf=None,
    output_dir=None,
    use_hough=True,
    save_comparison=True,
    save_debug_images=False,  # Added option
    dpi=300,  # Added option
):
    """
    Main function to correct a PDF's skew.

    Parameters:
    - input_pdf: Path to the input PDF file
    - output_pdf: Path for the output corrected PDF
    - output_dir: Directory to save individual page images (corrected, optionally comparison/debug)
    - use_hough: Whether to use Hough transform for angle detection
    - save_comparison: Whether to save before/after comparison images
    - save_debug_images: Whether to save intermediate images from detection process
    - dpi: Resolution for PDF page rendering

    Returns:
    - angles: List of detected angles
    """
    if not os.path.exists(input_pdf):
        print(f"Error: Input PDF not found at {input_pdf}")
        return None

    # Set default output paths if not provided
    base_name = os.path.splitext(os.path.basename(input_pdf))[0]
    input_dir = os.path.dirname(input_pdf)

    if output_pdf is None:
        output_pdf = os.path.join(input_dir, f"{base_name}_corrected.pdf")

    if output_dir is None:
        # Create output dir next to input pdf by default
        output_dir = os.path.join(input_dir, f"{base_name}_corrected_pages")

    # Process the PDF
    corrected_images, angles = process_pdf(
        input_pdf,
        output_dir,
        use_hough,
        dpi=dpi,
        save_comparison=save_comparison,
        save_debug_images=save_debug_images,
    )

    # Create corrected PDF
    if corrected_images:
        create_corrected_pdf(corrected_images, output_pdf)
    else:
        print("PDF Correction failed: No images were processed.")
        return None

    # Display results summary
    if angles:
        print("\n--- Skew Correction Summary ---")
        print(f"Input PDF: {input_pdf}")
        print(f"Output PDF: {output_pdf}")
        if output_dir:
            print(f"Output Pages Dir: {output_dir}")
        print(f"Number of pages processed: {len(angles)}")
        print(f"Detected angles (°): {[f'{angle:.2f}' for angle in angles]}")
        print(f"Average angle (°): {sum(angles)/len(angles):.2f}")
        print("------------------------------")
    else:
        print("\n--- Skew Correction Summary ---")
        print("No angles could be detected.")
        print("------------------------------")

    return angles


if __name__ == "__main__":
    # --- Configuration ---
    # Use raw string (r"...") or forward slashes for Windows paths
    INPUT_PDF_PATH = (
        r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon.pdf"
    )
    OUTPUT_PDF_PATH = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon_corrected.pdf"  # Save next to original
    OUTPUT_PAGES_DIR = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanForTreaningNaklon_corrected_pages"  # Save pages in a subfolder

    # --- Execution ---
    angles_detected = correct_pdf_skew(
        input_pdf=INPUT_PDF_PATH,
        output_pdf=OUTPUT_PDF_PATH,
        output_dir=OUTPUT_PAGES_DIR,
        use_hough=True,  # Try True first (Hough is often better for text)
        save_comparison=True,  # Save side-by-side images
        save_debug_images=False,  # Set to True to see intermediate steps if it fails
        dpi=300,  # Use good resolution
    )
