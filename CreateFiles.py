import re
import os


def sanitize_filename(name):
    """
    Очищает имя файла от недопустимых символов.
    
    Args:
        name (str): Исходное имя файла
        
    Returns:
        str: Очищенное имя файла
    """
    # Заменяем недопустимые символы на подчеркивания
    # Недопустимые символы в Windows: \ / : * ? " < > |
    sanitized = re.sub(r'[\\/:*?"<>|]', '_', name)
    
    # Убираем точки и пробелы в конце, так как Windows не позволяет 
    # файлам заканчиваться точкой или пробелом
    sanitized = sanitized.rstrip('. ')
    
    # Ограничиваем длину имени файла (обычно 255 символов)
    if len(sanitized) > 250:  # Оставляем немного места на расширение
        sanitized = sanitized[:250]
        
    return sanitized