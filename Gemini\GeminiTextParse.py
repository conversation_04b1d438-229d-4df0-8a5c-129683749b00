# -*- coding: utf-8 -*-
import google.generativeai as genai
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import time
import hashlib
import json
from datetime import datetime


# --- Загрузка переменных окружения ---
load_dotenv()

# --- Очистка консоли (опционально) ---
if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")

# --- Настройки API и Путей ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY_AHMED')
if not GEMINI_API_KEY:
    # Используем raise, как в оригинале
    raise ValueError("Необходимо установить переменную окружения GEMINI_API_KEY.")

""" https://ai.google.dev/gemini-api/docs/models#previous-experimental-models
*** "gemini-2.0-pro-exp-02-05"  классно работает, но лимиты
*** gemini-2.5-pro-preview-03-25 попробовать
*** "gemini-2.5-flash-preview-04-17"
*** gemini-2.5-pro-exp-03-25; 1,048,576/65,536
    RPM 5; RPD 25
    250,000 TPM
    1,000,000 TPD
*** gemini-2.5-flash-preview-04-17; хорошо, но мало токенов
    RPM 10; RPD 500
    250,000 TPM
*** gemini-2.0-flash; 1,048,576/8,192
    RPM 15; RPD 1500
    1,000,000 TPM
*** Gemini 2.0 Flash Experimental
    RPM 10; RPD 1000
    1,000,000 TPM
*** gemini-1.5-flash; 1,048,576/8,192
    RPM 15; RPD 1500
    1,000,000 TPM
"""


# Имя модели из переменной окружения или значение по умолчанию
# Логика выбора модели оставлена точно как в оригинале
MODEL_NAME_ENV_VAR =  "models/gemini-2.5-pro-preview-03-25" # "gemini-2.0-pro-exp-02-05"  # os.getenv('GEMINI_MODEL_2F')  # GEMINI_MODEL_2F
if MODEL_NAME_ENV_VAR:
    MODEL_NAME = MODEL_NAME_ENV_VAR
    print(f"{datetime.now().strftime('%d.%m.%Y %H:%M:%S')} | INFO | Используется модель Gemini из переменной окружения GEMINI_MODEL_2F: {MODEL_NAME}")
else:
    MODEL_NAME = os.getenv('GEMINI_MODEL_2F') # Значение по умолчанию 'gemini-1.5-flash-latest'
    print(f"{datetime.now().strftime('%d.%m.%Y %H:%M:%S')} | INFO | Переменная окружения GEMINI_MODEL_2F не найдена. Используется модель Gemini по умолчанию: {MODEL_NAME}")
MODEL_NAME_DEFAULT = 'GEMINI_MODEL_2F'  # Рекомендуемая актуальная модель
MODEL_NAME = MODEL_NAME_ENV_VAR # Эта строка и следующие две были в оригинале, оставляем
if not MODEL_NAME:
    MODEL_NAME = MODEL_NAME_DEFAULT
    print(f"Info: Environment variable '{MODEL_NAME_ENV_VAR}' not set or empty. Using default model: '{MODEL_NAME}'",
          file=sys.stderr)
else:
    # Эта строка тоже была в оригинале
    print(f"Info: Using model from environment variable '{MODEL_NAME_ENV_VAR}': '{MODEL_NAME}'")

GENERATION_TEMPERATURE = 0.1

# --- Настройки кеширования (логика оставлена без изменений) ---
USE_CACHE = True
CACHE_DIR = Path('..') / '.gemini_cache_text'
CACHE_DIR.mkdir(exist_ok=True)
CACHE_FILE_PATH = CACHE_DIR / 'text_processing_cache.json'


def load_cache():
    if not USE_CACHE:
        return {}
    if CACHE_FILE_PATH.exists():
        try:
            with open(CACHE_FILE_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, Exception) as e:
            print(
                f"Warning: Cache file {CACHE_FILE_PATH} is corrupted or unreadable ({e}). Starting with an empty cache.",
                file=sys.stderr)
            return {}
    return {}


def save_cache(cache_data):
    if not USE_CACHE:
        return
    try:
        with open(CACHE_FILE_PATH, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Warning: Could not save cache: {e}", file=sys.stderr)


processing_cache = load_cache()


def calculate_file_and_prompt_hash(file_path_str: str, prompt_text: str):
    hasher = hashlib.md5()
    try:
        with open(file_path_str, 'rb') as f:
            while chunk := f.read(8192):
                hasher.update(chunk)
        hasher.update(prompt_text.encode('utf-8'))
        return hasher.hexdigest()
    except FileNotFoundError:
        print(f"Error: File not found for hashing: {file_path_str}", file=sys.stderr)
        return None
    except Exception as e:
        print(f"Error calculating hash for {file_path_str}: {e}", file=sys.stderr)
        return None


def clean_gemini_json_output(text: str) -> str:
    text = text.strip()
    if text.startswith("```json") and text.endswith("```"):
        return text[7:-3].strip()
    if text.startswith("```") and text.endswith("```"):
        return text[3:-3].strip()
    return text


# ИЗМЕНЕНИЕ №1: Функция теперь возвращает кортеж (результат, usage_metadata или None)
def process_text_file_with_gemini(text_file_path_str: str, my_prompt: str, force_refresh: bool = False):
    if not my_prompt or not isinstance(my_prompt, str):
        print("Error: Prompt is missing, empty, or not a string.", file=sys.stderr)
        # Возвращаем None для метаданных
        return {"file_name": Path(text_file_path_str).name, "error": "Invalid prompt provided.", "processed_data": None}, None

    text_file_path = Path(text_file_path_str)
    file_name = text_file_path.name

    if not text_file_path.exists():
        print(f"Error: File not found: {text_file_path_str}", file=sys.stderr)
        # Возвращаем None для метаданных
        return {"file_name": file_name, "error": "File not found", "processed_data": None}, None

    # Логика кеширования оставлена без изменений, КРОМЕ возвращаемого значения
    combined_hash = calculate_file_and_prompt_hash(str(text_file_path), my_prompt)
    if not combined_hash:
        # Возвращаем None для метаданных
        return {"file_name": file_name, "error": "Could not calculate cache hash.", "processed_data": None}, None

    # Ключ кеша НЕ включает температуру в оригинале, оставляем так
    cache_key = combined_hash # f"{combined_hash}_temp{GENERATION_TEMPERATURE}" - убрано, т.к. не было в оригинале

    if USE_CACHE and not force_refresh and cache_key in processing_cache:
        # В оригинале не было вывода температуры в сообщении Cache hit, оставляем так
        print(f"  Cache hit for: {file_name} (Prompt specific)") # Убрано Temp
        try:
            cached_item = processing_cache[cache_key]
            if isinstance(cached_item, dict):
                 # ВАЖНО: В оригинальном коде кеш НЕ хранил метаданные.
                 # Чтобы минимально изменить код, при попадании в кеш мы НЕ СМОЖЕМ вернуть метаданные.
                 # Возвращаем None для метаданных, т.к. их нет в кеше оригинального формата.
                 return cached_item, None
            else:
                print(f"  Warning: Cached data for {file_name} has unexpected type '{type(cached_item)}'. Refetching.", file=sys.stderr)
        except Exception as e:
            print(f"  Warning: Error reading from cache for {file_name} ({e}). Refetching.", file=sys.stderr)

    if USE_CACHE and force_refresh:
        print(f"  Force refresh requested for: {file_name}. Ignoring cache for reading.")
    elif not USE_CACHE:
        print(f"  Cache is disabled. Processing API call for {file_name}.")

    # Вывод температуры в этом сообщении был в оригинале
    print(f"  Processing file: {file_name} with Gemini API (Model: '{MODEL_NAME}', Temperature: {GENERATION_TEMPERATURE})...")

    uploaded_file = None
    # ИЗМЕНЕНИЕ №2: Переменная для хранения метаданных
    usage_metadata = None

    try:
        print(f"    Uploading file '{file_name}'...")
        uploaded_file = genai.upload_file(path=str(text_file_path), display_name=file_name)
        print(f"    File '{uploaded_file.name}' uploaded successfully.")
        time.sleep(1)

        prompt_parts = [my_prompt, uploaded_file]

        model = genai.GenerativeModel(MODEL_NAME)
        generation_config = genai.types.GenerationConfig(
            response_mime_type="application/json",
            temperature=GENERATION_TEMPERATURE
        )

        print(f"    Sending request to Gemini for {file_name}...")
        response = model.generate_content(prompt_parts, generation_config=generation_config)

        # ИЗМЕНЕНИЕ №3: Извлекаем метаданные, если они есть
        if hasattr(response, 'usage_metadata'):
            usage_metadata = response.usage_metadata
        # Печатать токены здесь НЕ НУЖНО, сделаем это в конце основного скрипта

        # Остальная логика обработки ответа оставлена без изменений
        if not response.parts:
            error_detail = "No parts in response."
            if hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
                error_detail = f"Blocked. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}"
            print(f"    Error from Gemini for {file_name}: {error_detail}", file=sys.stderr)
            # Возвращаем извлеченные метаданные (могут быть None)
            return {"file_name": file_name, "error": "Gemini API request failed or was blocked.",
                    "details": error_detail, "processed_data": None}, usage_metadata

        raw_json_text = response.text.strip()
        print(f"    Raw response from Gemini (first 200 chars): '{raw_json_text[:200]}...'")
        cleaned_json_text = clean_gemini_json_output(raw_json_text)
        print(f"    Cleaned text for JSON parsing (first 200 chars): '{cleaned_json_text[:200]}...'")

        try:
            result_json = json.loads(cleaned_json_text)
            # Логика обработки list/dict оставлена без изменений
            if isinstance(result_json, list):
                final_data_to_return = {"file_name": file_name, "analyzed_pages": result_json, "error": None}
            elif isinstance(result_json, dict):
                if "file_name" not in result_json: result_json["file_name"] = file_name
                # В оригинале не было добавления "error": None, оставляем так
                final_data_to_return = result_json
            else:
                print(f"    Error: Parsed JSON is not a list or a dict. Type: {type(result_json)}", file=sys.stderr)
                raise json.JSONDecodeError(f"Parsed JSON is not a list or dict, but {type(result_json)}", cleaned_json_text, 0)

            print(f"    Successfully parsed JSON response for {file_name}.")
            if USE_CACHE:
                # ВАЖНО: Сохраняем в кеш ТОЛЬКО результат, как в оригинале.
                # Метаданные не сохраняются в кеш этим кодом.
                processing_cache[cache_key] = final_data_to_return
                save_cache(processing_cache)
            # Возвращаем результат и извлеченные метаданные
            return final_data_to_return, usage_metadata

        except json.JSONDecodeError as e_json:
            print(f"    Error: Gemini response was not valid JSON for {file_name}. Parser error: {e_json}. Raw response (first 200 chars): '{raw_json_text[:200]}...'", file=sys.stderr)
            # Закомментированный fallback оставлен как был
            # ... (fallback code commented out in original) ...

            error_data = {"file_name": file_name, "error": "Response was not valid JSON.",
                          "parser_error": str(e_json), "raw_response": raw_json_text, "processed_data": None}
            if USE_CACHE:
                # Сохраняем в кеш ТОЛЬКО результат ошибки, как в оригинале
                processing_cache[cache_key] = error_data
                save_cache(processing_cache)
            # Возвращаем ошибку и извлеченные метаданные (могут быть None)
            return error_data, usage_metadata # ИСПРАВЛЕНО в оригинале: возвращаем словарь, а не строку

        # Блок вывода токенов из оригинала УДАЛЕН отсюда, т.к. перенесен в конец скрипта
        # if hasattr(response, 'usage_metadata'): ... - УДАЛЕНО

    except Exception as e:
        print(f"    An error occurred while processing {file_name} with Gemini: {type(e).__name__} - {e}", file=sys.stderr)
        # Возвращаем ошибку и метаданные (которые могли быть None)
        return {"file_name": file_name, "error": f"Unhandled API/processing error: {type(e).__name__} - {str(e)}",
                "processed_data": None}, usage_metadata
    finally:
        if uploaded_file:
            try:
                print(f"Deleting uploaded file '{uploaded_file.name}'...")
                genai.delete_file(uploaded_file.name)
                print(f"File '{uploaded_file.name}' deleted.")
            except Exception as e_del:
                print(f"    Warning: Failed to delete uploaded file '{uploaded_file.name}': {e_del}", file=sys.stderr)


# --- Основная часть скрипта ---
if __name__ == "__main__":
    import fitz  # PyMuPDF
    genai.configure(api_key=GEMINI_API_KEY)

    # Загрузка промпта как в оригинале
    try:
        from prompt import GET_PAGES
        my_master_prompt = GET_PAGES
        if not my_master_prompt or not isinstance(my_master_prompt, str):
            print(f"FATAL ERROR: GET_PAGES from prompt.py is not a valid string. Value: {my_master_prompt}", file=sys.stderr)
            sys.exit(1)
    except ImportError:
        print("FATAL ERROR: Could not import GET_PAGES from prompt.py. Please check the file.", file=sys.stderr)
        sys.exit(1)
    except Exception as e_get_prompt:
        print(f"FATAL ERROR: Error getting prompt from prompt.py: {e_get_prompt}", file=sys.stderr)
        sys.exit(1)

    # Определение пути к файлу как в оригинале
    # target_txt_file_path = r"c:\Users\<USER>\Desktop\Сверка\Scan\ВЕРЕСЕНЬ 2024\ScanTest_20250510_190517.txt"
    # target_txt_file_path = r"d:\Scan\Эпицентр\2024\pdf_extracted_text_pages\Чек_2025-04-30_173244_pages.txt"
    target_txt_file_path = r"d:\Scan\20250430_Merge 202-300.pdf"
    doc = fitz.open(target_txt_file_path)
    pages_count = len(doc)
    print(f"Обнаружено страниц в файле: {pages_count}")
    my_master_prompt = (f"выведи в file_name: {target_txt_file_path}. Всего страниц: {pages_count}. ПРОМПТ: {my_master_prompt}")

    DO_FORCE_REFRESH = True # Как в оригинале

    # Обработка аргументов командной строки как в оригинале
    args = sys.argv[1:]
    temp_args = []

    idx = 0
    while idx < len(args):
        arg = args[idx]
        # Обработка аргументов --force-refresh, --no-cache, --use-cache, --temp как в оригинале
        if arg.lower() == '--force-refresh':
            DO_FORCE_REFRESH = True
            print("Command line: Force refresh enabled.")
        elif arg.lower() == '--no-cache':
            USE_CACHE = False
            processing_cache = {}
            print("Command line: Cache disabled.")
        elif arg.lower() == '--use-cache':
            USE_CACHE = True
            # Логика load_cache здесь была в оригинале, оставляем
            processing_cache = load_cache()
            print("Command line: Cache enabled.")
        elif arg.lower() == '--temp' and idx + 1 < len(args):
            try:
                new_temp = float(args[idx + 1])
                if 0.0 <= new_temp <= 2.0:
                    GENERATION_TEMPERATURE = new_temp
                    print(f"Generation temperature set to {GENERATION_TEMPERATURE} via command line.")
                else:
                    print(f"Invalid temperature value: {args[idx + 1]}. Must be between 0.0 and 2.0. Using default: {GENERATION_TEMPERATURE}", file=sys.stderr)
                idx += 1
            except ValueError:
                print(f"Invalid temperature value: {args[idx + 1]}. Using default: {GENERATION_TEMPERATURE}", file=sys.stderr)
                idx += 1
        else:
            temp_args.append(arg)
        idx += 1

    # Определение пути из аргументов как в оригинале
    if temp_args:
        potential_path = temp_args[0]
        if Path(potential_path).exists() and Path(potential_path).is_file():
            target_txt_file_path = potential_path
            print(f"Using file path from command line argument: {target_txt_file_path}")
        else:
            print(f"Warning: Command line argument '{potential_path}' is not a valid file path. Using default: {target_txt_file_path}", file=sys.stderr)

    # Проверка существования файла как в оригинале
    if not Path(target_txt_file_path).exists():
        print(f"FATAL ERROR: Input file not found: {target_txt_file_path}", file=sys.stderr)
        sys.exit(1)

    # Стартовое сообщение как в оригинале
    print(f"\nStarting processing for: {target_txt_file_path} (Force refresh: {DO_FORCE_REFRESH}, Cache: {USE_CACHE}, "
          f"Temperature: {GENERATION_TEMPERATURE}, Model: '{MODEL_NAME}')")

    # ИЗМЕНЕНИЕ №4: Распаковываем результат и метаданные
    analysis_result_wrapper, final_usage_metadata = process_text_file_with_gemini(
        target_txt_file_path,
        my_master_prompt,
        force_refresh=DO_FORCE_REFRESH
    )

    # Вывод результата как в оригинале
    print("\n--- Final Analysis Output ---")
    if analysis_result_wrapper:
        print(json.dumps(analysis_result_wrapper, ensure_ascii=False, indent=2))

        # Обработка ошибок в результате как в оригинале
        if analysis_result_wrapper.get("error") and analysis_result_wrapper.get("raw_response"):
            print("\n--- Note: There was an issue processing or parsing the response. ---", file=sys.stderr)
            print(f"Error: {analysis_result_wrapper['error']}", file=sys.stderr)
            if "parser_error" in analysis_result_wrapper:
                print(f"Parser Error: {analysis_result_wrapper['parser_error']}", file=sys.stderr)
            if USE_CACHE:
                 # Сообщение про --force-refresh было в оригинале
                print(f"To get a fresh response (e.g., if you changed temperature or prompt), try running with --force-refresh.", file=sys.stderr)

        # Сохранение результата в файл как в оригинале
        if not analysis_result_wrapper.get("error") or "analyzed_pages" in analysis_result_wrapper:
            # Имя файла было изменено в оригинале, оставляем так
            output_json_path = Path('..') / "analyzed_document_output.json"
            try:
                with open(output_json_path, 'w', encoding='utf-8') as oj_f:
                    json.dump(analysis_result_wrapper, oj_f, ensure_ascii=False, indent=4)
                print(f"\nFull analysis saved to: {output_json_path}")
            except Exception as e_save_json:
                print(f"Error saving full analysis to JSON file: {e_save_json}", file=sys.stderr)
    else:
        # Этот блок был в оригинале
        print("No result was returned from process_text_file_with_gemini.")

    # --- ИЗМЕНЕНИЕ №5: Вывод Информации о Токенах (В САМОМ КОНЦЕ) ---
    print("\n--- Использование Токенов ---")
    if final_usage_metadata:
        prompt_tokens = getattr(final_usage_metadata, 'prompt_token_count', 'N/A')
        candidates_tokens = getattr(final_usage_metadata, 'candidates_token_count', 'N/A')
        total_tokens = getattr(final_usage_metadata, 'total_token_count', 'N/A')

        print(f"Модель: {MODEL_NAME}") # Добавим модель для контекста
        print(f"Входные токены (prompt): {prompt_tokens}")
        print(f"Выходные токены (candidates): {candidates_tokens}")
        print(f"Всего токенов: {total_tokens}")
    elif analysis_result_wrapper and analysis_result_wrapper.get("error"):
        # Если была ошибка, но метаданные могли быть возвращены как None
         print("Информация о токенах недоступна из-за ошибки или отсутствия в ответе API.")
         print(f"(Ошибка: {analysis_result_wrapper.get('error')})")
    else:
         # Если результат None или нет ошибки, но и метаданных нет (например, из кеша)
         print("Информация о токенах недоступна (возможно, результат взят из кеша или не был получен от API).")
    print("-" * 27)
    # --------------------------------------------------------------------

    # Этот print был последним в оригинале
    print("\nScript finished.")