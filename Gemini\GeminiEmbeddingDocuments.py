#!/usr/bin/env python3

import os, re, psycopg2
from google import genai
from google.genai import types
from dotenv import load_dotenv



load_dotenv()

PG_USER=os.getenv('PG_USER')
PG_PASSWORD=os.getenv('PG_PASSWORD')
PG_HOST_LOCAL=os.getenv('PG_HOST_LOCAL')
PG_PORT=os.getenv('PG_PORT')
PG_DBNAME=os.getenv('PG_DBNAME')

# --- Конфиг ---
DB_DSN = os.getenv("DATABASE_URL", f"host={PG_HOST_LOCAL} dbname={PG_DBNAME} user={PG_USER} password={PG_PASSWORD}")
client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

MAX_CHUNK = 800

def chunk_text(text, max_size=MAX_CHUNK):
    sentences = re.split(r'(?<=[.?!])\s+', text)
    chunks, cur = [], ""
    for s in sentences:
        if len(cur) + len(s) + 1 > max_size:
            chunks.append(cur); cur = s
        else:
            cur = (cur + " " + s) if cur else s
    if cur: chunks.append(cur)
    return chunks

def ensure_schema(conn):
    cur = conn.cursor()
    cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
    cur.execute("""
        CREATE TABLE IF NOT EXISTS documents (
          id SERIAL PRIMARY KEY,
          source_id INT REFERENCES t_scan_documents_raw(id),
          file_name TEXT,
          page_number INT,
          chunk_index INT,
          text TEXT,
          embedding VECTOR(3072)
        );
    """)
    cur.execute("CREATE INDEX IF NOT EXISTS idx_documents_embedding ON documents USING hnsw (embedding);")
    conn.commit()
    cur.close()

def process_all(conn):
    cur = conn.cursor()
    cur.execute("SELECT id, description, file_name, page_number FROM t_scan_documents_raw;")
    rows = cur.fetchall()
    for source_id, desc, fname, page in rows:
        chunks = chunk_text(desc)
        resp = genai_client.models.embed_content(
            model="gemini-embedding-001",
            contents=chunks,
            config=types.EmbedContentConfig(task_type="RETRIEVAL_DOCUMENT")
        )
        for idx, (text, emb) in enumerate(zip(chunks, resp.embeddings)):
            cur.execute("""
                INSERT INTO documents(source_id, file_name, page_number, chunk_index, text, embedding)
                VALUES (%s,%s,%s,%s,%s,%s);
            """, (source_id, fname, page, idx, text, emb))
    conn.commit()
    cur.close()

def query_and_answer(conn, user_q):
    resp_q = genai_client.models.embed_content(
        model="gemini-embedding-001",
        contents=[user_q],
        config=types.EmbedContentConfig(task_type="RETRIEVAL_QUERY")
    )
    q_vec = resp_q.embeddings[0]
    cur = conn.cursor()
    cur.execute("""
      SELECT text, file_name, page_number
        FROM documents
    ORDER BY embedding <-> %s
       LIMIT 5;
    """, (q_vec,))
    rows = cur.fetchall()
    cur.close()

    ctx = "\n".join(f"(файл {f}, стр. {p}): {t}" for t, f, p in rows)
    prompt = f"Контекст:\n{ctx}\n\nВопрос: {user_q}\nОтветь кратко и точно, с указанием страницы."
    resp = genai_client.models.generate_content(
        model="gemini-2.5-flash",
        contents=[prompt]
    )
    print("\n➡️ Ответ:\n" + resp.text)

def main():
    conn = psycopg2.connect(DB_DSN)
    conn.autocommit = False

    ensure_schema(conn)
    process_all(conn)

    print("Готово. Введите ваш вопрос.")
    while True:
        q = input("Вопрос (‘exit’ для выхода): ").strip()
        if q.lower() in ("exit", "quit"):
            break
        query_and_answer(conn, q)

    conn.close()

if __name__ == "__main__":
    main()
