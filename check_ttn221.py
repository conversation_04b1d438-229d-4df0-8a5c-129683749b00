# -*- coding: utf-8 -*-
"""
Проверка конкретно ТТН 221
"""

import os
import pandas as pd
import psycopg2
from dotenv import load_dotenv

load_dotenv()

# Конфигурация из .env
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_HOST = os.getenv("PG_HOST_LOCAL")
PG_PORT = os.getenv("PG_PORT")
PG_DBNAME = os.getenv("PG_DBNAME")

def check_ttn221():
    conn_string = f"dbname='{PG_DBNAME}' user='{PG_USER}' host='{PG_HOST}' password='{PG_PASSWORD}' port='{PG_PORT}'"
    
    try:
        with psycopg2.connect(conn_string) as conn:
            print("Проверяем ТТН 221...")
            
            # Специальный запрос для ТТН 221
            sql = """
                SELECT 
                    doc_type,
                    doc_date,
                    doc_number,
                    buyer_name,
                    buyer_code,
                    page_number,
                    page_type,
                    invoices_numbers,
                    file_name
                FROM 
                    t_scan_documents
                WHERE doc_date > '01.01.2025' 
                  AND buyer_code = '32294905'
                  AND doc_type = 'ТТН'
                  AND doc_number = '221'
                ORDER BY page_type ASC, page_number ASC     
                ;
            """
            
            df = pd.read_sql_query(sql, conn)
            print(f"Найдено {len(df)} записей для ТТН 221:")
            
            if len(df) > 0:
                for idx, row in df.iterrows():
                    print(f"  Запись {idx+1}:")
                    print(f"    file_name: {row['file_name']}")
                    print(f"    page_number: {row['page_number']}")
                    print(f"    page_type: {row['page_type']}")
                    print(f"    doc_date: {row['doc_date']}")
                    print()
                
                # Проверяем уникальные файлы
                unique_files = df['file_name'].unique()
                print(f"Уникальные файлы: {len(unique_files)}")
                for i, file in enumerate(unique_files, 1):
                    print(f"  {i}. {file}")
                    file_pages = df[df['file_name'] == file]
                    print(f"     Страницы: {list(file_pages['page_number'])}")
                    print(f"     Типы: {list(file_pages['page_type'])}")
                
            else:
                print("❌ ТТН 221 не найден в базе данных!")
                
    except Exception as e:
        print(f"Ошибка: {e}")

if __name__ == "__main__":
    check_ttn221()
