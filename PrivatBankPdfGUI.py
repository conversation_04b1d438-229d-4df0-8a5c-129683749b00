# -*- coding: utf-8 -*-
# pyinstaller --onefile --windowed --name PrivatBankDownloader PrivatBankPdfGUI.py
# https://gemini.google.com/app/2043f90d1b17f244
# Выгрузка квитанций ПриватБанка в PDF БЕЗ GUI
import asyncio
import os
import re
from datetime import datetime
from collections import defaultdict
import aiohttp
import aiofiles
import pandas as pd
from dotenv import load_dotenv
from dateutil.parser import parse

# Загружаем переменные окружения из файла .env
load_dotenv()

# --- КОНФИГУРАЦИЯ ---
# Базовые URL для API ПриватБанка
TRANSACTIONS_URL = "https://acp.privatbank.ua/api/statements/transactions"
RECEIPT_URL = "https://acp.privatbank.ua/api/paysheets/print_receipt"
# Максимальное количество одновременных загрузок PDF
MAX_CONCURRENT_DOWNLOADS = 10

def sanitize_foldername(name):
    """Удаляет недопустимые символы из имени для создания папки."""
    return re.sub(r'[\\/*?:"<>|]', "_", name)

async def get_all_transactions(session, token, start_date, end_date):
    """
    Асинхронно получает полный список транзакций за указанный период.
    """
    all_transactions = []
    follow_id = None
    page_num = 1
    headers = {'user-agent': 'Avtoklient', 'token': token, 'Content-Type': 'application/json;charset=utf-8'}
    print(f"Получение транзакций с {start_date} по {end_date}...")
    while True:
        params = {'startDate': start_date, 'endDate': end_date, 'limit': 500}
        if follow_id:
            params['followId'] = follow_id
        try:
            async with session.post(TRANSACTIONS_URL, params=params, headers=headers, timeout=30) as response:
                response.raise_for_status()
                data = await response.json()
                transactions_on_page = data.get('transactions', [])
                if transactions_on_page:
                    all_transactions.extend(transactions_on_page)
                    print(f"  - Получено {len(transactions_on_page)} транзакций со страницы {page_num}")
                if data.get('exist_next_page'):
                    follow_id = data.get('next_page_id')
                    page_num += 1
                else:
                    break
        except aiohttp.ClientError as e:
            print(f"Ошибка при получении транзакций: {e}")
            break
        except Exception as e:
            print(f"Неожиданная ошибка при обработке транзакций: {e}")
            break
    return all_transactions

async def download_receipt(session, transaction, token, base_output_dir, semaphore):
    """
    Асинхронно загружает PDF-квитанцию для одной транзакции.
    """
    async with semaphore:
        try:
            doc_num = transaction.get('NUM_DOC', '0')
            doc_date_str = transaction.get('DAT_OD', 'UNKNOWN_DATE')
            doc_datetime = parse(doc_date_str, dayfirst=True)
            month_folder_name = doc_datetime.strftime('%Y%m')
            month_output_dir = os.path.join(base_output_dir, month_folder_name)
            os.makedirs(month_output_dir, exist_ok=True)
            doc_date_formatted = doc_datetime.strftime('%d %m %Y')
            filename = f"{doc_num} {doc_date_formatted}.pdf"
            filepath = os.path.join(month_output_dir, filename)
            if os.path.exists(filepath):
                print(f"-> Файл {filename} уже существует, пропуск.")
                return (month_output_dir, filename)
            print(f"-> Загрузка квитанции {filename} в {month_output_dir}...")
            headers = {'token': token, 'Content-Type': 'application/json', 'Accept': 'application/octet-stream'}
            payload = {
                "transactions": [{"account": transaction.get('AUT_MY_ACC'), "reference": transaction.get('REF'), "refn": transaction.get('REFN')}],
                "perPage": 1
            }
            async with session.post(RECEIPT_URL, json=payload, headers=headers, timeout=60) as response:
                if response.status == 200:
                    content = await response.read()
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)
                    print(f"   - Файл {filename} успешно сохранен.")
                    return (month_output_dir, filename)
                else:
                    error_text = await response.text()
                    print(f"   - Ошибка {response.status} при загрузке {filename}: {error_text[:200]}")
                    return None
        except Exception as e:
            print(f"   - Не удалось обработать транзакцию {transaction.get('REF')}: {e}")
            return None

async def write_log_files(file_map):
    """Записывает собранные имена файлов в лог-файлы по месяцам."""
    for folder_path, filenames in file_map.items():
        if not filenames:
            continue
        month_str = os.path.basename(folder_path)
        log_filepath = os.path.join(folder_path, f"{month_str}.log")
        filenames.sort()
        log_content = ",".join(filenames)
        async with aiofiles.open(log_filepath, 'w', encoding='utf-8') as f:
            await f.write(log_content)
        print(f"Создан/обновлен лог-файл: {log_filepath}")

def create_excel_report(report_data, base_output_dir, partner_code, client_name, start_date, end_date):
    """Создает Excel отчет на основе данных транзакций."""
    if not report_data:
        print("Нет данных для создания Excel отчета.")
        return
    try:
        report_data.sort(key=lambda x: x['дата'])
        df = pd.DataFrame(report_data)
        df['дата'] = pd.to_datetime(df['дата']).dt.strftime('%d.%m.%Y')
        excel_filename = f"Отчет_{partner_code}_{start_date}_по_{end_date}.xlsx"
        excel_filepath = os.path.join(base_output_dir, excel_filename)
        df.to_excel(excel_filepath, index=False, engine='openpyxl')
        print(f"\nОтчет Excel для '{client_name}' успешно сохранен: {excel_filepath}")
    except Exception as e:
        print(f"\nНе удалось создать Excel отчет для '{client_name}': {e}")

async def main():
    """
    Главная функция: определяет параметры, получает транзакции, фильтрует их, запускает загрузку и создает логи.
    """
    # --- УКАЖИТЕ ВАШИ ДАННЫЕ ЗДЕСЬ ---
    # Оставьте partner_code = "" для обработки ВСЕХ контрагентов.
    # Укажите код (например, "43028967") для обработки только одного контрагента.
    partner_code = ""
    token = os.getenv("PB_PPK_TOKEN")
    start_date = "01-01-2024"
    end_date = datetime.today().strftime("%d-%m-%Y")
    # ВАЖНО: Укажите правильное имя поля для суммы транзакции из API
    amount_field = "AMT"
    # ------------------------------------

    if not token:
        print("Ошибка: токен не найден. Проверьте файл .env и имя переменной PB_PPK_TOKEN.")
        return

    semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
    
    async with aiohttp.ClientSession() as session:
        all_transactions = await get_all_transactions(session, token, start_date, end_date)
        if not all_transactions:
            print("Транзакции за указанный период не найдены.")
            return

        # Группируем все транзакции по коду контрагента
        grouped_transactions = defaultdict(list)
        client_names = {}
        for tx in all_transactions:
            p_code = tx.get('AUT_CNTR_CRF')
            p_name = tx.get('AUT_CNTR_NAM', 'UnknownClient')
            if p_code:
                grouped_transactions[p_code].append(tx)
                if p_code not in client_names:
                    client_names[p_code] = p_name
            else:
                print(f"Предупреждение: пропущена транзакция без кода контрагента (REF: {tx.get('REF')})")

        # Определяем, каких партнеров обрабатывать
        partners_to_process = {}
        if partner_code: # Режим одного клиента
            if partner_code in grouped_transactions:
                partners_to_process = {partner_code: grouped_transactions[partner_code]}
                print(f"Режим одного клиента. Найден контрагент: {client_names.get(partner_code)}")
            else:
                print(f"Контрагент с кодом {partner_code} не найден в транзакциях за указанный период.")
                return
        else: # Режим всех клиентов
            partners_to_process = grouped_transactions
            print(f"\nРежим всех клиентов. Обнаружено {len(partners_to_process)} контрагентов. Начинаю обработку...")

        # Основной цикл обработки по каждому контрагенту
        for p_code, p_transactions in partners_to_process.items():
            client_name = client_names.get(p_code, "UnknownClient")
            sanitized_client_name = sanitize_foldername(client_name)
            
            print("\n" + "="*50)
            print(f"Обработка клиента: {client_name} (код: {p_code})")
            print(f"Найдено транзакций: {len(p_transactions)}")
            
            base_output_dir = f"{p_code}_{sanitized_client_name}"
            incoming_dir = os.path.join(base_output_dir, "входящие")
            outgoing_dir = os.path.join(base_output_dir, "исходящие")
            os.makedirs(incoming_dir, exist_ok=True)
            os.makedirs(outgoing_dir, exist_ok=True)

            incoming_tx_list, outgoing_tx_list, report_data = [], [], []
            log_file_map = defaultdict(list)

            for tx in p_transactions:
                is_incoming = tx.get('TRANTYPE') == 'C'
                is_outgoing = tx.get('TRANTYPE') == 'D'
                
                if is_incoming or is_outgoing:
                    try:
                        doc_datetime = parse(tx.get('DAT_OD', ''), dayfirst=True)
                        amount = float(tx.get(amount_field, 0.0))
                        if is_outgoing:
                            outgoing_tx_list.append(tx)
                            amount = -amount
                        else:
                            incoming_tx_list.append(tx)
                        report_data.append({"Клиент": client_name, "дата": doc_datetime.strftime('%Y-%m-%d'), "сумма": amount})
                    except (ValueError, TypeError) as e:
                        print(f" - Предупреждение: не удалось обработать данные для отчета из транзакции {tx.get('REF')}: {e}")

            # Загрузка квитанций
            if incoming_tx_list:
                print(f"\nНайдено {len(incoming_tx_list)} входящих транзакций. Начало загрузки...")
                tasks_in = [download_receipt(session, tx, token, incoming_dir, semaphore) for tx in incoming_tx_list]
                for result in await asyncio.gather(*tasks_in):
                    if result: log_file_map[result[0]].append(result[1])
            
            if outgoing_tx_list:
                print(f"\nНайдено {len(outgoing_tx_list)} исходящих транзакций. Начало загрузки...")
                tasks_out = [download_receipt(session, tx, token, outgoing_dir, semaphore) for tx in outgoing_tx_list]
                for result in await asyncio.gather(*tasks_out):
                    if result: log_file_map[result[0]].append(result[1])

            # Создание лог-файлов и отчета для текущего клиента
            if log_file_map:
                await write_log_files(log_file_map)
            
            create_excel_report(report_data, base_output_dir, p_code, client_name, start_date, end_date)

    print("\n" + "="*50)
    print("Работа завершена.")

if __name__ == "__main__":
    # Для Windows может понадобиться следующая строка
    # asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())