from datetime import datetime, time

# Получение текущего времени в UTC
current_utc_time = datetime.utcnow().time()

# Определение временных границ
time_16_30 = time(16, 30)
time_00_30 = time(0, 30)

# Логика выбора модели
if current_utc_time >= time_16_30 or current_utc_time < time_00_30:
    MODEL_DEEPSEEK = "deepseek-reasoner"
else:
    MODEL_DEEPSEEK = "deepseek-chat"

print(f"Current UTC time: {current_utc_time}")
print(f"Selected model: {MODEL_DEEPSEEK}")