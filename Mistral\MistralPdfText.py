import asyncio
import json
import os
import time
from typing import Union, Dict

from mistralai import Mistral
from dotenv import load_dotenv
from pyexpat.errors import messages
from typing_extensions import Any

from prompt import PROMPT_EXAMPLE_GEMINI

load_dotenv()

MISTRAL_MODEL = "pixtral-12b"  # pixtral-12b M/tokens In=$0.15; Out=$0.15; "mistral-small-latest" in=0,1$; out=0,3$

if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")

msg = """
```markdown
Здав (відповідальна особа вантажовідправника)
Валяєв Расім Сейранович, <PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
(прізвище (за наявності), власне ім'я та по батькові (за наявності),
підпис)

Прийняв (відповідальна особа вантажоодержувача)

Ідентифікаційний код 32294905
Р/р 26009010119536
ВАНТАЖНО-РОЗВАНТАЖУВАЛЬНІ ОПЕРАЦІЇ
ІПН 322949010132, Св-во №13842174
08134 (год. Києво-Святошинський р-н,
Майбутнє, вул. Промислова, 5

| Операція       | Маса брутто, т | прибуття |
| -------------- | --------------- | -------- |
| 1              | 2               | 3        |
| Завантаження   | 0,07787         |          |
| Розвантаження | 0,07787         |          |

* відомості заповнюються у разі перевезення туш, напівтуш, четвертин, відрубів, шматків м'яса.
** відомості заповнюються у разі перевезення харчових продуктів, які потребують дотримання температурного режиму.

| Підпис відповідальної особи |
| ----------------------------- |
| 6                             |
| Валяєв Расім Сейранович, Директор |
| 5                             |
```
"""

# If local document, upload and retrieve the signed url
# uploaded_pdf = client.files.upload(
#     file={
#         "file_name": "uploaded_file.pdf",
#         "content": open("uploaded_file.pdf", "rb"),
#     },
#     purpose="ocr"
# )
# signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)


def clear_text(json_string: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        try:
            json_string = json_string.strip()
            json_string = json_string.replace('```markdown', '')
            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            return {}
    return json_string if isinstance(json_string, dict) else {}



def get_url_for_file(pdf_path):
    api_key = os.environ["MISTRAL_API_KEY"]
    client = Mistral(api_key=api_key)

    uploaded_pdf = client.files.upload(
        file={"file_name": pdf_path, "content": open(pdf_path, "rb"),},
        purpose="ocr",
    )
    return client.files.get_signed_url(file_id=uploaded_pdf.id)


def get_messages(signed_url):
    # Define the messages for the chat
    messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": PROMPT_EXAMPLE_GEMINI},
                {
                    "type": "document_url",
                    # "document_url": "https://arxiv.org/pdf/1805.04770",
                    "document_url": signed_url.url,
                },
            ],
        }
    ]
    return messages


def get_structured_data_sync(text, prompt=PROMPT_EXAMPLE_GEMINI, repeat_count=3):
    import requests
    promt = prompt + '\n\n' + text
    API_URL = "https://api.mistral.ai/v1/chat/completions"
    api_key = os.environ["MISTRAL_API_KEY"]

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": MISTRAL_MODEL,  # mistral-small-latest in=$0.1; out=$0.3
        "messages": [{"role": "user", "content": promt}],
        "temperature": 0.2,
        # "max_tokens": 1000
    }
    while repeat_count > 0:
        response = requests.post(API_URL, headers=headers, json=data)

        if response.status_code == 200:
            result = response.json()['choices'][0]['message']['content']
            result = clear_text(result)
            if result:
                return result
        else:
            print("Ошибка:", response.status_code, response.text)
        time.sleep(5* (4 - repeat_count))  # Увеличиваем задержку между попытками
        repeat_count -= 1
    print(f"Не удалось получить данные после нескольких попыток.\nТЕКСТ:\n{text}\nОТВЕТ:\n{response.text}")
    return None


async def get_data_from_text_by_mistral_async(content, prompt = PROMPT_EXAMPLE_GEMINI, repeat_count=3):
    import aiohttp
    import os

    API_URL = "https://api.mistral.ai/v1/chat/completions"
    api_key = os.environ["MISTRAL_API_KEY"]
    content = f"{prompt}\n\nЗАДАЧА(OCR-текст): {content}"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    data = {
        "model": MISTRAL_MODEL,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.2,
    }
    while repeat_count > 0:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(API_URL, headers=headers, json=data) as response:
                    if response.status == 200:
                        result = await response.json()
                        print(result['usage'])
                        result = clear_text(result['choices'][0]['message']['content'])
                        if result:
                            return result
                    else:
                        print("Ошибка:", response.status, await response.text())
                        return None
        except Exception as e:
            print(f"Ошибка в get_data_from_text_by_mistral_async: {e}")

        time.sleep(5* (4 - repeat_count))  # Увеличиваем задержку между попытками
        repeat_count -= 1
    print(f"Не удалось получить данные после нескольких попыток.\nТЕКСТ:\n{content}\nОТВЕТ:\n{response.text}")
    return None


def get_info_from_pdf(pdf_path: str):
    # Retrieve the API key from environment variables
    api_key = os.environ["MISTRAL_API_KEY"]

    # Initialize the Mistral client
    client = Mistral(api_key=api_key)

    signed_url = get_url_for_file(pdf_path)
    messages = get_messages(signed_url)

    # Get the chat response
    chat_response = client.chat.complete(model=MISTRAL_MODEL, messages=messages)

    # Print the content of the response
    print(chat_response.choices[0].message.content)


if __name__ == "__main__":
    pdf_path = r"c:\Scan\All\TestVN2str.pdf"
    # get_info_from_pdf(pdf_path)
    rst = asyncio.run(get_data_from_text_by_mistral_async(msg))
    print(rst)
