# -*- coding: utf-8 -*-
"""
Утилита для разделения больших PDF файлов на отдельные страницы
для последующей обработки в Google Document AI
"""

import os
import sys
from pathlib import Path

try:
    import PyPDF2
except ImportError:
    print("Ошибка: Не установлен модуль PyPDF2")
    print("Установите его командой: pip install PyPDF2")
    sys.exit(1)

# --- НАСТРОЙКА ---
# Путь к большому PDF файлу
LARGE_PDF_PATH = r"c:\Users\<USER>\Desktop\Разблокировка\Перелік_ПН_РК_ГУ_ДПС_суд.pdf "

# Папка для сохранения отдельных страниц
OUTPUT_DIRECTORY = r"c:\Users\<USER>\Desktop\Разблокировка"
# --- КОНЕЦ НАСТРОЙКИ ---


def split_pdf_to_pages(pdf_path: str, output_dir: str) -> bool:
    """
    Разделяет PDF файл на отдельные страницы.
    
    Args:
        pdf_path: Путь к исходному PDF файлу
        output_dir: Папка для сохранения отдельных страниц
        
    Returns:
        True если успешно, False если ошибка
    """
    if not os.path.exists(pdf_path):
        print(f"Ошибка: Файл не найден: {pdf_path}")
        return False
    
    # Создаем выходную папку если её нет
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    try:
        # Открываем PDF файл
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            total_pages = len(pdf_reader.pages)
            
            print(f"Обрабатываю файл: {os.path.basename(pdf_path)}")
            print(f"Всего страниц: {total_pages}")
            
            base_name = os.path.splitext(os.path.basename(pdf_path))[0]
            
            # Разделяем на отдельные страницы
            for page_num in range(total_pages):
                pdf_writer = PyPDF2.PdfWriter()
                pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Формируем имя файла для страницы
                page_filename = f"{base_name}_page_{page_num + 1:03d}.pdf"
                page_path = os.path.join(output_dir, page_filename)
                
                # Сохраняем страницу
                with open(page_path, 'wb') as output_file:
                    pdf_writer.write(output_file)
                
                # Проверяем размер созданного файла
                page_size = os.path.getsize(page_path)
                max_size = 40 * 1024 * 1024  # 40 МБ
                
                status = "✓" if page_size <= max_size else "⚠ (всё ещё большой)"
                print(f"  Страница {page_num + 1:3d}: {page_size / (1024*1024):.1f} МБ {status}")
            
            print(f"\nУспешно разделено на {total_pages} страниц")
            print(f"Файлы сохранены в: {output_dir}")
            return True
            
    except Exception as e:
        print(f"Ошибка при разделении PDF: {e}")
        return False


def main():
    """Главная функция"""
    print("=== РАЗДЕЛЕНИЕ БОЛЬШОГО PDF НА СТРАНИЦЫ ===")
    print(f"Исходный файл: {LARGE_PDF_PATH}")
    print(f"Выходная папка: {OUTPUT_DIRECTORY}")
    print()
    
    # # Проверяем размер исходного файла
    # if os.path.exists(LARGE_PDF_PATH):
    #     file_size = os.path.getsize(LARGE_PDF_PATH)
    #     print(f"Размер исходного файла: {file_size / (1024*1024):.1f} МБ")
        
    #     if file_size <= 40 * 1024 * 1024:
    #         print("Файл не превышает лимит 40 МБ. Разделение не требуется.")
    #         return
    # else:
    #     print(f"Ошибка: Файл не найден: {LARGE_PDF_PATH}")
    #     print("Проверьте путь в переменной LARGE_PDF_PATH")
    #     return
    
    print("Начинаю разделение...")
    success = split_pdf_to_pages(LARGE_PDF_PATH, OUTPUT_DIRECTORY)
    
    if success:
        print("\n=== РАЗДЕЛЕНИЕ ЗАВЕРШЕНО ===")
        print("Теперь вы можете:")
        print("1. Обновить путь PDF_SOURCE_DIRECTORY в process_all_pdfs.py")
        print(f"   на: {OUTPUT_DIRECTORY}")
        print("2. Запустить process_all_pdfs.py для обработки отдельных страниц")
        print()
        print("⚠️  ВАЖНО: После обработки process_all_pdfs.py автоматически удалит:")
        print("   • Все разделенные PDF файлы")
        print("   • Временные JSON файлы")
        print("   • Папку split_pages (если она станет пустой)")
        print("   • Останется только объединенный JSON файл с результатами")
    else:
        print("\n=== ОШИБКА ПРИ РАЗДЕЛЕНИИ ===")


if __name__ == "__main__":
    main()
