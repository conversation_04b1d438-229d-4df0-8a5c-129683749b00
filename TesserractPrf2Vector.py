import os
import cv2
import pytesseract
import numpy as np
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch

# --- КОНФИГУРАЦИЯ ---

# Укажите путь к исполняемому файлу Tesseract, если он не находится в системном PATH
# Для Windows:
# pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
# Для Linux/macOS обычно этот шаг не требуется.

# Порог уверенности OCR. Слова с уверенностью ниже этого значения будут проигнорированы.
OCR_CONFIDENCE_THRESHOLD = 60

def preprocess_image(image_path: str) -> np.ndarray:
    """
    Загружает изображение и выполняет предварительную обработку для улучшения качества OCR.
    
    :param image_path: Путь к исходному изображению.
    :return: Обработанное изображение в формате OpenCV (NumPy array).
    """
    print(f"[ИНФО] Шаг 1: Предварительная обработка изображения '{image_path}'...")
    
    # Загрузка изображения с помощью OpenCV
    image = cv2.imread(image_path)
    if image is None:
        raise FileNotFoundError(f"Не удалось загрузить изображение по пути: {image_path}")

    # Преобразование в оттенки серого
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # Применение адаптивного порогового преобразования для создания чистого черно-белого изображения
    processed_image = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
    )
    
    print("[ИНФО] Предварительная обработка завершена.")
    return processed_image

def extract_ocr_data(processed_image: np.ndarray, lang: str = 'ukr') -> dict:
    """
    Выполняет OCR на обработанном изображении и извлекает данные о тексте и его координатах.
    
    :param processed_image: Изображение, обработанное для OCR.
    :param lang: Язык для распознавания (трехбуквенный код).
    :return: Словарь с данными OCR, отфильтрованными по порогу уверенности.
    """
    print(f"[ИНФО] Шаг 2: Выполнение OCR с языком '{lang}'...")
    
    # Использование pytesseract для получения структурированных данных
    ocr_data = pytesseract.image_to_data(
        processed_image, 
        lang=lang, 
        output_type=pytesseract.Output.DICT
    )
    
    print("[ИНФО] OCR завершено. Фильтрация результатов...")
    return ocr_data


def create_searchable_pdf(original_image_path: str, ocr_data: dict, output_pdf_path: str):
    """
    Создает PDF-файл с возможностью поиска, накладывая невидимый текстовый слой
    поверх исходного изображения.
    
    :param original_image_path: Путь к оригинальному изображению.
    :param ocr_data: Данные OCR от Tesseract.
    :param output_pdf_path: Путь для сохранения итогового PDF-файла.
    """
    print(f"[ИНФО] Шаг 3: Создание PDF-файла с возможностью поиска '{output_pdf_path}'...")
    
    # Открываем оригинальное изображение, чтобы получить его размеры
    original_image = Image.open(original_image_path)
    width_px, height_px = original_image.size
    
    # Размеры в пунктах (1 пункт = 1/72 дюйма). Предполагаем 72 DPI для прямого соответствия.
    width_pt, height_pt = width_px, height_px

    # Создание холста ReportLab с размерами, соответствующими изображению
    pdf_canvas = canvas.Canvas(output_pdf_path, pagesize=(width_pt, height_pt))

    # 1. Рисуем оригинальное изображение как фон
    pdf_canvas.drawImage(original_image_path, 0, 0, width=width_pt, height=height_pt)

    # 2. Добавляем невидимый текстовый слой
    num_words = len(ocr_data['text'])
    for i in range(num_words):
        # Фильтруем слова с низкой уверенностью
        conf = int(ocr_data['conf'][i])
        if conf < OCR_CONFIDENCE_THRESHOLD:
            continue

        # Пропускаем пустые строки
        text = ocr_data['text'][i].strip()
        if not text:
            continue

        # Получаем координаты из данных Tesseract (в пикселях, от верхнего левого угла)
        x, y, w, h = ocr_data['left'][i], ocr_data['top'][i], ocr_data['width'][i], ocr_data['height'][i]

        # --- КЛЮЧЕВОЕ ПРЕОБРАЗОВАНИЕ КООРДИНАТ ---
        x_rl = x
        y_rl = height_pt - (y + h)

        # Создаем текстовый объект и размещаем его
        text_object = pdf_canvas.beginText()
        text_object.setTextOrigin(x_rl, y_rl)
        
        # --- ИСПРАВЛЕНИЕ: Применяем настройки к текстовому объекту, а не к холсту ---
        text_object.setFont("Helvetica", 8) # Размер шрифта здесь не так важен, но он должен быть задан
        text_object.setTextRenderMode(3)    # Режим 3 делает текст невидимым (для поиска и копирования)
        
        text_object.textLine(text)
        pdf_canvas.drawText(text_object)

    # Сохраняем PDF-файл
    pdf_canvas.save()
    print(f"[ИНФО] PDF-файл успешно сохранен: {output_pdf_path}")


def main(image_path: str, output_path: str):
    """
    Главная функция, оркестрирующая весь процесс.
    """
    if not os.path.exists(image_path):
        print(f"[ОШИБКА] Исходный файл не найден: {image_path}")
        return

    try:
        # Шаг 1: Предварительная обработка изображения
        processed_image = preprocess_image(image_path)
        
        # Шаг 2: Извлечение данных OCR
        ocr_data = extract_ocr_data(processed_image, lang='ukr')
        
        # Шаг 3: Создание PDF
        create_searchable_pdf(image_path, ocr_data, output_path)
        
    except Exception as e:
        print(f"[КРИТИЧЕСКАЯ ОШИБКА] Произошла ошибка во время обработки: {e}")

if __name__ == "__main__":
    # --- ПАРАМЕТРЫ ЗАПУСКА ---
    INPUT_IMAGE = r"C:\Users\<USER>\Desktop\20250516_1-3_040.png"  # Замените на путь к вашему JPG-файлу
    OUTPUT_PDF = "document_searchable.pdf" # Имя итогового PDF-файла
    
    main(INPUT_IMAGE, OUTPUT_PDF)