-- genspark
WITH clean_source AS (
    SELECT
        doc_type,
        -- Заполняем дату документа, если она пуста, максимальной датой по file_name и doc_type
        COALESCE(doc_date,
            MAX(doc_date) OVER (
                PARTITION BY file_name, doc_type
            )
        ) AS doc_date,
        -- Заполняем номер документа, если он пустой, максимальным номером по file_name, doc_type и invoices_numbers
        COALESCE(doc_number,
            MAX(doc_number) OVER (
                PARTITION BY file_name, doc_type, invoices_numbers
            )
        ) AS doc_number,
        -- Приводим название клиента к единому виду
        CASE
            WHEN buyer_name IS NULL OR buyer_name = '' THEN
                MAX(buyer_name) OVER (
                    PARTITION BY file_name, doc_type, invoices_numbers
                )
            WHEN buyer_name ILIKE '%епіцентр%' THEN
                'Епіцентр К'
            ELSE
                buyer_name
        END AS buyer_name,
        -- Заполняем код клиента, если он пустой
        COALESCE(buyer_code,
            MAX(buyer_code) OVER (
                PARTITION BY file_name, doc_type, invoices_numbers
            )
        ) AS buyer_code,
        invoices_numbers,
        page_type,
        file_name,
        page_number
    FROM
        t_scan_documents
)
-- Для ВН также ищем код клиента, если он не найден выше
, final_result AS (
    SELECT
        doc_type,
        doc_date,
        doc_number,
        buyer_name,
        -- Для ВН ищем код клиента по file_name и invoices_numbers среди ТТН, если пусто
        CASE
            WHEN doc_type = 'ВН' AND (buyer_code IS NULL OR buyer_code = '') THEN (
                SELECT cs2.buyer_code
                FROM clean_source cs2
                WHERE cs2.file_name = cs1.file_name
                  AND cs2.doc_type = 'ТТН'
                  AND cs2.invoices_numbers = cs1.invoices_numbers
                LIMIT 1
            )
            ELSE buyer_code
        END AS buyer_code,
        invoices_numbers,
        page_type,
        file_name,
        page_number
    FROM clean_source cs1
)
SELECT
    elements.invoice_element AS elem,
--    tsd.id, -- Явно перечисляем нужные поля из tsd, чтобы избежать дублирования tsd.*
    tsd.doc_type,
    tsd.doc_date,
    tsd.doc_number,
    tsd.buyer_name,
    tsd.buyer_code,
    tsd.invoices_numbers,
    tsd.page_type,
    tsd.file_name,
    tsd.page_number
FROM
    final_result tsd
LEFT JOIN LATERAL jsonb_array_elements_text( -- ИЗМЕНЕНО НА LEFT JOIN LATERAL
    CASE
        WHEN jsonb_typeof(tsd.invoices_numbers) = 'array' THEN tsd.invoices_numbers -- Передаем массив, если он есть
        ELSE NULL -- Если не массив (или NULL), передаем NULL, jsonb_array_elements_text(NULL) вернет 0 строк
    END
) AS elements(invoice_element) ON true -- ON true для LATERAL JOIN, когда нет явных условий соединения
WHERE
    -- Условие для элемента из массива (сработает, только если elements.invoice_element не NULL)
    elements.invoice_element IN ('12370')
    OR
    tsd.doc_number IN ('12370')
--    (tsd.doc_number IN ('161', '170') AND doc_type <> 'ТТН')
ORDER BY 
    file_name,
    page_number
