from prompt import PROMPT_EXAMPLE_GEMINI
from google import genai
from dotenv import load_dotenv
from typing import Dict, Any, Union, List
import os
import asyncio

from Gemini.GeminiTokenCount import get_token_count

# Загрузка переменных окружения из файла .env
load_dotenv()

# Инициализация клиента Gemini
client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
model_2f  = os.getenv("GEMINI_MODEL_2F")

def clear_text(json_string)->Dict[str, Any]:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        import json
        if not json_string: 
            return {}
        
        json_string = json_string.strip()
        try:

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")
    return json_string


async def extract_data_by_gemini_flash(content: str, prompt = PROMPT_EXAMPLE_GEMINI)->Dict[str, Any]:
    # contents = f"{prompt}\n\nТекст для обработки:\n{contents}"
    model= model_2f  # "gemini-2.5-pro-preview-05-06"  # 'models/gemini-2.0-flash'  # "gemini-2.5-flash-preview-tts" 

    # Настройка конфигурации генерации с температурой 0.7
    generation_config = {
        "temperature": 0.15,  # Строгий режим для документов
        "top_p": 0.15,        # Уже диапазон (вместо 0.95)
        "top_k": 30,         # Более строгая фильтрация
        "max_output_tokens": 65000  # Достаточно для ТТН
    }
    content = f"{prompt}\n\nЗАДАЧА(OCR-текст): {content}"
    # Отправка запроса к модели Gemini 2.0 Flash с указанной конфигурацией
    response = client.models.generate_content(model=model, contents=content, config=generation_config)
    
    # Выводим количество токенов в запросе и ответе
    get_token_count(response)
    
    if not  response.text:
        print("Пустой ответ от Gemini")
        return {}
    
    clean_text = clear_text(response.text)
    return clean_text


if __name__ == "__main__":

    if os.name == "nt":
        os.system("cls")
    else:
        os.system("clear")

    contents = """
```markdown
Видаткова накладна № 576 від 01 квітня 2025 р.

Постачальник: ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ "ПРЕСТИЖ ПРОДУКТ.К"
п/р UA203052990000026001015000569 у банку АТ КБ "ПРИВАТБАНК", м. Київ,
вул. Бескидська, буд. 41, м. ЛЬВІВ, 79067, тел.: +38 (050) 428-54-89,
код за ЄДРПΟΥ 41098985, ΙΠΗ 410989826574

Покупець: ПРИВАТНЕ ПІДПРИЄМСТВО "МОНБЛАН 2023"
07406, Київська обл., Броварський р-н, місто Бровари, вул. Київська, будинок 316/2

Договір: № 348 від 01.08.2024

Угода: Замовлення покупця № 537 від 27 березня 2025 р.

Адреса
доставки: 07406, Київська обл., Броварський р-н, місто Бровари, вул. Київська, будинок 316/2

№ замовлення: 2601 від 27.03.2025 р.

| № | Артикул  | Штрихкод   | Код УКТЗЕД | Товар

"""
    
    prompt =  PROMPT_EXAMPLE_GEMINI # "Извлеки дату из этого текста: тип, номер и дату в формате dd.mm.yyyy. Верни только валидный JSON"
    prompt = prompt + ". К словарю добавь ключ 'raw_text' и в него вставь текст который тебе дал."
    
    response = asyncio.run(extract_data_by_gemini_flash(contents, prompt))
    print(response)
