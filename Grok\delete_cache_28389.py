#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Скрипт для удаления кэшированных данных для конкретного ID
"""

import sys
import os

# Добавляем путь к модулю
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from GrokEntity import delete_cache_for_id
from prompt import PROMPT_EXAMPLE_GEMINI

def main():
    """Удаляет кэш для ID 28389"""
    print("🗑️ Удаление кэшированных данных для ID 28389...")
    
    # Вызываем функцию удаления кэша
    success = delete_cache_for_id(28389, PROMPT_EXAMPLE_GEMINI)
    
    if success:
        print("✅ Кэш для ID 28389 успешно удален")
        print("🔄 Теперь при следующем запуске будет выполнен новый API запрос")
    else:
        print("❌ Не удалось удалить кэш для ID 28389")
        print("💡 Возможные причины:")
        print("   - Кэш для этого ID не существует") 
        print("   - Ошибка подключения к базе данных")
        print("   - Ошибка доступа к файлу кэша")

if __name__ == "__main__":
    main()