# -*- coding: utf-8 -*-
"""
GUI для SavePDFPageAsImage.py - обработка PDF документов с OCR через различные AI API.
"""
import os
import logging
import threading
import queue
import asyncio
import sys
import io
import contextlib
import tempfile
from pathlib import Path

# --- Библиотеки для GUI ---
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext, messagebox

# --- Импорт основной логики ---
try:
    from SavePDFPageAsImage import main as process_main
except ImportError as e:
    print(f"Предупреждение: Не удалось импортировать SavePDFPageAsImage: {e}")
    process_main = None

# --- Настройка логгирования ---
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# --- Класс для перехвата логов ---
class TextHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)
        def append():
            self.text_widget.config(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.see(tk.END)
            self.text_widget.config(state='disabled')
        self.text_widget.after(0, append)

# --- Класс для перехвата всего вывода ---
class OutputRedirector:
    def __init__(self, text_widget, prefix=""):
        self.text_widget = text_widget
        self.prefix = prefix
        
    def write(self, text):
        # Показываем даже пустые строки
        def append():
            self.text_widget.config(state='normal')
            # Добавляем префикс к каждой строке
            if self.prefix and text.strip():
                lines = text.split('\n')
                formatted_text = '\n'.join([f"[{self.prefix}] {line}" if line else line for line in lines])
                self.text_widget.insert(tk.END, formatted_text)
            else:
                self.text_widget.insert(tk.END, text)
            self.text_widget.see(tk.END)
            self.text_widget.config(state='disabled')
        self.text_widget.after(0, append)
    
    def flush(self):
        pass

# --- Класс для GUI ---
class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Обработчик PDF документов с AI OCR")
        self.root.geometry("800x700")

        self.style = ttk.Style(self.root)
        self.style.theme_use("clam")

        # Переменные для полей
        self.api_name = tk.StringVar(value="Gemini AI")
        self.model_name = tk.StringVar(value="gemini-2.5-flash")
        self.api_key = tk.StringVar()
        self.folder_path = tk.StringVar()

        self.create_widgets()
        self.setup_logging()

    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Параметры
        form_frame = ttk.LabelFrame(main_frame, text="Параметры обработки", padding="10")
        form_frame.pack(fill=tk.X, expand=False)
        form_frame.columnconfigure(1, weight=1)

        # Наименование API
        ttk.Label(form_frame, text="Наименование API:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        api_name_entry = ttk.Entry(form_frame, textvariable=self.api_name)
        api_name_entry.grid(row=0, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(api_name_entry)

        # Наименование модели
        ttk.Label(form_frame, text="Наименование модели:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        model_entry = ttk.Entry(form_frame, textvariable=self.model_name)
        model_entry.grid(row=1, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(model_entry)

        # API Key
        ttk.Label(form_frame, text="API Key:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        api_key_entry = ttk.Entry(form_frame, textvariable=self.api_key, show="*")
        api_key_entry.grid(row=2, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(api_key_entry)

        # Папка для обработки
        ttk.Label(form_frame, text="Путь к папке:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        folder_entry = ttk.Entry(form_frame, textvariable=self.folder_path)
        folder_entry.grid(row=3, column=1, sticky=tk.EW, padx=5)
        self.add_context_menu(folder_entry)
        ttk.Button(form_frame, text="Обзор...", command=self.browse_folder).grid(row=3, column=2, padx=5)

        # Кнопка запуска
        self.run_button = ttk.Button(main_frame, text="Запустить обработку", command=self.start_processing_thread, style="Accent.TButton")
        self.run_button.pack(pady=10)
        self.style.configure("Accent.TButton", foreground="white", background="green")

        # Лог
        log_frame = ttk.LabelFrame(main_frame, text="Лог выполнения", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled', bg='black', fg='white')
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.add_context_menu(self.log_text)

    def add_context_menu(self, widget):
        """Добавляет контекстное меню с копированием/вставкой для виджета"""
        menu = tk.Menu(widget, tearoff=0)
        menu.add_command(label="Копировать", command=lambda: self.copy_text(widget))
        menu.add_command(label="Вставить", command=lambda: self.paste_text(widget))
        menu.add_separator()
        menu.add_command(label="Выделить все", command=lambda: self.select_all(widget))

        def show_menu(event):
            menu.post(event.x_root, event.y_root)

        widget.bind("<Button-3>", show_menu)  # Правый клик
        widget.bind("<Button-2>", show_menu)  # Средний клик (для macOS)

        # Горячие клавиши
        widget.bind("<Control-c>", lambda e: self.copy_text(widget))
        widget.bind("<Control-v>", lambda e: self.paste_text(widget))
        widget.bind("<Control-a>", lambda e: self.select_all(widget))
        
        # Для macOS
        widget.bind("<Command-c>", lambda e: self.copy_text(widget))
        widget.bind("<Command-v>", lambda e: self.paste_text(widget))
        widget.bind("<Command-a>", lambda e: self.select_all(widget))

    def copy_text(self, widget):
        """Копирует выделенный текст в буфер обмена"""
        try:
            if isinstance(widget, tk.Entry):
                try:
                    start = widget.index("sel.first")
                    end = widget.index("sel.last")
                    selected_text = widget.get()[start:end]
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                except tk.TclError:
                    pass
            elif isinstance(widget, (tk.Text, scrolledtext.ScrolledText)):
                try:
                    selected_text = widget.get("sel.first", "sel.last")
                    self.root.clipboard_clear()
                    self.root.clipboard_append(selected_text)
                except tk.TclError:
                    pass
        except Exception:
            pass

    def paste_text(self, widget):
        """Вставляет текст из буфера обмена"""
        try:
            text = self.root.clipboard_get()
            if isinstance(widget, tk.Entry):
                try:
                    start = widget.index("sel.first")
                    end = widget.index("sel.last")
                    widget.delete(start, end)
                    widget.insert(start, text)
                except tk.TclError:
                    widget.insert(tk.INSERT, text)
            elif isinstance(widget, (tk.Text, scrolledtext.ScrolledText)):
                try:
                    start = widget.index("sel.first")
                    end = widget.index("sel.last")
                    widget.delete(start, end)
                    widget.insert(tk.INSERT, text)
                except tk.TclError:
                    widget.insert(tk.INSERT, text)
        except Exception:
            pass

    def select_all(self, widget):
        """Выделяет весь текст в виджете"""
        widget.focus_set()
        if isinstance(widget, tk.Entry):
            widget.select_range(0, tk.END)
        elif isinstance(widget, (tk.Text, scrolledtext.ScrolledText)):
            widget.tag_add("sel", "1.0", tk.END)

    def browse_folder(self):
        path = filedialog.askdirectory(title="Выберите папку с PDF файлами")
        if path:
            self.folder_path.set(path)

    def start_processing_thread(self):
        # Проверка заполнения полей
        if not self.api_name.get().strip():
            messagebox.showerror("Ошибка", "Введите наименование API!")
            return
        if not self.model_name.get().strip():
            messagebox.showerror("Ошибка", "Введите наименование модели!")
            return
        if not self.api_key.get().strip():
            messagebox.showerror("Ошибка", "Введите API Key!")
            return
        if not self.folder_path.get().strip():
            messagebox.showerror("Ошибка", "Выберите путь к папке!")
            return

        # Проверка существования папки
        if not os.path.exists(self.folder_path.get()):
            messagebox.showerror("Ошибка", "Выбранная папка не существует!")
            return

        # Проверка наличия основного модуля обработки
        if process_main is None:
            messagebox.showerror("Ошибка", "Основной модуль обработки недоступен. Проверьте установку зависимостей.")
            return

        # Отключение кнопки
        self.run_button.config(state='disabled', text="Обработка...")

        # Очистка лога
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

        # Запуск в отдельном потоке
        thread = threading.Thread(
            target=self.run_processing,
            args=(self.api_name.get(), self.model_name.get(), self.api_key.get(), self.folder_path.get()),
            daemon=True
        )
        thread.start()

    def run_processing(self, api_name: str, model_name: str, api_key: str, folder_path: str):
        """Функция, выполняемая в отдельном потоке"""
        try:
            logger.info(f"🚀 Запуск обработки с API: {api_name}")
            logger.info(f"🧠 Модель: {model_name}")
            logger.info(f"🔑 API Key: {api_key[:5]}...")  # Показываем только начало ключа
            logger.info(f"📁 Папка: {folder_path}")

            # Установка переменных окружения в зависимости от выбранного API
            if "gemini" in api_name.lower() or "google" in api_name.lower():
                os.environ["GEMINI_API_KEY_AHMED"] = api_key
            elif "grok" in api_name.lower():
                os.environ["GROK_API_KEY"] = api_key
            elif "mistral" in api_name.lower():
                os.environ["MISTRAL_API_KEY"] = api_key
            elif "deepseek" in api_name.lower():
                os.environ["DEEPSEEK_API_KEY"] = api_key
            else:
                # По умолчанию используем переменную GEMINI_API_KEY_AHMED
                os.environ["GEMINI_API_KEY_AHMED"] = api_key

            # Проверка наличия основного модуля обработки
            if process_main is None:
                raise RuntimeError("Основной модуль обработки недоступен. Проверьте установку зависимостей.")

            # Создаем перенаправители для stdout и stderr
            stdout_redirector = OutputRedirector(self.log_text, "STDOUT")
            stderr_redirector = OutputRedirector(self.log_text, "STDERR")
            
            # Сохраняем оригинальные потоки
            original_stdout = sys.stdout
            original_stderr = sys.stderr
            
            # Перенаправляем потоки
            sys.stdout = stdout_redirector
            sys.stderr = stderr_redirector
            
            try:
                # Запуск асинхронной обработки
                asyncio.run(process_main(folder_path))
                logger.info("✅ Обработка завершена успешно!")
            finally:
                # Восстанавливаем оригинальные потоки
                sys.stdout = original_stdout
                sys.stderr = original_stderr

        except Exception as e:
            logger.error(f"❌ Ошибка при обработке: {str(e)}", exc_info=True)
        finally:
            # Восстанавливаем состояние кнопки
            def enable_button():
                self.run_button.config(state='normal', text="Запустить обработку")
            self.root.after(0, enable_button)

    def setup_logging(self):
        # Добавляем обработчик для текстового виджета
        text_handler = TextHandler(self.log_text)
        text_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        logger.addHandler(text_handler)
        
        # Также выводим логи в консоль
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        logger.addHandler(console_handler)

if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()