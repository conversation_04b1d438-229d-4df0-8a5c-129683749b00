import asyncio
import google.generativeai as genai
from dotenv import load_dotenv
import os


load_dotenv()
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY_PRESTIGE')
genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel("gemini-2.0-flash")

def translate_by_gemini_sync(text_to_translate: str) -> str:
    response = model.generate_content(
        contents="ЗАДАЧА: переведи текст на русский.\n\n" + text_to_translate
    )
    result  = response.text.replace("\n\n", "\n").replace("  ", " ").strip()
    return result


async def translate_by_gemini(text: str, target_language: str = "ru") -> str:
    """
    Асинхронная обертка для синхронной функции перевода Gemini.
    """
    if not text:
        return ""
    
    # Запуск синхронной функции в отдельном потоке, чтобы не блокировать asyncio event loop
    # Требует Python 3.9+
    try:
        translated_text = await asyncio.to_thread(translate_by_gemini_sync, text)
        return translated_text
    except Exception as e:
        # Этот блок перехватит ошибки, если сам asyncio.to_thread столкнется с проблемой,
        # или если _translate_by_gemini_sync выбросит исключение, не обработанное внутри.
        print(f"Error in async wrapper translate_by_gemini: {e}")
        return f"[Async Wrapper Error] Original: {text}"


# Пример для тестирования GeminiTranslate.py отдельно
if __name__ == "__main__":
    async def test_gemini_translate():
        sample_text = "See you later."
        print(f"Original: {sample_text}")
        translated = await translate_by_gemini(sample_text)
        print(f"Translated: {translated}")

    asyncio.run(test_gemini_translate())