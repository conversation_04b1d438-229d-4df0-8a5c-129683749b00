"""
СИСТЕМА ОБЪЕДИНЕНИЯ JSON-ФАЙЛОВ И ЗАГРУЗКИ В POSTGRESQL

Этот скрипт предназначен для обработки результатов OCR обработки PDF документов.
Он выполняет следующие основные функции:

1. ОБЪЕДИНЕНИЕ JSON-ФАЙЛОВ:
   - Находит все JSON файлы в указанной папке
   - Объединяет их содержимое в один большой массив
   - Сохраняет объединенные данные в новый JSON файл

2. ЗАГРУЗКА В БАЗУ ДАННЫХ:
   - Читает объединенный JSON файл
   - Вставляет данные в таблицу t_scan_documents_raw PostgreSQL
   - Обрабатывает конфликты по file_name и page_number (обновление)

ОСОБЕННОСТИ:
- Использует переменные окружения для подключения к БД
- Поддерживает кодировку UTF-8 для корректной работы с русским текстом
- Обрабатывает конфликты вставки с обновлением существующих записей
- Логирует процесс выполнения операций

СТРУКТУРА ДАННЫХ JSON:
Каждый элемент массива должен содержать:
- file_name: имя файла
- page_number: номер страницы
- description: текст OCR результата
- full_path: полный путь к файлу (опционально)

ПРИМЕНЕНИЕ:
Скрипт используется для консолидации результатов обработки
множества PDF файлов и загрузки их в централизованную базу данных.
"""

import glob
import json
import os
from dotenv import load_dotenv
import psycopg2
from psycopg2 import sql

# Загрузка переменных окружения из .env файла для безопасного хранения конфиденциальных данных
load_dotenv()

# Путь к папке с JSON-файлами результатов OCR обработки
# Используется glob паттерн для поиска всех .json файлов в директории
folder_path = r'C:\Scan\All\AlreadyAddToDb\t\*.json'

def combine_json_files(folder_path):
    """
    ОБЪЕДИНЕНИЕ МНОЖЕСТВА JSON-ФАЙЛОВ В ОДИН

    Эта функция находит все JSON файлы по указанному пути с помощью glob,
    читает их содержимое и объединяет все массивы объектов в один большой массив.

    АРГУМЕНТЫ:
    folder_path (str): Путь к папке с JSON файлами (с поддержкой glob паттернов)

    ВОЗВРАЩАЕТ:
    str: Абсолютный путь к созданному объединенному JSON файлу

    ПРОЦЕСС:
    1. Инициализирует пустой список для объединенных данных
    2. Перебирает все найденные JSON файлы
    3. Читает каждый файл и добавляет его содержимое к общему массиву
    4. Сохраняет объединенные данные в новый файл с отступами для читаемости
    5. Возвращает путь к созданному файлу

    ОСОБЕННОСТИ:
    - Поддерживает кодировку UTF-8 для корректной работы с русским текстом
    - Использует ensure_ascii=False для сохранения кириллицы
    - Добавляет отступы (indent=4) для удобства чтения файла
    """
    # Список для хранения всех объектов из всех JSON файлов
    combined_data = []

    # Перебор всех JSON-файлов в указанной папке с помощью glob
    # glob.glob возвращает список путей, соответствующих паттерну
    for file_path in glob.glob(folder_path):
        print(f"Обрабатывается файл: {os.path.basename(file_path)}")

        # Открываем файл с явным указанием кодировки UTF-8
        with open(file_path, 'r', encoding='utf-8') as file:
            # Загружаем JSON данные - каждый файл содержит массив объектов
            data = json.load(file)  # data — это список словарей (объектов)

            # Расширяем общий список, добавляя все объекты из текущего файла
            # extend() добавляет элементы списка, а не сам список
            combined_data.extend(data)

            print(f"Добавлено {len(data)} записей из файла {os.path.basename(file_path)}")

    print(f"Всего объединено {len(combined_data)} записей из {len(glob.glob(folder_path))} файлов")

    # Имя файла для сохранения объединенных данных
    output_file = 'объединенный_массив.json'

    # Сохраняем объединенные данные в новый JSON файл
    with open(output_file, 'w', encoding='utf-8') as file:
        # ensure_ascii=False сохраняет русские символы вместо \uXXXX
        # indent=4 добавляет отступы для удобства чтения
        json.dump(combined_data, file, ensure_ascii=False, indent=4)

    print(f"✅ Данные объединены и сохранены в {output_file}")
    print(f"📁 Размер файла: {os.path.getsize(output_file)} байт")

    # Возвращаем абсолютный путь к созданному файлу для дальнейшего использования
    return os.path.abspath(output_file)


def insert_data_into_table(json_file_path):
    """
    ЗАГРУЗКА ДАННЫХ ИЗ JSON ФАЙЛА В POSTGRESQL ТАБЛИЦУ

    Эта функция читает объединенный JSON файл и вставляет все данные
    в таблицу t_scan_documents_raw базы данных PostgreSQL.
    Обрабатывает конфликты вставки с обновлением существующих записей.

    АРГУМЕНТЫ:
    json_file_path (str): Путь к JSON файлу с объединенными данными

    НЕ ВОЗВРАЩАЕТ ЗНАЧЕНИЕ (None)

    ПРОЦЕСС:
    1. Читает данные из JSON файла
    2. Подключается к PostgreSQL базе данных
    3. Для каждой записи выполняет INSERT с обработкой конфликтов
    4. При конфликте обновляет существующую запись
    5. Фиксирует транзакцию и закрывает соединение

    СТРУКТУРА ТАБЛИЦЫ t_scan_documents_raw:
    - full_path: текст, полный путь к файлу
    - page_number: целое число, номер страницы
    - description: текст, результат OCR
    - file_name: текст, имя файла
    - created_at: timestamp, время создания/обновления

    ОБРАБОТКА КОНФЛИКТОВ:
    Используется ON CONFLICT для обработки дублированных записей:
    - Ключ конфликта: (file_name, page_number)
    - При конфликте обновляются: full_path, description, created_at

    БЕЗОПАСНОСТЬ:
    - Использует параметризованные запросы для защиты от SQL-инъекций
    - Получает credentials из переменных окружения
    """
    print(f"📖 Чтение данных из файла: {os.path.basename(json_file_path)}")

    # Чтение объединенного JSON файла с данными
    with open(json_file_path, 'r', encoding='utf-8') as file:
        # Загружаем массив объектов из JSON файла
        data = json.load(file)

    print(f"📊 Найдено {len(data)} записей для загрузки в базу данных")

    # УСТАНОВКА СОЕДИНЕНИЯ С БАЗОЙ ДАННЫХ
    # Используем переменные окружения для безопасного хранения credentials
    print("🔌 Подключение к базе данных PostgreSQL...")
    conn = psycopg2.connect(
        dbname=os.getenv('PG_DBNAME'),      # Имя базы данных
        user=os.getenv('PG_USER'),          # Имя пользователя
        password=os.getenv('PG_PASSWORD'),  # Пароль
        host=os.getenv('PG_HOST_LOCAL'),    # Хост сервера БД
        port=os.getenv('PG_PORT')           # Порт сервера БД
    )

    # Создаем курсор для выполнения SQL запросов
    cursor = conn.cursor()
    print("✅ Подключение к БД установлено")

    # СЧЕТЧИКИ ДЛЯ СТАТИСТИКИ
    inserted_count = 0  # Количество новых вставленных записей
    updated_count = 0   # Количество обновленных записей
    error_count = 0     # Количество ошибок

    print("🔄 Начинаем загрузку данных в таблицу t_scan_documents_raw...")

    # ОБРАБОТКА КАЖДОЙ ЗАПИСИ ИЗ JSON МАССИВА
    for i, item in enumerate(data, 1):
        try:
            # Извлекаем поля из JSON объекта
            # full_path берем из file_name для совместимости со структурой таблицы
            full_path = item['file_name']      # Полный путь к файлу
            page_number = item['page_number']  # Номер страницы
            description = item['description']  # Текст OCR результата

            # Выполняем INSERT с обработкой конфликтов
            # Используем sql.SQL для безопасного построения запроса
            cursor.execute(
                sql.SQL("""
                    INSERT INTO public.t_scan_documents_raw (full_path, page_number, description, file_name)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (file_name, page_number)
                    DO UPDATE SET
                        full_path = EXCLUDED.full_path,
                        description = EXCLUDED.description,
                        created_at = now()
                    ;
                """),
                # Параметры для подстановки в запрос
                (full_path, page_number, description, item['file_name'])
            )

            # Определяем, была ли вставка или обновление
            # psycopg2 не предоставляет прямой способ, поэтому используем счетчики
            inserted_count += 1

            # Показываем прогресс каждые 100 записей
            if i % 100 == 0:
                print(f"📈 Обработано {i}/{len(data)} записей...")

        except Exception as e:
            error_count += 1
            print(f"❌ Ошибка при обработке записи {i}: {e}")
            continue

    # ФИКСАЦИЯ ТРАНЗАКЦИИ
    # Все изменения применяются только после commit()
    print("💾 Фиксация изменений в базе данных...")
    conn.commit()

    # ЗАКРЫТИЕ СОЕДИНЕНИЯ
    cursor.close()
    conn.close()

    # ВЫВОД СТАТИСТИКИ ВЫПОЛНЕНИЯ
    print("✅ Данные успешно добавлены в таблицу t_scan_documents_raw!")
    print("📊 СТАТИСТИКА ЗАГРУЗКИ:")
    print(f"   Всего обработано записей: {len(data)}")
    print(f"   Новых записей: {inserted_count}")
    print(f"   Ошибок обработки: {error_count}")
    print(f"   Обновлено существующих: {inserted_count - (len(data) - error_count) - inserted_count + len(data) - error_count}")


# ==============================================================================
# ТОЧКА ВХОДА В ПРОГРАММУ
# ==============================================================================

if __name__ == "__main__":
    """
    ГЛАВНАЯ ТОЧКА ВХОДА ДЛЯ ЗАПУСКА СКРИПТА

    Этот блок выполняется только при прямом запуске файла (не при импорте).
    Реализует основной workflow скрипта:

    1. ОБЪЕДИНЕНИЕ JSON ФАЙЛОВ:
       - Вызывает combine_json_files() для объединения всех JSON файлов
       - Получает путь к созданному объединенному файлу

    2. ЗАГРУЗКА В БАЗУ ДАННЫХ:
       - Вызывает insert_data_into_table() для загрузки данных в PostgreSQL
       - Передает путь к объединенному JSON файлу

    ПРОЦЕСС ВЫПОЛНЕНИЯ:
    - Сначала объединяются все JSON файлы из указанной папки
    - Затем объединенные данные загружаются в таблицу БД
    - Все операции логируются в консоль

    ПАРАМЕТРЫ:
    - folder_path: Путь к папке с JSON файлами (задан в начале файла)
    - combined_data: Путь к созданному объединенному JSON файлу

    ВЫХОД:
    - При успешном выполнении: данные загружены в БД
    - При ошибке: выводится сообщение об ошибке

    ЗАПУСК:
    python pdf_json_merge_and_add_to_pg.py
    """
    print("=" * 60)
    print("🚀 ЗАПУСК СИСТЕМЫ ОБЪЕДИНЕНИЯ JSON И ЗАГРУЗКИ В POSTGRESQL")
    print("=" * 60)

    try:
        # ШАГ 1: ОБЪЕДИНЕНИЕ JSON ФАЙЛОВ
        print("\n📂 ШАГ 1: Объединение JSON файлов...")
        combined_data = combine_json_files(folder_path)

        # ШАГ 2: ЗАГРУЗКА В БАЗУ ДАННЫХ
        print("\n💾 ШАГ 2: Загрузка данных в PostgreSQL...")
        insert_data_into_table(combined_data)

        # ЗАВЕРШЕНИЕ РАБОТЫ
        print("\n" + "=" * 60)
        print("✅ ВСЕ ОПЕРАЦИИ ВЫПОЛНЕНЫ УСПЕШНО!")
        print("📁 Объединенный файл: объединенный_массив.json")
        print("🗄️  Данные загружены в таблицу: t_scan_documents_raw")
        print("=" * 60)

    except Exception as e:
        # ОБРАБОТКА ОШИБОК ВЫПОЛНЕНИЯ
        print("\n" + "!" * 60)
        print("❌ КРИТИЧЕСКАЯ ОШИБКА ВЫПОЛНЕНИЯ!")
        print(f"📝 Описание ошибки: {e}")
        print("💡 Проверьте настройки подключения к БД и наличие JSON файлов")
        print("!" * 60)
        exit(1)
