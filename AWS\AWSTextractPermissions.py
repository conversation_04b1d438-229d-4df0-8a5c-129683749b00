import boto3
import json
from botocore.exceptions import ClientError
from dotenv import load_dotenv
import os

# Загружаем переменные окружения из .env файла
load_dotenv()

def get_current_username():
    """Автоматическое определение имени текущего пользователя"""
    sts = boto3.client('sts')
    try:
        response = sts.get_caller_identity()
        arn = response['Arn']
        # Парсим ARN формата: arn:aws:iam::************:user/username
        return arn.split('/')[-1]
    except Exception as e:
        print(f"Ошибка получения имени пользователя: {e}")
        return None

def get_textract_policy_arn():
    """Получение ARN управляемой политики AWS для Textract"""
    # Используем готовую управляемую политику AWS
    return "arn:aws:iam::aws:policy/AmazonTextractFullAccess"

def get_account_id():
    """Получение ID аккаунта AWS"""
    sts = boto3.client('sts')
    try:
        return sts.get_caller_identity()['Account']
    except Exception as e:
        print(f"Ошибка получения ID аккаунта: {e}")
        return None

def attach_policy_to_user(username, policy_arn):
    """Прикрепление политики к пользователю"""
    iam = boto3.client('iam')

    try:
        # Проверяем, прикреплена ли уже политика к пользователю
        attached_policies = iam.list_attached_user_policies(UserName=username)
        for policy in attached_policies['AttachedPolicies']:
            if policy['PolicyArn'] == policy_arn:
                print(f"Политика {policy_arn} уже прикреплена к пользователю {username}")
                return True

        # Если политика еще не прикреплена, прикрепляем ее
        iam.attach_user_policy(
            UserName=username,
            PolicyArn=policy_arn
        )
        print(f"Политика {policy_arn} успешно прикреплена к пользователю {username}")
        return True
    except ClientError as e:
        print(f"Ошибка при прикреплении политики к пользователю: {e}")
        return False

def main():
    # Получаем имя текущего пользователя
    username = get_current_username()
    if not username:
        print("Не удалось определить имя пользователя")
        return

    print(f"Настройка прав доступа к AWS Textract для пользователя: {username}")

    # Получаем ARN управляемой политики для Textract
    policy_arn = get_textract_policy_arn()
    print(f"Используем управляемую политику AWS: {policy_arn}")

    # Прикрепляем политику к пользователю
    if attach_policy_to_user(username, policy_arn):
        print(f"Пользователь {username} теперь имеет доступ к AWS Textract")
        print("Вы можете запустить AWS_OCR.py для распознавания текста")
    else:
        print("Не удалось настроить права доступа")

if __name__ == "__main__":
    main()
