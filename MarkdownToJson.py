# Установить последнюю версию с github
# pip install git+https://github.com/njvack/markdown-to-json/


import sys
import subprocess
import json
import re
import markdown_to_json


# Проверка и установка необходимых модулей
required_modules = ["re", "json"]
for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        print(f"Модуль '{module}' не найден. Устанавливаю...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", module])



def markdown_to_json(text):
    # Удаляем обёртку markdown, если она есть
    try:
        if type(text) == dict or type(text) == list:
            return text
        
        cleaned_text = text.strip().replace('```','').replace('\n','').replace('  ',' ').replace('json{','{').replace('\\','')
        clean_str_fixed = re.sub(r'(?<!\\)[\n\r]+', ' ', cleaned_text)
        return json.loads(clean_str_fixed)
    except json.JSONDecodeError:
        return None

if __name__ == "__main__":
    # Пример использования
    markdown_text = '```{\n  "buyer": {\n    "name": "Товариство з обмеженою відповідальністю \\"Епіцентр К\\"",\n    "code": "32490244"\n  },\n  "documents": [\n    {\n      "type": "Товарно-транспортна накладна",\n      "date": null,\n      "number": "11033538"\n    },\n    {\n      "type": "Видаткова накладна",\n      "date": null,\n      "number": "10"\n    }\n  ]\n}\n```'

    print(markdown_to_json(markdown_text))
