import base64


def decode_base64_to_pdf(base64_string, output_file):
    pdf_data = base64.b64decode(base64_string)
    with open(output_file, "wb") as pdf_file:
        pdf_file.write(pdf_data)


def encode_pdf_to_base64(input_file):
    with open(input_file, "rb") as pdf_file:
        base64_encoded = base64.b64encode(pdf_file.read()).decode("utf-8")
    return base64_encoded


if __name__ == "__main__":
    file_name = r"D:\Scan\Эпицентр\2024\ВЕРЕСЕНЬ 2024\202409 Scan_3.pdf"
    base64_string = encode_pdf_to_base64(file_name)
    print(base64_string)
