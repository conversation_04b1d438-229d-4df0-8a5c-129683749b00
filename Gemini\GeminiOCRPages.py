import google.generativeai as genai
import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import time
import fitz  # PyMuPDF
import numpy as np
import datetime  # <--- Добавлен импорт datetime

# --- Загрузка переменных окружения ---
load_dotenv()

# --- Очистка консоли (опционально) ---
if os.name == "nt":
    os.system("cls")
else:
    os.system("clear")

# --- Настройки API и Путей ---
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY_PRESTIGE')

if not GEMINI_API_KEY:
    raise ValueError("Необходимо установить переменную окружения GEMINI_API_KEY.")

MODEL_NAME = os.getenv("GEMINI_MODEL_2F")  # Рекомендовано как более стабильная опция
# MODEL_NAME = 'gemini-2.0-flash'  # Если вы предпочитаете эту модель

PDF_INPUT_DIRECTORY = r'd:\Scan\Эпицентр\2024\ForParse'  # Папка с PDF файлами
TEMP_IMAGE_FILENAME = "temp_page_image.png"  # Имя временного файла для изображения страницы
ERROR_LOG_FILE = "pdf_extraction_errors.log"  # Имя файла для лога ошибок


# --- Функция для логирования ---
def log_extraction_message(message, level="INFO", pdf_path_context=None):
    """
    Записывает сообщение в лог-файл и выводит в консоль в зависимости от уровня.
    """
    timestamp = datetime.datetime.now().strftime("%d.%m.%Y %H:%M:%S")

    log_entry_for_file = f"{timestamp} | {level.upper()}"
    if pdf_path_context:  # Добавляем путь к файлу в лог, если он предоставлен
        log_entry_for_file += f" | File: {os.path.basename(pdf_path_context)}"
    log_entry_for_file += f" | {message}\n"

    console_output = f"{timestamp} | {level.upper()}"
    if pdf_path_context:
        console_output += f" | File: {os.path.basename(pdf_path_context)}"
    console_output += f" | {message}"

    print(console_output)  # Всегда выводим в консоль для отслеживания

    # Записываем в лог все уровни, чтобы иметь полную историю
    try:
        with open(ERROR_LOG_FILE, "a", encoding="utf-8") as f:
            f.write(log_entry_for_file)
    except Exception as e_log:
        # Если не удалось записать в основной лог, выводим критическую ошибку в консоль
        print(f"{timestamp} | CRITICAL | Не удалось записать в лог-файл {ERROR_LOG_FILE}: {e_log}")


# --- Функция локальной проверки на пустую страницу ---
def is_blank_page_local(page, threshold=0.99, white_level=250):
    pix = page.get_pixmap(matrix=fitz.Identity, colorspace=fitz.csGRAY, alpha=False)
    if not pix.samples or len(pix.samples) == 0:
        return True
    try:
        pixels = np.frombuffer(pix.samples, dtype=np.uint8)
    except ValueError:
        return False
    if pixels.size == 0:
        return True
    total_pixels = pixels.size
    white_pixels = np.sum(pixels >= white_level)
    blank_ratio = white_pixels / total_pixels
    return blank_ratio >= threshold


def find_pdf_files(directory):
    pdf_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.pdf'):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(root, directory)
                pdf_files.append((full_path, file, rel_path))
    return pdf_files


def extract_text_from_pdf_pages_with_gemini(pdf_path):
    base_pdf_name = os.path.basename(pdf_path)
    log_extraction_message(f"Начало обработки файла постранично.", "INFO", pdf_path)
    all_pages_content = []
    failed_extraction_page_details = []  # Собираем детали для статистики

    doc = None  # Инициализируем для finally
    model_instance = None  # Инициализируем модель здесь

    try:
        doc = fitz.open(pdf_path)
        total_pages = doc.page_count

        if total_pages == 0:
            log_extraction_message("PDF-файл не содержит страниц.", "WARNING", pdf_path)
            return "--- PDF-ФАЙЛ ПУСТ ИЛИ НЕ СОДЕРЖИТ СТРАНИЦ ---", []

        for page_num_zero_based in range(total_pages):
            page_num_one_based = page_num_zero_based + 1
            log_extraction_message(f"Обработка страницы {page_num_one_based}/{total_pages}...", "INFO", pdf_path)

            separator = f"***** {page_num_one_based}/{total_pages} *****\n"
            all_pages_content.append(separator)

            page = doc.load_page(page_num_zero_based)
            page_text_content = ""

            if is_blank_page_local(page):
                log_extraction_message(f"Страница {page_num_one_based} определена как пустая локально.", "INFO",
                                       pdf_path)
                page_text_content = "--- ПУСТАЯ СТРАНИЦА (локально) ---"
            else:
                log_extraction_message(f"Страница {page_num_one_based} не пустая локально, отправка в Gemini API...",
                                       "INFO", pdf_path)
                if model_instance is None:
                    try:
                        model_instance = genai.GenerativeModel(MODEL_NAME)
                        log_extraction_message(f"Модель Gemini '{MODEL_NAME}' успешно инициализирована.", "INFO",
                                               pdf_path)
                    except Exception as e_model_init:
                        msg = f"Критическая ошибка: Не удалось инициализировать модель Gemini '{MODEL_NAME}': {e_model_init}"
                        log_extraction_message(msg, "ERROR", pdf_path)
                        # Если модель не инициализирована, нет смысла продолжать для этого файла
                        return f"--- ОШИБКА ИНИЦИАЛИЗАЦИИ МОДЕЛИ GEMINI: {e_model_init} ---", failed_extraction_page_details

                pix = page.get_pixmap(dpi=300)
                pix.save(TEMP_IMAGE_FILENAME)
                uploaded_image_file = None

                try:
                    log_extraction_message(f"Загрузка изображения страницы {page_num_one_based}...", "INFO", pdf_path)
                    uploaded_image_file = genai.upload_file(
                        path=TEMP_IMAGE_FILENAME,
                        display_name=f"page_{page_num_one_based}_{base_pdf_name}"
                    )
                    time.sleep(2)  # Небольшая задержка после загрузки

                    prompt = [
                        "Тебе дается отсканированный документ в формате pdf. Возможно некоторые страницы будут повернуты, качество сканирования будет плохим.",
                        "Твоя задача - извлечь текст со страницы.",
                        "Если страница полностью пустая (например, белый лист), напиши: --- ПУСТАЯ СТРАНИЦА ---",
                        "Если страница непустая, и ты не можешь извлечь текст, поменяй ориентиры и попробуй еще раз.",
                        "Поворачивай до тех пор, пока не извлечешь текст.",
                        "Если текст не найден или не может быть извлечен по другой причине, напиши: --- ТЕКСТ НЕ ИЗВЛЕЧЕН (Gemini) ---",
                        "Начинай с нумерации страницы: ***", page_num_one_based, "/", total_pages, "***",
                        uploaded_image_file
                    ]

                    log_extraction_message(f"Отправка запроса Gemini для страницы {page_num_one_based}...", "INFO",
                                           pdf_path)
                    response = model_instance.generate_content(prompt, request_options={'timeout': 120})

                    page_text_content = ""  # Сбрасываем перед получением ответа
                    if response.parts and response.text:
                        page_text_content = response.text.strip()

                    is_explicitly_empty_by_gemini = "--- ПУСТАЯ СТРАНИЦА ---" in page_text_content.upper()
                    is_explicitly_not_extracted_by_gemini = "--- ТЕКСТ НЕ ИЗВЛЕЧЕН (GEMINI) ---" in page_text_content.upper()

                    if not page_text_content or is_explicitly_not_extracted_by_gemini:
                        if not page_text_content:  # Если Gemini вернул совсем пустой ответ
                            page_text_content = "--- ТЕКСТ НЕ ИЗВЛЕЧЕН (Gemini: пустой ответ) ---"
                        log_extraction_message(
                            f"Текст со страницы {page_num_one_based} не извлечен (Gemini) или помечен как не извлекаемый.",
                            "WARNING", pdf_path)
                        if not is_explicitly_empty_by_gemini:  # Не считаем ошибкой, если Gemini явно сказал, что пусто
                            failed_extraction_page_details.append(
                                (base_pdf_name, page_num_one_based, total_pages,
                                 "Текст не извлечен или помечен как не извлекаемый Gemini")
                            )
                    elif is_explicitly_empty_by_gemini:
                        log_extraction_message(f"Страница {page_num_one_based} определена как пустая (Gemini).", "INFO",
                                               pdf_path)
                    else:
                        log_extraction_message(f"Текст со страницы {page_num_one_based} успешно извлечен (Gemini).",
                                               "INFO", pdf_path)

                except Exception as e_page:
                    page_error_msg = f"Ошибка обработки страницы {page_num_one_based} (Gemini): {str(e_page).splitlines()[0]}"
                    page_text_content = f"--- {page_error_msg} ---"
                    log_extraction_message(page_error_msg, "ERROR", pdf_path)
                    failed_extraction_page_details.append(
                        (base_pdf_name, page_num_one_based, total_pages, page_error_msg))
                finally:
                    if uploaded_image_file:
                        try:
                            genai.delete_file(uploaded_image_file.name)
                        except Exception as e_del:
                            log_extraction_message(
                                f"Предупреждение: Не удалось удалить файл изображения '{uploaded_image_file.name}' с сервера: {e_del}",
                                "WARNING", pdf_path)
                    if os.path.exists(TEMP_IMAGE_FILENAME):
                        try:
                            os.remove(TEMP_IMAGE_FILENAME)
                        except Exception as e_del_temp:
                            log_extraction_message(
                                f"Предупреждение: Не удалось удалить временный файл изображения '{TEMP_IMAGE_FILENAME}': {e_del_temp}",
                                "WARNING", pdf_path)

            all_pages_content.append(page_text_content)
            all_pages_content.append("\n")

        full_text = "".join(all_pages_content)
        if full_text.endswith("\n"):
            full_text = full_text[:-1]  # Убираем последний перевод строки, если есть
        return full_text, failed_extraction_page_details

    except (RuntimeError, fitz.FileDataError) as e_fitz:  # Исправлено на правильные исключения PyMuPDF
        error_msg = f"Ошибка PyMuPDF: Не удалось обработать файл. Возможно, файл поврежден/защищен. Детали: {e_fitz}"
        log_extraction_message(error_msg, "ERROR", pdf_path)
        return f"--- {error_msg} ---", failed_extraction_page_details  # Возвращаем failed_extraction_page_details, даже если ошибка файла
    except Exception as e:
        error_msg = f"Непредвиденная ошибка при обработке файла: {e}"
        log_extraction_message(error_msg, "ERROR", pdf_path)
        return f"--- {error_msg} ---", failed_extraction_page_details
    finally:
        if doc:
            try:
                doc.close()
            except Exception as e_doc_close:
                log_extraction_message(f"Ошибка при закрытии PDF документа: {e_doc_close}", "WARNING", pdf_path)
        if os.path.exists(TEMP_IMAGE_FILENAME):  # Гарантированно удаляем временный файл, если он остался
            try:
                os.remove(TEMP_IMAGE_FILENAME)
            except Exception as e_del_temp_fin:
                log_extraction_message(
                    f"Предупреждение: Не удалось удалить временный файл '{TEMP_IMAGE_FILENAME}' в блоке finally: {e_del_temp_fin}",
                    "WARNING", pdf_path)


# --- Основная часть скрипта ---
if __name__ == "__main__":
    # Создаем/очищаем лог-файл при старте, если это необходимо, или просто открываем в режиме 'a'
    # Для простоты, log_extraction_message будет создавать его при первой записи, если его нет.
    # Можно добавить явное создание/очистку здесь:
    # with open(ERROR_LOG_FILE, "w", encoding="utf-8") as f:
    #    f.write(f"{datetime.datetime.now().strftime('%d.%m.%Y %H:%M:%S')} | INFO | --- Начало сессии логирования --- \n")

    genai.configure(api_key=GEMINI_API_KEY)
    log_extraction_message(f"--- Запуск скрипта обработки PDF. Директория: {PDF_INPUT_DIRECTORY} ---", "INFO")

    if not os.path.isdir(PDF_INPUT_DIRECTORY):
        log_extraction_message(f"Ошибка: Директория для PDF '{PDF_INPUT_DIRECTORY}' не найдена.", "ERROR")
        sys.exit(1)

    pdf_files_found = find_pdf_files(PDF_INPUT_DIRECTORY)
    all_failed_pages_globally_summary = []

    if not pdf_files_found:
        log_extraction_message(f"В директории '{PDF_INPUT_DIRECTORY}' и её подпапках не найдено PDF файлов.", "WARNING")
    else:
        log_extraction_message(f"Найдено PDF файлов для обработки: {len(pdf_files_found)}", "INFO")
        processed_files_count = 0

        for full_pdf_path, pdf_file_name, rel_path in pdf_files_found:
            log_extraction_message(f"--- Обработка файла: {pdf_file_name} (Путь: {full_pdf_path}) ---", "INFO")

            # Формирование пути для сохранения .txt файла
            output_base_dir = Path(
                PDF_INPUT_DIRECTORY) / "pdf_extracted_text_pages"  # Корень для всех извлеченных текстов
            current_output_dir = output_base_dir / rel_path  # Сохраняем относительную структуру подпапок
            current_output_dir.mkdir(parents=True, exist_ok=True)  # Создаем, если нет

            output_txt_filename = os.path.splitext(pdf_file_name)[0] + "_pages.txt"
            output_txt_path = current_output_dir / output_txt_filename

            extracted_content, failed_pages_for_current_file = extract_text_from_pdf_pages_with_gemini(full_pdf_path)
            if failed_pages_for_current_file:  # Если были ошибки на страницах этого файла
                all_failed_pages_globally_summary.extend(failed_pages_for_current_file)

            try:
                with open(output_txt_path, 'w', encoding='utf-8') as f_out:
                    f_out.write(extracted_content)
                log_extraction_message(f"Извлеченный текст сохранен в: {output_txt_path}", "INFO", full_pdf_path)

                # Обновляем счетчик успешно обработанных файлов
                # Успешной обработкой считаем, если не было критических ошибок на уровне файла
                if not extracted_content.startswith("--- ОШИБКА PYMUPDF") and \
                        not extracted_content.startswith("--- НЕПРЕДВИДЕННАЯ ОШИБКА") and \
                        not extracted_content.startswith("--- PDF-ФАЙЛ ПУСТ") and \
                        not extracted_content.startswith("--- ОШИБКА ИНИЦИАЛИЗАЦИИ МОДЕЛИ GEMINI"):
                    processed_files_count += 1
            except IOError as e_io:
                log_extraction_message(f"Ошибка записи в файл {output_txt_path}: {e_io}", "ERROR", full_pdf_path)

            time.sleep(1)  # Небольшая задержка между файлами

        log_extraction_message(
            f"--- Обработка PDF завершена. Успешно (полностью или частично) обработано файлов: {processed_files_count} из {len(pdf_files_found)}. ---",
            "INFO")
        log_extraction_message(
            f"--- Результаты сохранены в подпапках 'pdf_extracted_text_pages' внутри '{PDF_INPUT_DIRECTORY}' ---",
            "INFO")

        if all_failed_pages_globally_summary:
            log_extraction_message("--- Статистика по страницам с проблемами извлечения (Gemini) или ошибками API: ---",
                                   "WARNING")
            # Группируем ошибки по файлам для более наглядного вывода
            errors_by_file = {}
            for pdf_name, page_num, total_pgs, detail in all_failed_pages_globally_summary:
                if pdf_name not in errors_by_file:
                    errors_by_file[pdf_name] = []
                errors_by_file[pdf_name].append(f"Стр. {page_num}/{total_pgs} - {detail}")

            for pdf_name, errors_list in errors_by_file.items():
                log_extraction_message(f"Файл: {pdf_name}", "WARNING")
                for error_detail_str in errors_list:
                    log_extraction_message(f"{error_detail_str}", "WARNING")
        else:
            log_extraction_message(
                "--- Проблем с извлечением текста со страниц (требующих вмешательства Gemini) не зафиксировано (кроме возможно пустых страниц). ---",
                "INFO")

    log_extraction_message(f"--- Сессия завершена. Лог ошибок сохранен в: {ERROR_LOG_FILE} ---", "INFO")
    print(f"\nЛог ошибок сохранен в: {os.path.abspath(ERROR_LOG_FILE)}")