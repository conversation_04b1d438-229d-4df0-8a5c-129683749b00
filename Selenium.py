from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
import time
import pyperclip


def automate_gemini():
    # Настройки Chrome
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    # Убираем headless режим, чтобы использовать уже открытый браузер
    # chrome_options.add_argument("--headless")

    try:
        # Подключаемся к уже запущенному Chrome
        # Если браузер уже открыт, можно использовать remote debugging
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        driver = webdriver.Chrome(options=chrome_options)

        print("Переходим на Gemini...")
        driver.get("https://gemini.google.com/app")

        # Ждем загрузки страницы
        wait = WebDriverWait(driver, 10)

        # Ищем поле ввода сообщения
        # Возможные селекторы для поля ввода
        input_selectors = [
            "textarea[placeholder*='Введите сообщение']",
            "textarea[placeholder*='Enter a prompt']",
            "textarea[data-testid='chat-input']",
            "textarea[aria-label*='Message']",
            "div[contenteditable='true']",
            "textarea"
        ]

        message_input = None
        for selector in input_selectors:
            try:
                message_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                print(f"Найдено поле ввода: {selector}")
                break
            except:
                continue

        if not message_input:
            print("Не удалось найти поле ввода. Попробуем найти любое текстовое поле...")
            # Альтернативный поиск
            try:
                message_input = driver.find_element(By.TAG_NAME, "textarea")
            except:
                print("Поле ввода не найдено. Проверьте страницу вручную.")
                return

        # Очищаем поле и вводим сообщение
        message_input.clear()
        message_input.send_keys("Как дела")

        print("Сообщение введено. Отправляем...")

        # Отправляем сообщение (Enter или поиск кнопки отправки)
        message_input.send_keys(Keys.RETURN)

        # Ждем ответа
        print("Ждем ответа от Gemini...")
        time.sleep(3)

        # Ждем ответа
        print("Ждем ответа от Gemini...")
        time.sleep(3)

        # Ждем появления ответа (пока идет генерация, может быть анимация)
        max_wait = 30  # Максимум 30 секунд
        start_time = time.time()

        while time.time() - start_time < max_wait:
            # Проверяем, есть ли кнопки действий (копировать, лайк, дизлайк)
            try:
                copy_buttons = driver.find_elements(By.XPATH,
                                                    "//button[@title='Копировать' or @aria-label='Копировать' or contains(@class, 'copy')]")
                if copy_buttons:
                    print("Найдена кнопка копирования - ответ готов")
                    break

                # Или ищем группу кнопок действий (лайк, дизлайк, копировать и т.д.)
                action_buttons = driver.find_elements(By.XPATH,
                                                      "//div[count(button) >= 3 and .//button[contains(@aria-label, 'копировать') or contains(@title, 'копировать')]]")
                if action_buttons:
                    print("Найдена группа кнопок действий")
                    break

            except:
                pass

            time.sleep(1)

        # Ищем кнопку копирования
        copy_button = None
        copy_selectors = [
            "//button[@title='Копировать']",
            "//button[@aria-label='Копировать']",
            "//button[contains(@aria-label, 'Copy')]",
            "//button[contains(@title, 'Copy')]",
            "//button[contains(@class, 'copy')]",
            "//button[.//svg[contains(@class, 'copy')]]",
            # Ищем кнопку по позиции - обычно третья или четвертая в группе
            "//div[count(button) >= 4]/button[3]",
            "//div[count(button) >= 5]/button[4]"
        ]

        print("Ищем кнопку копирования...")
        for selector in copy_selectors:
            try:
                buttons = driver.find_elements(By.XPATH, selector)
                if buttons:
                    # Берем последнюю найденную кнопку (самый новый ответ)
                    copy_button = buttons[-1]
                    print(f"Найдена кнопка через селектор: {selector}")
                    break
            except:
                continue

        # Если не нашли по прямым селекторам, ищем по группе кнопок
        if not copy_button:
            print("Ищем группу кнопок действий...")
            try:
                # Ищем все группы кнопок (обычно 4-6 кнопок: лайк, дизлайк, копировать, поделиться, и т.д.)
                button_groups = driver.find_elements(By.XPATH, "//div[count(button) >= 4 and count(button) <= 7]")

                for group in reversed(button_groups):  # Идем с конца (новые сообщения)
                    try:
                        # Проверяем, что это группа не в сайдбаре
                        location = group.location
                        if location['x'] > 200:  # Справа от сайдбара
                            buttons = group.find_elements(By.TAG_NAME, "button")
                            if len(buttons) >= 4:
                                # Обычно кнопка копирования 3-я или 4-я
                                # Попробуем найти по иконке или атрибутам
                                for btn in buttons:
                                    try:
                                        # Проверяем атрибуты кнопки
                                        aria_label = btn.get_attribute('aria-label') or ''
                                        title = btn.get_attribute('title') or ''
                                        class_name = btn.get_attribute('class') or ''

                                        if ('copy' in aria_label.lower() or
                                                'copy' in title.lower() or
                                                'копировать' in aria_label.lower() or
                                                'копировать' in title.lower() or
                                                'copy' in class_name.lower()):
                                            copy_button = btn
                                            print("Найдена кнопка копирования в группе")
                                            break
                                    except:
                                        continue

                                # Если не нашли по атрибутам, берем кнопку по позиции
                                if not copy_button and len(buttons) >= 5:
                                    copy_button = buttons[4]  # 5-я кнопка часто копирование
                                    print("Выбрана кнопка по позиции (5-я)")
                                elif not copy_button and len(buttons) >= 4:
                                    copy_button = buttons[3]  # 4-я кнопка
                                    print("Выбрана кнопка по позиции (4-я)")

                                if copy_button:
                                    break
                    except:
                        continue

            except Exception as e:
                print(f"Ошибка поиска группы кнопок: {e}")

        # Нажимаем кнопку копирования
        if copy_button:
            try:
                print("Нажимаем кнопку копирования...")

                # Скроллим к кнопке если нужно
                driver.execute_script("arguments[0].scrollIntoView(true);", copy_button)
                time.sleep(0.5)

                # Нажимаем кнопку
                copy_button.click()
                time.sleep(1)

                print("Кнопка копирования нажата!")

                # Получаем содержимое буфера обмена
                try:
                    response_text = pyperclip.paste()
                    if response_text and len(response_text) > 5:
                        print(f"Получен ответ из буфера обмена:")
                        print(f"'{response_text[:200]}{'...' if len(response_text) > 200 else ''}'")
                        return response_text
                    else:
                        print("Буфер обмена пуст или содержит слишком мало текста")
                except Exception as e:
                    print(f"Ошибка чтения буфера обмена: {e}")

            except Exception as e:
                print(f"Ошибка нажатия кнопки копирования: {e}")
                # Попробуем альтернативный способ клика
                try:
                    driver.execute_script("arguments[0].click();", copy_button)
                    time.sleep(1)
                    response_text = pyperclip.paste()
                    if response_text:
                        print("Альтернативный клик сработал!")
                        return response_text
                except:
                    print("Альтернативный клик тоже не сработал")
        else:
            print("Кнопка копирования не найдена")

            # Отладка - показываем все найденные группы кнопок
            try:
                all_button_groups = driver.find_elements(By.XPATH, "//div[count(button) >= 3]")
                print(f"Найдено групп с кнопками: {len(all_button_groups)}")

                for i, group in enumerate(all_button_groups[-3:]):  # Показываем последние 3 группы
                    try:
                        location = group.location
                        buttons = group.find_elements(By.TAG_NAME, "button")
                        print(f"Группа {i + 1}: позиция x={location['x']}, кнопок={len(buttons)}")

                        for j, btn in enumerate(buttons):
                            try:
                                aria_label = btn.get_attribute('aria-label') or 'нет'
                                title = btn.get_attribute('title') or 'нет'
                                print(f"  Кнопка {j + 1}: aria-label='{aria_label}', title='{title}'")
                            except:
                                pass
                    except:
                        pass

            except Exception as e:
                print(f"Ошибка отладки: {e}")

        return None



    except Exception as e:
        print(f"Ошибка: {e}")
        print("Убедитесь, что:")
        print("1. Chrome запущен с флагом --remote-debugging-port=9222")
        print("2. Вы авторизованы в Google")
        print("3. Страница Gemini загрузилась корректно")

    finally:
        # Не закрываем браузер, оставляем открытым
        # driver.quit()
        pass


def setup_chrome_for_automation():
    """Инструкция по настройке Chrome для автоматизации"""
    print("=== НАСТРОЙКА CHROME ===")
    print("Для работы скрипта выполните следующие шаги:")
    print("1. Закройте все окна Chrome")
    print("2. Запустите Chrome с командой:")
    print("   chrome.exe --remote-debugging-port=9222 --user-data-dir='C:\\temp\\chrome_dev'")
    print("   (на Windows)")
    print("   или")
    print("   google-chrome --remote-debugging-port=9222 --user-data-dir='/tmp/chrome_dev'")
    print("   (на Linux/Mac)")
    print("3. Откройте https://gemini.google.com/app и авторизуйтесь")
    print("4. Запустите этот скрипт")
    print("=" * 50)


if __name__ == "__main__":
    print("Автоматизация работы с Gemini")
    print("-" * 30)

    # Показываем инструкцию по настройке
    setup_chrome_for_automation()

    input("Нажмите Enter после настройки Chrome...")

    # Запускаем автоматизацию
    result = automate_gemini()

    if result:
        print("\n=== РЕЗУЛЬТАТ ===")
        print(result)
        print("\nОтвет скопирован в буфер обмена!")
    else:
        print("Не удалось получить ответ. Проверьте настройки.")


# Альтернативный вариант без remote debugging
def simple_automation():
    """Простой вариант - открывает новое окно браузера"""
    driver = webdriver.Chrome()

    try:
        print("Переходим на Gemini...")
        driver.get("https://gemini.google.com/app")

        input("Авторизуйтесь вручную и нажмите Enter...")

        # Остальная логика аналогична
        wait = WebDriverWait(driver, 10)

        # Поиск поля ввода
        message_input = wait.until(EC.element_to_be_clickable((By.TAG_NAME, "textarea")))
        message_input.send_keys("Как дела")
        message_input.send_keys(Keys.RETURN)

        print("Ждем ответа...")
        time.sleep(5)

        # Здесь можно добавить логику поиска ответа

    finally:
        input("Нажмите Enter для закрытия браузера...")
        driver.quit()

# Раскомментируйте для простого варианта
# simple_automation()