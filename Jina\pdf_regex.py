import psycopg2
import numpy as np
import re
from typing import List, Dict, Any
from dotenv import load_dotenv
import os
from datetime import datetime

# Загрузка переменных окружения
load_dotenv()

PG_USER = os.getenv('PG_USER')
PG_PASSWORD = os.getenv('PG_PASSWORD')
PG_HOST_LOCAL = 'localhost'
PG_PORT = os.getenv('PG_PORT')
PG_DBNAME = os.getenv('PG_DBNAME')

class DocumentSearch:
    def __init__(self):
        self.db_config = {
            'host': PG_HOST_LOCAL,
            'database': PG_DBNAME,
            'user': PG_USER,
            'password': PG_PASSWORD,
            'port': PG_PORT,
            'client_encoding': 'utf-8'
        }
        
        try:
            self.conn = psycopg2.connect(**self.db_config)
            self.conn.set_client_encoding('UTF8')
        except Exception as e:
            print(f"Помилка підключення до бази даних: {e}")
            raise
        
    def safe_decode(self, text: any) -> str:
        """Безопасное декодирование текста"""
        if text is None:
            return ""
        if isinstance(text, str):
            return text
        if isinstance(text, bytes):
            try:
                for encoding in ['utf-8', 'cp1251', 'iso-8859-1', 'cp866']:
                    try:
                        return text.decode(encoding)
                    except UnicodeDecodeError:
                        continue
                return text.decode('utf-8', errors='replace')
            except:
                return str(text)
        return str(text)
    
    def extract_document_info(self, text: str):
        """Извлечение структурированной информации из текста документа"""
        info = {
            'type': self.detect_document_type(text),
            'number': self.extract_document_number(text),
            'date': self.extract_document_date(text),
            'client': self.extract_client_name(text)
        }
        
        return info
    
    def detect_document_type(self, text: str) -> str:
        """Определение типа документа"""
        text_lower = text.lower()
        
        type_patterns = [
            (r'товарно-транспортна\s+накладна|ттн', 'ТТН'),
            (r'заявка\s+на\s+повернення', 'Заявка на повернення'),
            (r'акт\s+повернення|акт\s+приймання-передачі', 'Акт повернення'),
            (r'рахунок-фактура|р\/ф|рф', 'Рахунок-фактура'),
            (r'накладна\s+на\s+повернення', 'Накладна на повернення'),
            (r'повернення\s+товарів', 'Акт повернення'),
            (r'накладна', 'Накладна'),
            (r'рахунок', 'Рахунок'),
            (r'акт', 'Акт'),
            (r'договір', 'Договір'),
            (r'протокол', 'Протокол'),
            (r'повідомлення', 'Повідомлення'),
            (r'звіт', 'Звіт'),
            (r'заявка', 'Заявка')
        ]
        
        for pattern, doc_type in type_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return doc_type
        
        return 'Документ'
    
    def extract_document_number(self, text: str) -> str:
        """Извлечение номера документа"""
        text_lower = text.lower()
        
        number_patterns = [
            r'накладна\s+№\s*([\w\-/]+)',
            r'№\s*документа[:]?\s*([\w\-/]+)',
            r'номер\s+документа[:]?\s*([\w\-/]+)',
            r'№\s*([\w\-/]+)\s+від',
            r'рахунок[-]?фактура\s+№\s*([\w\-/]+)',
            r'ттн\s+№\s*([\w\-/]+)',
            r'акт\s+№\s*([\w\-/]+)',
            r'заявка\s+№\s*([\w\-/]+)',
            r'№\s*([\w\-/]{3,})'  # Номера от 3 символов
        ]
        
        for pattern in number_patterns:
            match = re.search(pattern, text_lower, re.IGNORECASE)
            if match:
                number = match.group(1).strip()
                # Проверяем что это действительно номер, а не случайный текст
                if any(char.isdigit() for char in number) and len(number) >= 2:
                    return number
        
        # Дополнительный поиск номера в тексте
        number_matches = re.findall(r'№\s*(\w+[\-/\w]*)', text, re.IGNORECASE)
        for number in number_matches:
            if any(char.isdigit() for char in number) and len(number) >= 2:
                return number
        
        return 'Не вказано'
    
    def extract_document_date(self, text: str) -> str:
        """Извлечение даты документа"""
        date_patterns = [
            r'від\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{2,4})',
            r'дата[:]?\s+(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{2,4})',
            r'(\d{1,2}\s+(січня|лютого|березня|квітня|травня|червня|липня|серпня|вересня|жовтня|листопада|грудня)\s+\d{4})',
            r'(\d{1,2}[\.\/]\d{1,2}[\.\/]\d{2,4})'  # Общий паттерн для дат
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    date_str = match[0]
                else:
                    date_str = match
                
                # Проверяем что это похоже на дату
                if any(sep in date_str for sep in ['.', '/', '-']) or any(month in date_str.lower() for month in ['січня', 'лютого', 'березня', 'квітня', 'травня', 'червня', 'липня', 'серпня', 'вересня', 'жовтня', 'листопада', 'грудня']):
                    return date_str
        
        return 'Не вказано'
    
    def extract_client_name(self, text: str) -> str:
        """Извлечение имени клиента"""
        # Паттерны для поиска названий компаний
        company_patterns = [
            r'ТОВ\s+[«"]([^»"]+)[»"]',
            r'Товариство\s+[«"]([^»"]+)[»"]',
            r'ПП\s+[«"]([^»"]+)[»"]',
            r'ФОП\s+[«"]([^»"]+)[»"]',
            r'ПрАТ\s+[«"]([^»"]+)[»"]',
            r'([А-ЯЄІЇҐ]{2,}\s+[А-ЯЄІЇҐ]{2,}(?:\s+[А-ЯЄІЇҐ]{2,})*)'  # CAPS слова
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if len(match.strip()) > 3:  # Минимальная длина названия
                    return match.strip()
        
        return 'Не вказано'
    
    def search_documents(self, client: str = None, doc_type: str = None, period: str = None):
        """Поиск документов по заданным критериям"""
        try:
            with self.conn.cursor() as cursor:
                # Базовый запрос
                query = """
                    SELECT id, file_name, page_number, description, full_path 
                    FROM t_scan_documents_raw 
                    WHERE description IS NOT NULL AND description != ''
                    ORDER BY file_name, page_number
                """
                
                cursor.execute(query)
                documents = cursor.fetchall()
                
                results = []
                
                for doc in documents:
                    try:
                        doc_id = doc[0]
                        file_name = self.safe_decode(doc[1])
                        page_number = doc[2]
                        description = self.safe_decode(doc[3])
                        full_path = self.safe_decode(doc[4])
                        
                        # Извлекаем информацию из текста
                        doc_info = self.extract_document_info(description)
                        
                        # Проверяем критерии поиска
                        matches_client = True
                        matches_type = True
                        matches_period = True
                        
                        if client:
                            matches_client = client.lower() in description.lower() or client.lower() in doc_info['client'].lower()
                        
                        if doc_type:
                            matches_type = doc_type.lower() in doc_info['type'].lower()
                        
                        if period:
                            # Простая проверка периода в тексте
                            matches_period = period.lower() in description.lower() or period.lower() in doc_info['date'].lower()
                        
                        if matches_client and matches_type and matches_period:
                            results.append({
                                'id': doc_id,
                                'file_name': file_name,
                                'page_number': page_number,
                                'full_path': full_path,
                                'type': doc_info['type'],
                                'number': doc_info['number'],
                                'date': doc_info['date'],
                                'client': doc_info['client'],
                                'description': description[:300] + '...' if len(description) > 300 else description
                            })
                            
                    except Exception as e:
                        print(f"Помилка обробки документа ID {doc[0]}: {e}")
                        continue
                
                return results
                
        except Exception as e:
            print(f"Помилка пошуку документів: {e}")
            return []
    
    def close(self):
        """Закрытие соединения с базой данных"""
        if self.conn:
            self.conn.close()


def create_search_engine():
    try:
        return DocumentSearch()
    except Exception as e:
        print(f"Не вдалося створити пошуковий рушій: {e}")
        return None


def results_to_markdown_table(results):
    """Конвертация результатов в Markdown таблицу"""
    if not results:
        return "❌ Документів не знайдено"
    
    table = "| № | Тип | Номер | Дата документа | Клієнт | Найменування файлу | Сторінка |\n"
    table += "|---|-----|-------|----------------|--------|-------------------|----------|\n"
    
    for i, result in enumerate(results, 1):
        table += f"| {i} | {result['type']} | {result['number']} | {result['date']} | {result['client']} | {result['file_name']} | {result['page_number']} |\n"
    
    return table


def search_documents_interactive():
    """Интерактивный поиск документов"""
    search_engine = create_search_engine()
    
    if not search_engine:
        return
    
    try:
        print("🔍 Пошук документів")
        print("=" * 60)
        
        # Запрос параметров поиска
        client = input("Введіть назву клієнта (або Enter для пропуску): ").strip()
        doc_type = input("Введіть тип документа (або Enter для пропуску): ").strip()
        period = input("Введіть період (або Enter для пропуску): ").strip()
        
        print(f"\n📋 Пошук: Клієнт='{client or 'всі'}', Тип='{doc_type or 'всі'}', Період='{period or 'всі'}'")
        print("=" * 60)
        
        results = search_engine.search_documents(client, doc_type, period)
        
        if results:
            print(f"✅ Знайдено {len(results)} документів:\n")
            
            # Вывод в виде Markdown таблицы
            markdown_table = results_to_markdown_table(results)
            print(markdown_table)
            
            print("\n📋 Деталі знайдених документів:")
            print("=" * 80)
            for i, result in enumerate(results, 1):
                print(f"\n{i}. 📄 {result['type']} №{result['number']} від {result['date']}")
                print(f"   👤 Клієнт: {result['client']}")
                print(f"   📁 Файл: {result['file_name']}")
                print(f"   📖 Сторінка: {result['page_number']}")
                print(f"   📍 Шлях: {result['full_path']}")
                print(f"   📝 Уривок тексту: {result['description']}")
                
        else:
            print("❌ Документів не знайдено за вказаними критеріями.")
            
    except Exception as e:
        print(f"Помилка під час пошуку: {e}")
    finally:
        search_engine.close()


# Пример поиска для ЕПІЦЕНТР
def search_epicenter_example():
    """Пример поиска для ЕПІЦЕНТР"""
    search_engine = create_search_engine()
    
    if not search_engine:
        return
    
    try:
        print("🔍 Приклад пошуку для ЕПІЦЕНТР")
        print("=" * 60)
        
        results = search_engine.search_documents(
            client="ЕПІЦЕНТР",
            doc_type="Накладна на повернення",
            period="2024"
        )
        
        if results:
            print(f"✅ Знайдено {len(results)} документів:\n")
            
            markdown_table = results_to_markdown_table(results)
            print(markdown_table)
            
        else:
            print("❌ Документів не знайдено.")
            
    except Exception as e:
        print(f"Помилка під час пошуку: {e}")
    finally:
        search_engine.close()


if __name__ == "__main__":
    # Запуск интерактивного поиска
    # search_documents_interactive()
    
    # Или раскомментируйте для примера поиска ЕПІЦЕНТР
    search_epicenter_example()