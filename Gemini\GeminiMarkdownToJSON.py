# -*- coding: utf-8 -*-
# парсит текст в JSON с помощью Gemini API

import google.generativeai as genai
import json
import os
from dotenv import load_dotenv # Если используете .env файл
import time

# --- Загрузка переменных окружения ---
load_dotenv() # Эта строка загрузит переменные из файла .env в той же директории

# --- Настройки API ---
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY") # Обратите внимание, стандартное имя переменной GOOGLE_API_KEY
                                            # или GEMINI_API_KEY, как вы использовали ранее.
                                            # Убедитесь, что имя совпадает с тем, что в .env

if not GEMINI_API_KEY:
    # Если ключ не найден в .env, вы можете жестко задать его здесь для теста
    # (НЕ РЕКОМЕНДУЕТСЯ ДЛЯ ПРОДАШЕНА - НЕ ХРАНИТЕ КЛЮЧИ В КОДЕ)
    # GEMINI_API_KEY = "ВАШ_РЕАЛЬНЫЙ_API_КЛЮЧ"
    # raise ValueError("Необходимо установить переменную окружения GOOGLE_API_KEY или GEMINI_API_KEY.")
    print("ПРЕДУПРЕЖДЕНИЕ: API ключ не найден в переменных окружения. Попытка использовать ключ, если он будет передан в функцию, или ожидание ошибки.")


# Конфигурируем genai глобально, если ключ есть
if GEMINI_API_KEY:
    try:
        genai.configure(api_key=GEMINI_API_KEY)
        # print("INFO: Gemini API успешно сконфигурирован с API ключом.")
    except Exception as e:
        print(f"ERROR: Ошибка при конфигурации Gemini API: {e}")
        # Здесь можно решить, прерывать ли выполнение или позволить функции попытаться использовать явно переданный ключ
else:
    print("INFO: Глобальный API ключ для Gemini не сконфигурирован. Ключ должен быть передан в функцию get_json_from_text_with_gemini.")


MODEL_NAME = os.getenv("GEMINI_MODEL_2F")  # 'gemini-1.5-flash-latest' # Рекомендуется или 'gemini-2.0-flash'

# Настройте API ключ один раз при инициализации вашего приложения/скрипта
# genai.configure(api_key=GEMINI_API_KEY)


def get_json_from_text_with_gemini(input_text: str, api_key: str = None, max_retries: int = 3,
                                   initial_delay: int = 2) -> str:
    """
    Отправляет текст в Gemini API с инструкцией преобразовать его в JSON
    и пытается гарантировать возврат корректного JSON-ответа.

    Args:
        input_text (str): Входной текст для преобразования.
        api_key (str, optional): Ключ API Gemini. Если None, предполагается,
                                 что genai.configure() был вызван ранее.
        max_retries (int): Максимальное количество попыток в случае некорректного JSON.
        initial_delay (int): Начальная задержка перед повторной попыткой (в секундах).

    Returns:
        str: Строка, содержащая JSON, или строка с сообщением об ошибке,
             если не удалось получить корректный JSON после всех попыток.
             Может вернуть пустой JSON "{}" если входной текст не может быть осмысленно преобразован.
    """
    if api_key:
        genai.configure(api_key=api_key)  # Конфигурируем, если ключ передан явно

    model = genai.GenerativeModel(MODEL_NAME)

    # Промпт, явно указывающий на необходимость JSON и описывающий ожидаемую структуру (если известна)
    # Пример: если вы ожидаете структуру {"key1": "value1", "details": {"sub_key": "sub_value"}}
    # Вы можете добавить описание этой структуры в промпт.
    # Для общего случая, просто просим JSON.
    prompt_parts = [
        f"Преобразуй следующий текст в корректный JSON формат. Убедись, что ответ является валидным JSON. "
        f"Текст для преобразования:\n\n---\n{input_text}\n---\n\nВерни ТОЛЬКО JSON и ничего больше.",
    ]

    # Можно добавить более строгие инструкции, если есть типичные ошибки, например:
    # "Убедись, что все строки заключены в двойные кавычки. Ключи также должны быть в двойных кавычках. Конечные запятые недопустимы."

    for attempt in range(max_retries):
        try:
            # print(f"Попытка {attempt + 1}/{max_retries}...")
            response = model.generate_content(prompt_parts, request_options={'timeout': 120})

            if not response.parts or not response.text:
                # print("Ответ от Gemini пуст или не содержит текста.")
                if attempt < max_retries - 1:
                    time.sleep(initial_delay * (2 ** attempt))  # Экспоненциальная задержка
                    continue
                else:
                    return json.dumps(
                        {"error": "Gemini API did not return text content.", "details": "Empty response."})

            potential_json = response.text.strip()

            # Попытка найти JSON внутри ответа, если API добавляет лишний текст
            # Это очень упрощенный поиск, может потребовать улучшения
            if '{' in potential_json and '}' in potential_json:
                start_index = potential_json.find('{')
                end_index = potential_json.rfind('}')
                if start_index != -1 and end_index != -1 and end_index > start_index:
                    potential_json = potential_json[start_index: end_index + 1]

            # Проверка, является ли ответ валидным JSON
            json.loads(potential_json)  # Если здесь ошибка, перейдем в except
            return potential_json  # Возвращаем валидный JSON

        except json.JSONDecodeError as e:
            # print(f"JSONDecodeError на попытке {attempt + 1}: {e}")
            # print(f"Ответ от Gemini, который не удалось распарсить: <<<{potential_json}>>>")
            if attempt < max_retries - 1:
                time.sleep(initial_delay * (2 ** attempt))  # Экспоненциальная задержка
                # Можно добавить модификацию промпта для следующей попытки, например:
                # prompt_parts = [
                #    f"Предыдущая попытка вернуть JSON не удалась. Пожалуйста, будь особенно внимателен к синтаксису JSON. "
                #    f"Убедись, что все строки и ключи в двойных кавычках, нет висячих запятых. "
                #    f"Текст: \n{input_text}\nВерни ТОЛЬКО JSON."
                # ]
                continue
            else:
                error_message = (f"Failed to decode JSON after {max_retries} attempts. Last attempt error: {e}. "
                                 f"Last response: {potential_json[:500]}...")
                return json.dumps({"error": "JSONDecodeError", "details": error_message})
        except Exception as e:
            # print(f"Непредвиденная ошибка на попытке {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                time.sleep(initial_delay * (2 ** attempt))
                continue
            else:
                return json.dumps({"error": "Unexpected API error", "details": str(e)})

    return json.dumps({"error": "Failed to get a valid JSON response after all retries."})


if __name__ == '__main__':
    # --- Настройка API ключа для примера ---
    # Убедитесь, что GEMINI_API_KEY установлен в переменных окружения или передайте его явно
    # Например:
    # my_api_key = "ВАШ_API_КЛЮЧ"
    # genai.configure(api_key=my_api_key)
    # Либо, если он уже установлен в .env и load_dotenv() был вызван:

    # Пример 1: Простой текст
    sample_text_1 = "Имя: Иван, Возраст: 30, Город: Москва"
    print(f"\nОбработка текста 1: '{sample_text_1}'")
    json_output_1 = get_json_from_text_with_gemini(sample_text_1)
    print("Результат 1 (JSON строка):")
    print(json_output_1)
    try:
        parsed_json_1 = json.loads(json_output_1)
        print("Результат 1 (распарсенный JSON):")
        print(parsed_json_1)
    except json.JSONDecodeError:
        print("Не удалось распарсить результат 1 как JSON.")

    print("-" * 30)

    # Пример 2: Текст, который может вызвать проблемы
    sample_text_2 = """
    Информация о продукте:
    Наименование: 'СуперТовар X100'
    Описание: Это "лучший" товар на рынке с 'уникальными' свойствами.
    Цена: 99.99
    В наличии: Да
    """
    print(f"\nОбработка текста 2: '{sample_text_2[:100]}...'")  # Показываем часть для краткости
    json_output_2 = get_json_from_text_with_gemini(sample_text_2)
    print("Результат 2 (JSON строка):")
    print(json_output_2)
    try:
        parsed_json_2 = json.loads(json_output_2)
        print("Результат 2 (распарсенный JSON):")
        print(parsed_json_2)
    except json.JSONDecodeError:
        print("Не удалось распарсить результат 2 как JSON.")

    print("-" * 30)

    # Пример 3: Текст, который API может вернуть с дополнительными пояснениями
    sample_text_3 = "Событие: Конференция, Дата: 2024-12-15, Участники: [Алиса, Боб, Чарли]"
    # Для имитации некорректного ответа, мы не будем его вызывать, а предположим, что API вернул что-то вроде:
    # "Конечно, вот ваш JSON: {\"event\": \"Конференция\", ...} Надеюсь, это поможет!"
    # Функция попытается извлечь JSON из такой строки.
    # Но чтобы протестировать реальный вызов:
    print(f"\nОбработка текста 3: '{sample_text_3}'")
    json_output_3 = get_json_from_text_with_gemini(sample_text_3)
    print("Результат 3 (JSON строка):")
    print(json_output_3)
    try:
        parsed_json_3 = json.loads(json_output_3)
        print("Результат 3 (распарсенный JSON):")
        print(parsed_json_3)
    except json.JSONDecodeError:
        print("Не удалось распарсить результат 3 как JSON.")

    # Пример с передачей API ключа (если genai.configure не был вызван глобально)
    # my_explicit_api_key = os.getenv("GEMINI_API_KEY") # Получаем ключ
    # if my_explicit_api_key:
    #     print("\nОбработка с явно переданным ключом:")
    #     json_output_explicit_key = get_json_from_text_with_gemini(sample_text_1, api_key=my_explicit_api_key)
    #     print(json_output_explicit_key)
    # else:
    #     print("\nПропуск теста с явным ключом: GEMINI_API_KEY не найден.")