# -*- coding: utf-8 -*-
"""
Объединяет PDF файлы с совпадающими именами в одный файл. Налоговая накладная и ее корректировки.
https://chat.deepseek.com/a/chat/s/7251fc59-e89b-4c03-b21f-e9e2ee2e2711
"""

import os
import re
from PyPDF2 import PdfMerger

def merge_pdf_files(folder_path):
    """
    Рекурсивно объединяет PDF файлы с совпадающими именами во всех вложенных папках,
    добавляя файлы с KVT в конец основного файла в отсортированном порядке.
    """
    # Получаем все PDF файлы в папке и вложенных папках
    pdf_files = []
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    
    # Группируем файлы по базовому имени
    file_groups = {}
    
    for file_path in pdf_files:
        filename = os.path.basename(file_path)

        # Пропускаем уже объединенные файлы
        if "merge" in filename:
            continue

        # Определяем базовое имя (без KVT части)
        if " KVT" in filename:
            # Ищем базовое имя до " KVT"
            base_name = filename.split(" KVT")[0]
            # Извлекаем номер KVT
            kvt_match = re.search(r"KVT(\d+)", filename)
            kvt_number = int(kvt_match.group(1)) if kvt_match else 0
            file_type = "kvt"
        elif "merge" not in filename:
            base_name = filename.replace(".pdf", "")
            kvt_number = 0
            file_type = "main"
        
        # Добавляем файл в соответствующую группу
        if base_name not in file_groups:
            file_groups[base_name] = {
                "main_file": None,
                "kvt_files": []
            }
        
        if file_type == "main":
            file_groups[base_name]["main_file"] = {
                "path": file_path,
                "filename": filename
            }
        else:
            file_groups[base_name]["kvt_files"].append({
                "path": file_path,
                "filename": filename,
                "kvt_number": kvt_number
            })
    
    # Обрабатываем каждую группу файлов
    for base_name, files in file_groups.items():
        main_file = files["main_file"]
        kvt_files = files["kvt_files"]
        
        # Проверяем, есть ли основной файл и KVT файлы для объединения
        if main_file and kvt_files:
            print(f"Обработка группы: {base_name}")
            print(f"Основной файл: {main_file['filename']}")
            
            # Сортируем KVT файлы по номеру
            kvt_files_sorted = sorted(kvt_files, key=lambda x: x["kvt_number"])
            
            print("KVT файлы в порядке объединения:")
            for kvt_file in kvt_files_sorted:
                print(f"  - {kvt_file['filename']} (KVT{kvt_file['kvt_number']})")
            
            # Создаем объект для объединения PDF
            merger = PdfMerger()
            
            # Добавляем основной файл
            merger.append(main_file["path"])
            
            # Добавляем KVT файлы в отсортированном порядке
            for kvt_file in kvt_files_sorted:
                merger.append(kvt_file["path"])
            
            # Формируем имя для объединенного файла
            output_filename = f"{base_name} merge.pdf"
            output_path = os.path.join(os.path.dirname(main_file["path"]), output_filename)
            
            # Сохраняем объединенный файл
            with open(output_path, "wb") as output_file:
                merger.write(output_file)
            
            print(f"Объединенный файл создан: {output_filename}")
            print("-" * 50)
            
            # Закрываем merger
            merger.close()
        else:
            if not main_file:
                print(f"Пропускаем группу {base_name}: отсутствует основной файл")
            if not kvt_files:
                print(f"Пропускаем группу {base_name}: отсутствуют KVT файлы")

def main():
    # Укажите путь к папке с PDF файлами
    pdf_path = r"C:\Users\<USER>\Desktop\Разблокировка\Block_NN_RK"
    folder_path = pdf_path.strip()
    
    # Проверяем существование папки
    if not os.path.exists(folder_path):
        print("Указанная папка не существует!")
        return
    
    if not os.path.isdir(folder_path):
        print("Указанный путь не является папкой!")
        return
    
    # Запускаем процесс объединения
    merge_pdf_files(folder_path)
    print("Обработка завершена!")

if __name__ == "__main__":
    main()
