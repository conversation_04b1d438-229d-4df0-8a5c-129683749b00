# -*- coding: utf-8 -*-
"""
Vchasno.py

Скачивает файлы из Вчасно.ua
https://edo.vchasno.ua/app/
Этот скрипт автоматизирует процесс авторизации, скачивания реестра документов через правильную кнопку и форму,
а затем проходит по ссылкам в отчете и скачивает каждый документ в формате PDF.

*** Финальная, исправленная версия с корректными селекторами для полей дат ***
"""

import os # Для работы с файловой системой
from pydoc import doc
import time # Для работы со временем
import re # Для работы с регулярными выражениями
from datetime import date, datetime, timedelta # Для работы с датами
from dotenv import load_dotenv # Для загрузки переменных окружения из файла .env
import pandas as pd # Для работы с данными в формате CSV
import fitz  # Для проверки целостности PDF (PyMuPDF)
import shutil # Для работы с файлами и папками
from dateutil.parser import parse
from selenium import webdriver # Для управления браузером Chrome
from selenium.webdriver.chrome.options import Options # Для настройки браузера Chrome
from selenium.webdriver.common.by import By # Для поиска элементов на странице
from selenium.webdriver.common.keys import Keys # Для ввода текста
from selenium.webdriver.support.ui import WebDriverWait # Для ожидания элементов
from selenium.webdriver.support import expected_conditions as EC # Для ожидания условий
from selenium.common.exceptions import TimeoutException # Для обработки таймаутов
from selenium.webdriver.common.action_chains import ActionChains # Для имитации человеческого ввода

# --- Глобальные константы и настройка ---
load_dotenv()

LOGIN_EMAIL = os.getenv("VCHASNO_LOGIN_EMAIL")
LOGIN_PASSWORD = os.getenv("VCHASNO_LOGIN_PASSWORD")

START_URL = "https://edo.vchasno.ua/auth/start"
LOGIN_URL = "https://edo.vchasno.ua/auth/login"
SUCCESS_URL_PART = "/app/"

# Временная папка для скачивания временных ]файлов
_current_folder = time.strftime("%Y-%m-%d_%H-%M-%S")
DOWNLOAD_DIR = os.path.join(os.getcwd(), "downloads", _current_folder)
os.makedirs(DOWNLOAD_DIR, exist_ok=True)

# Папка для финальных файлов
SAVE_DIR = os.path.join(os.getcwd(), "downloads")
os.makedirs(SAVE_DIR, exist_ok=True)


def is_pdf_valid(filepath):
    """Проверяет, является ли файл действительным и неповрежденным PDF."""
    if not os.path.exists(filepath) or os.path.getsize(filepath) < 1024:
        print(f"    -> ПРОВЕРКА НЕ ПРОЙДЕНА: Файл '{os.path.basename(filepath)}' не существует или слишком мал.")
        return False
    try:
        doc = fitz.open(filepath)
        is_valid = doc.page_count > 0
        doc.close()
        if not is_valid:
            print(f"    -> ПРОВЕРКА НЕ ПРОЙДЕНА: Файл '{os.path.basename(filepath)}' пуст (0 страниц).")
        return is_valid
    except Exception as e:
        print(f"    -> ПРОВЕРКА НЕ ПРОЙДЕНА: Ошибка при открытии '{os.path.basename(filepath)}': {e}.")
        return False


def move_file(path_in: str):
    """
    Надежно перемещает файл из одного места в другое.

    Эта функция сначала проверяет все условия, создает папку назначения,
    если необходимо, и обрабатывает возможные ошибки.

    Args:
        path_out (str): Полный путь к исходному файлу, который нужно переместить.
                        (Source path)
        path_in (str):  Полный путь к месту назначения, включая новое имя файла.
                        (Destination path)
    """
    print(f"--- Попытка перемещения файла ---")
    print(f" -> В:  {path_in}")
    filename = os.path.basename(path_in)
    
    # Папка для скачанных файлов у нас по умолчанию DOWNLOAD_DIR
    # Передаем rename_downloaded_file только наименования файла, в которое будем переименован файл в DOWNLOAD_DIR
    # В ответе получаем полный путь к файлу в DOWNLOAD_DIR.
    path_out = rename_downloaded_file(filename)
    
    # Проверка: является ли файл действительным и неповрежденным PDF
    if not is_pdf_valid(path_out):
        print(f" -> PDF '{path_out}' ПОВРЕЖДЕН. ОТКЛОНЯЕМ.")
        return None

    # 1. Проверка: существует ли исходный файл?   
    if not os.path.isfile(str(path_out)):
        print(f" [X] ОШИБКА: Указанный путь не является файлом: {path_out}")
        return None

    try:
        # 2. Создание папки назначения, если она не существует
        destination_folder = os.path.dirname(path_in)
        if not os.path.exists(destination_folder):
            os.makedirs(destination_folder)
            print(f" -> Создана папка назначения: {destination_folder}")

        # 3. Удаление файла в месте назначения, если он уже существует
        if os.path.exists(path_in):
            print(f" -> В месте назначения уже существует файл. Удаляем его: {path_in}")
            os.remove(path_in)

        # 4. Перемещение файла
        shutil.move(str(path_out), path_in)
        
        print(f" [V] УСПЕХ: Файл успешно перемещен.")
        return path_in
    except FileExistsError as e:
        print(f" [X] ОШИБКА: Файл в месте назначения уже существует: {e}")
    except FileNotFoundError as e:
        print(f" [X] ОШИБКА: Исходный файл не найден: {e}")
    except Exception as e:
        print(f" [X] ОШИБКА: Произошла непредвиденная ошибка при перемещении файла: {e}")

    return None


def safe_driver_operation(operation_func, max_retries=3, delay=2):
    """Безопасно выполняет операцию с WebDriver с повторными попытками."""
    for attempt in range(max_retries):
        try:
            return operation_func()
        except TimeoutException as e:
            print(f"[!] Таймаут при попытке {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay * (attempt + 1))  # Увеличиваем задержку с каждой попыткой
            else:
                raise
        except Exception as e:
            print(f"[!] Ошибка при попытке {attempt + 1}/{max_retries}: {e}")
            if attempt < max_retries - 1:
                time.sleep(delay)
            else:
                raise


def human_like_type(element, text):
    """Имитирует человеческий ввод: клик, очистка, печать по одному символу."""
    print(f"    -> Имитируем человеческий ввод для значения '{text}'...")
    try:
        element.click(); time.sleep(0.3)
        element.send_keys(Keys.CONTROL + "a") # Выделить все
        element.send_keys(Keys.DELETE) # Удалить
        time.sleep(0.3)
        for char in text:
            element.send_keys(char)
            time.sleep(0.05)
        print(" -> Значение успешно напечатано.")
    except Exception as e:
        raise Exception(f"Ошибка при 'человеческом' вводе: {e}")


def sanitize_filename(filename):
    """Очищает имя файла от недопустимых символов."""
    return re.sub(r'[\\/*?:"<>|]', "_", str(filename))


def configure_driver():
    """Настраивает и возвращает экземпляр Chrome WebDriver."""
    print("Конфигурируем браузер для АВТОМАТИЧЕСКОГО скачивания...")
    chrome_options = Options()
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-save-password-bubble")
    chrome_options.add_argument("--disable-password-manager-reauthentication")

    # Добавляем настройки для стабильности соединения
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-images")  # Отключаем загрузку изображений для ускорения
    # НЕ отключаем JavaScript, так как сайт его использует
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--remote-debugging-port=0")  # Используем случайный порт

    # Дополнительные настройки для стабильности
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-renderer-backgrounding")

    prefs = {
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,  # Не показывать диалог сохранения
        "download.default_directory": DOWNLOAD_DIR,
        "safebrowsing.enabled": True,  # Папка для скачивания
        "credentials_enable_service": False,
        "profile.password_manager_enabled": False, # Не запоминать пароли
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,  # Не показывать всплывающие окна
    }
    chrome_options.add_experimental_option("prefs", prefs)

    try:
        # Создаем драйвер с увеличенными таймаутами
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(60)  # Таймаут загрузки страницы
        driver.implicitly_wait(10)  # Неявное ожидание элементов
        driver.maximize_window()
        return driver
    except Exception as e:
        print(f"Ошибка при создании WebDriver: {e}")
        raise


def authorize(driver, wait):
    try:
        """Выполняет авторизацию на сайте."""
        print("Шаг 1: Открываем стартовую страницу...")
        driver.get(START_URL)
        print(" -> Стартовая страница открыта.")

        print("Шаг 2: Вводим Email...")
        email_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login input")))
        email_input.send_keys(LOGIN_EMAIL)
        print(" -> Email введен.")

        print("Шаг 3: Нажимаем кнопку 'Продовжити'...")
        continue_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#login > div > div > button")))
        driver.execute_script("arguments[0].click();", continue_button)
        print(" -> Кнопка 'Продовжити' нажата.")
        wait.until(EC.url_to_be(LOGIN_URL))
        print(" -> Переход на страницу авторизации произошел.")

        print("Шаг 4: Вводим Пароль...")
        password_input = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "#password")))
        password_input.send_keys(LOGIN_PASSWORD)
        print(" -> Пароль введен.")

        print("Шаг 5: Нажимаем кнопку 'Увійти'...")
        submit_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit']")))
        driver.execute_script("arguments[0].click();", submit_button)
        print(" -> Кнопка 'Увійти' нажата.")

        wait.until(EC.url_contains(SUCCESS_URL_PART))
        print(" -> Авторизация прошла успешно.")
        return True
    except Exception as e:
        print(f"Ошибка при авторизации: {e}")
    
    return False


def clear_folder(folder_path):
    """Удаляет все файлы в указанной папке."""
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"Ошибка при удалении файла {file_path}: {e}")
            

# скачиваем журнал документов в формате csv из edo
def download_register_edo(driver, wait):
    try:
        # --- СКАЧИВАНИЕ РЕЕСТРА ЧЕРЕЗ ПРАВИЛЬНУЮ КНОПКУ И ФОРМУ ---
        print("\nШаг 4: Ищем и нажимаем кнопку 'Завантажити реєстр документів за період'...")
        button_xpath = "//button[@aria-label='Завантажити реєстр документів за період']"
        register_button = wait.until(EC.element_to_be_clickable((By.XPATH, button_xpath)))
        driver.execute_script("arguments[0].click();", register_button)
        print(" -> Кнопка нажата, ждем появления окна...")

        # Ждем, пока появится заголовок окна
        wait.until(EC.visibility_of_element_located((By.XPATH, "//*[text()='Завантажити реєстр документів']")))
        print(" -> Окно для ввода дат появилось.")
        
        print("\nШаг 5: Заполняем форму экспорта...")
        start_date_obj = date.today() - timedelta(days=364)
        end_date_obj = date.today()
        start_date_str = start_date_obj.strftime('%d.%m.%Y')
        end_date_str = end_date_obj.strftime('%d.%m.%Y')

        # *** ИСПРАВЛЕННЫЙ И НАДЕЖНЫЙ ЛОКАТОР ***
        # Ищем input, который находится внутри элемента-обертки с текстом "Дата з"
        start_date_input_xpath = "//span[contains(@class, 'vchasno-ui-date-picker__wrapper')][.//span[contains(text(), 'Дата з')]]//input"
        start_date_input = wait.until(EC.presence_of_element_located((By.XPATH, start_date_input_xpath)))
        human_like_type(start_date_input, start_date_str)
        start_date_input.send_keys(Keys.ENTER)

        # *** ИСПРАВЛЕННЫЙ И НАДЕЖНЫЙ ЛОКАТОР ***
        # Ищем input, который находится внутри элемента-обертки с текстом "Дата по"
        end_date_input_xpath = "//span[contains(@class, 'vchasno-ui-date-picker__wrapper')][.//span[contains(text(), 'Дата по')]]//input"
        end_date_input = wait.until(EC.presence_of_element_located((By.XPATH, end_date_input_xpath)))
        human_like_type(end_date_input, end_date_str)
        end_date_input.send_keys(Keys.ENTER)
        
        print("\nШаг 6: Нажимаем кнопку 'Зберегти' для скачивания реестра...")
        
        clear_folder(DOWNLOAD_DIR)
        
        csv_filename_edo = f"vchasno_register_edo_{start_date_obj.strftime('%Y-%m-%d')}_{end_date_obj.strftime('%Y-%m-%d')}.csv"
        # Ищем кнопку по точному тексту "Зберегти"
        save_button_xpath = "//button[text()='Зберегти']"
        
        print("Ищем кнопку 'Зберегти'...")
        
        # Ждем, пока кнопка станет кликабельной
        save_button = wait.until(
            EC.element_to_be_clickable((By.XPATH, save_button_xpath))
        )
        
        print("Кнопка найдена, нажимаем...")
        save_button.click()    
        return csv_filename_edo
    
    except Exception as e:
        print(f"Ошибка при скачивании реестра: {e}")
    
    return None


def download_register_edi(driver, wait):
    try:
        driver.get('https://edi.vchasno.ua/app/deals')
        
        # Ищем div, у которого атрибут class СОДЕРЖИТ 'DocumentsExportButton__icon'
        button_selector = "div[class*='DocumentsExportButton__icon']"
        
        print("Ищем кнопку экспорта по частичному совпадению класса...")
        
        # Ждем, пока кнопка станет кликабельной
        export_button = wait.until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, button_selector))
        )
        
        print("Кнопка найдена, нажимаем...")
        export_button.click()
        
        print("[V] Кнопка экспорта успешно нажата.")

        # 1. Вычисляем и форматируем нужную дату
        start_date_obj = (date.today() - timedelta(days=364)).strftime('%d.%m.%Y')
        print(f"Целевая дата для установки: {start_date_obj}")

        # 2. Создаем самый надежный XPath-локатор.
        # Он ищет <label> с нужным data-qa, а затем находит <input> ВНУТРИ него.
        date_input_xpath = "//label[@data-qa='set_export_document_date_start']//input[@type='text']"
        
        print("Ищем поле для ввода даты по data-qa атрибуту...")
        
        # 3. Ждем появления поля и передаем его в вашу функцию
        date_output_element = wait.until(
            EC.presence_of_element_located((By.XPATH, date_input_xpath))
        )
        
        print("Поле найдено. Вызываем human_like_type для ввода даты...")
        human_like_type(date_output_element, start_date_obj)
        
        # 4. Нажимаем Enter, чтобы календарь закрылся
        date_output_element.send_keys(Keys.ENTER)
        
        print("[V] Дата начала периода успешно установлена.")
        
        # *******************
        # 1. Вычисляем и форматируем нужную дату
        end_date_obj = date.today().strftime('%d.%m.%Y')
        print(f"Целевая дата для установки: {end_date_obj}")

        # 2. Создаем самый надежный XPath-локатор.
        # Он ищет <label> с нужным data-qa, а затем находит <input> ВНУТРИ него.
        date_ouptput_xpath = "//label[@data-qa='set_export_document_date_end']//input[@type='text']"
        
        print("Ищем поле для ввода даты по data-qa атрибуту...")
        
        # 3. Ждем появления поля и передаем его в вашу функцию
        date_input_element = wait.until(
            EC.presence_of_element_located((By.XPATH, date_ouptput_xpath))
        )
        
        print("Поле найдено. Вызываем human_like_type для ввода даты...")
        human_like_type(date_input_element, end_date_obj)
        
        # 4. Нажимаем Enter, чтобы календарь закрылся
        date_input_element.send_keys(Keys.ENTER)
        
        print("[V] Дата начала периода успешно установлена.")        
        
        # *****************************
        # Ищем кнопку по ее точному тексту. Замените 'Зберегти' на реальный текст кнопки.
        # normalize-space() помогает, если вокруг текста есть лишние пробелы.
        button_xpath = '//*[@id="content"]/div/header/div/div[2]/div[1]/div[5]/div/div[2]/div[2]/form/div/button'  # "//button[normalize-space()='Зберегти']" 
        
        print("Ищем кнопку по ее тексту...")
        
        # Ждем, пока кнопка станет кликабельной
        button = wait.until(
            EC.element_to_be_clickable((By.XPATH, button_xpath))
        )
        
        print("Кнопка найдена, нажимаем...")
        button.click()
        
        csv_filename_edo = f"vchasno_register_edi_{start_date_obj}_{end_date_obj}.csv"
        return csv_filename_edo

    except Exception as e:
        print(f"[X] Не удалось найти или нажать на кнопку экспорта: {e}")
    
    return None


def rename_downloaded_file(new_file_name_or_path):
    # Определяем скачанный файл и переименовываем его
    try:
        max_wait_time = 90
        wait_start_time = time.time()
        downloaded_filename = None
        only_file_name = os.path.basename(new_file_name_or_path)
        new_file_name = sanitize_filename(only_file_name)
        
        while time.time() - wait_start_time < max_wait_time:
            files = set(os.listdir(DOWNLOAD_DIR))
            for filename in files:
                if not filename.endswith('.crdownload'): 
                    downloaded_filename = filename
                    break
            if downloaded_filename: 
                break
            time.sleep(1)
        
        if not downloaded_filename: 
            raise Exception(f"Файл реестра не был скачан в течение {max_wait_time} секунд.")
        
        print(f"\nШаг 7: Переименование скачанного файла реестра '{downloaded_filename}'...")
        downloaded_file_path = os.path.join(DOWNLOAD_DIR, downloaded_filename)       
        csv_file_path = os.path.join(DOWNLOAD_DIR, new_file_name)
        if os.path.exists(csv_file_path): 
            os.remove(csv_file_path)
        os.rename(downloaded_file_path, csv_file_path)
        print(f" -> Файл успешно переименован в '{csv_file_path}'")        
        return csv_file_path
    
    except Exception as e:
        print(f"Ошибка при ожидании скачивания реестра: {e}")
        return None
    

def main():
    """Главная функция, выполняющая весь процесс скачивания."""
    driver = None
    try:
        driver = configure_driver()
        wait = WebDriverWait(driver, 30)  # Увеличиваем таймаут до 30 секунд

        if not authorize(driver, wait):
            raise Exception("Авторизация не удалась")

        csv_filename_edi = download_register_edi(driver, wait)
        
        csv_filename_edo = download_register_edo(driver, wait)
        if not csv_filename_edo:
            raise Exception("Скачивание реестра не удалось")
        
        # перемещаем скачанный файл в папку SAVE_DIR
        path_in = os.path.join(SAVE_DIR, csv_filename_edo)
        
        path_out = move_file(path_in)
        if not path_out:
            raise Exception("Перемещение файла реестра не удалось")
                
        print("\nШаг 8: Загружаем данные из реестра в pandas DataFrame...")
        try:
            df = pd.read_csv(path_out, sep=';', on_bad_lines='skip', engine='python', encoding='utf-8')
        except UnicodeDecodeError:
            try:
                df = pd.read_csv(path_out, sep=';', on_bad_lines='skip', engine='python', encoding='cp1251')
                print(" -> Использована кодировка cp1251")
            except Exception as e:
                print(f" -> Не удалось прочитать как CSV ({e}). Пробуем как Excel...")
                df = pd.read_excel(path_out)
        print(" -> Данные из реестра успешно загружены.")
        
        # --- СКАЧИВАНИЕ PDF ДОКУМЕНТОВ (Шаг 9) ---
        print("\nШаг 9: Начинаем скачивание PDF документов по ссылкам...")
        
        if df.empty:
            print(" -> DataFrame пуст, скачивать нечего.")
        else:
            df.fillna('', inplace=True) # Заполняем пустые значения пустой строкой, чтобы не было ошибок при преобразовании в int
            total_docs = len(df)
            print(f" -> Всего документов для скачивания: {total_docs}")

            for i, (_, row) in enumerate(df.iterrows()):
                time.sleep(2)  # уже есть дальше
                # if i >= 10: print(f" -> Остановка после {i} документов для анализа результатов"); break

                doc_url = row.get('Посилання на документ')
                doc_type = sanitize_filename(row.get('Тип документа', 'Інший'))
                doc_number = sanitize_filename(row.get('Номер документа'))
                statuse_code = row.get('Код статусу підписання',0)
                if statuse_code and int(statuse_code) != 7008:  # 7008 - Підписаний всіма
                    # print(f"\n [!] Пропускаем строку {i+1}: документ не подписан всеми. Код статуса: {statuse_code}")
                    continue
                doc_date = row.get('Дата формування', 'Дата завантаження')
                if doc_date:
                    doc_date  = parse(doc_date, dayfirst=True)
                    doc_date_formatted = doc_date.strftime('%d %m %Y') if isinstance(doc_date, datetime) else 'UNKNOWN_DATE'
                contractor_code = sanitize_filename(row.get('ЄДРПОУ/ІПН контрагента', 'Unknown'))
                contractor_dir = os.path.join(SAVE_DIR, contractor_code, doc_type)
                os.makedirs(contractor_dir, exist_ok=True)
                filename = f"{doc_type} {doc_number} {doc_date_formatted}.pdf"
                final_pdf_path = os.path.join(contractor_dir, filename)
                bad_filename = f"BAD_{filename}"
                bad_filepath = os.path.join(contractor_dir, bad_filename)

                if not isinstance(doc_url, str) or not doc_url.startswith('http'):
                    print(f"\n [!] Пропускаем строку {i+1}: неверная ссылка '{doc_url}'"); continue
                if os.path.exists(final_pdf_path):
                    if filename =='Інший GM00-002506 31 07 2024.pdf':
                        print("Stop")
                    print(f"\n -> Файл '{filename}' уже существует. Пропускаем."); continue
                if os.path.exists(bad_filepath):
                    print(f"\n -> Поврежденный файл '{bad_filename}' уже существует. Пропускаем."); continue
                
                print(f"\n=== ОБРАБОТКА ДОКУМЕНТА {i+1}/{min(10, total_docs)}: '{filename}' ===")
                download_successful = False
                last_downloaded_file_path = None

                print(f" -> Переходим по ссылке: ...{doc_url[-36:]}")
                try:
                    driver.get(doc_url)
                except Exception as e:
                    print(f"[X] Ошибка при переходе по ссылке: {e}")
                    continue

                for attempt in range(3):
                    # time.sleep(2)
                    print(f"    -> Попытка {attempt + 1}/3...")

                    try:
                        # Обновляем страницу с обработкой таймаута
                        try:
                            driver.refresh()
                        except Exception as refresh_error:
                            print(f"[!] Ошибка при обновлении страницы: {refresh_error}")
                            time.sleep(5)  # Ждем дольше при ошибке
                            continue

                        # --- ЭТАП 1: Нажатие на иконку скачивания ---

                        # Самый надежный локатор: ищем по началу атрибута data-qa
                        download_icon_xpath = "//div[starts-with(@data-qa, 'qa_icon_download_')]"

                        print("Ищем иконку скачивания по data-qa атрибуту...")

                        # Ждем, пока иконка станет кликабельной с обработкой таймаута
                        try:
                            download_icon = wait.until(
                                EC.element_to_be_clickable((By.XPATH, download_icon_xpath))
                            )
                        except TimeoutException:
                            print("[!] Таймаут при поиске иконки скачивания")
                            continue
                        
                        print("Иконка найдена, нажимаем, чтобы открыть меню...")
                        # Используем JavaScript-клик, он часто надежнее обычного
                        try:
                            driver.execute_script("arguments[0].click();", download_icon)
                        except Exception as click_error:
                            print(f"[!] Ошибка при клике на иконку: {click_error}")
                            continue

                        # --- ЭТАП 2: Нажатие на пункт "Завантажити PDF" в меню ---

                        # Этот XPath ищет span, который содержит ОБА текста: "Завантажити" И "PDF".
                        # Точка (.) в contains(., 'текст') означает поиск по всему текстовому содержимому элемента.
                        pdf_option_xpath = "//span[contains(@class, 'DownloadDocumentButton__action') and contains(., 'Завантажити') and contains(., 'PDF')]"

                        print("Ищем пункт меню, содержащий 'Завантажити' и 'PDF'...")

                        # Ждем, пока элемент станет кликабельным с обработкой таймаута
                        try:
                            pdf_option = wait.until(
                                EC.element_to_be_clickable((By.XPATH, pdf_option_xpath))
                            )
                        except TimeoutException:
                            print("[!] Таймаут при поиске пункта меню PDF")
                            continue

                        print("Пункт меню найден, нажимаем...")
                        # Используем JavaScript-клик для максимальной надежности
                        try:
                            driver.execute_script("arguments[0].click();", pdf_option)
                            move_file(final_pdf_path)
                                
                            download_successful = True
                            break
                        except Exception as pdf_click_error:
                            print(f"[!] Ошибка при клике на PDF опцию: {pdf_click_error}")
                            continue

                    except TimeoutException as timeout_error:
                        print(f"[X] Таймаут при выполнении операции: {timeout_error}")
                        time.sleep(5)  # Дополнительная пауза при таймауте
                    except Exception as e:
                        print(f"[X] Не удалось выполнить двухэтапный клик: {e}")
                        time.sleep(2)  # Короткая пауза при других ошибках

                if download_successful:
                    print(f"[V] УСПЕХ: Документ '{filename}' успешно скачан и проверен.")
                else:
                    print(f"[X] НЕУДАЧА: Не удалось скачать корректный файл '{filename}' после 3 попыток.")
                    if last_downloaded_file_path and os.path.exists(last_downloaded_file_path):
                        print(f" -> Последний скачанный файл был поврежден. Переименовываем в '{bad_filename}'")
                        os.rename(last_downloaded_file_path, bad_filepath)
                

        print("\n[V] Скрипт успешно завершен. Браузер останется открытым.")
    except TimeoutException as timeout_error:
        print(f"\n[X] ОШИБКА ТАЙМАУТА: {timeout_error}")
        print(" -> Возможно, проблема с соединением или медленная загрузка страницы.")
        print(" -> Попробуйте запустить скрипт еще раз или проверьте интернет-соединение.")
    except Exception as e:
        print(f"\n[X] ГЛОБАЛЬНАЯ ОШИБКА: {e}")
        print(" -> Браузер останется открытым для анализа ошибки.")
    finally:
        # input("\nНажмите Enter в этой консоли, чтобы закрыть браузер...")
        if driver:
            try:
                driver.quit()
                print("Браузер закрыт.")
            except Exception as quit_error:
                print(f"Ошибка при закрытии браузера: {quit_error}")

# --- Точка входа в программу ---
if __name__ == "__main__":
    main()