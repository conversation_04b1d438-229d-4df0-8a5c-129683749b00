import asyncio
import os
import json
from concurrent.futures import ThreadPoolExecutor
from dotenv import load_dotenv
from google import genai
from google.genai import types
from prompt import PROMPT_EXAMPLE_GEMINI
load_dotenv()

GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
client = genai.Client(api_key=GEMINI_API_KEY)


def clear_text(json_string)->json:
    # Пытаемся распарсить JSON, если json_string вернул строку
    if isinstance(json_string, str):
        import json
        if not json_string: 
            return {}
        
        json_string = json_string.strip()
        try:

            # Обрабатываем различные форматы JSON
            if json_string.startswith('```json'):
                json_string = json_string[7:]
            if json_string.endswith('```'):
                json_string = json_string[:-3]
            if json_string.startswith('"'):
                json_string = json_string[1:]
            if json_string.endswith('"'):
                json_string = json_string[:-1]

            # Преобразуем строку JSON в словарь Python
            extract_data = json.loads(json_string)
            print(extract_data)
            return extract_data
        except json.JSONDecodeError as e:
            print(f"Не удалось распарсить JSON из extract_info: {e}")
    return json_string


def extract_data_by_gemini_sync(contents: str, prompt=PROMPT_EXAMPLE_GEMINI):
    full_prompt = f"{prompt}\n\nТекст для обработки:\n{contents}"
    model = "gemini-2.5-flash"  # "gemini-2.5-flash-preview-05-20"
    response = client.models.generate_content(
        model=model,
        contents=full_prompt,
        config=types.GenerateContentConfig(
            response_mime_type="application/json",
            thinking_config=types.ThinkingConfig(thinking_budget=0)
        )
    )
    if response.usage_metadata:
        print(f'prompt_token_count: {response.usage_metadata.prompt_token_count}')
        print(f'candidates_token_count: {response.usage_metadata.candidates_token_count}')
        print(f"total_token_count: {response.usage_metadata.total_token_count}")
        print(f"thoughts_token_count: {response.usage_metadata.thoughts_token_count}")
    return response.text


# Пример асинхронной функции
async def extract_data_by_gemini_async(contents: str, prompt=PROMPT_EXAMPLE_GEMINI):
    loop = asyncio.get_running_loop()
    with ThreadPoolExecutor() as pool:
        return await loop.run_in_executor(pool, extract_data_by_gemini_sync, contents, prompt)

if __name__ == "__main__":
    # response = extract_data_by_gemini("что больше 9.11 или 9.8?")
    response = asyncio.run(extract_data_by_gemini_async("что больше 9.11 или 9.8?", ''))
    print(response)