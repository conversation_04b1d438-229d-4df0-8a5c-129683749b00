from openai import OpenAI
from dotenv import load_dotenv
import os


load_dotenv()
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')

client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

# Round 1
messages = [{"role": "user", "content": "9.11 и 9.8, что больше?"}]
response = client.chat.completions.create(model = "deepseek-reasoner", messages = messages)

reasoning_content = response.choices[0].message.reasoning_content  # reasoning_content - чем обосновывает
print(f"reasoning_content: {reasoning_content}")
content = response.choices[0].message.content
print(f"content: {content}")

print("*" * 50)
# Round 2
messages.append({'role': 'assistant', 'content': content})
messages.append({'role': 'user', 'content': "2*55"})
response = client.chat.completions.create( model = "deepseek-chat", messages = messages)
content = response.choices[0].message.content
print(f"reasoning_content: {reasoning_content}")
print(f"content: {content}")
